#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DR项目名称清单处理程序
功能：
1. 去除空格
2. 中文括号（）替换成英文括号()
3. 新增三列：人群、疾病、平急诊
4. 识别"部位"和"摆位"中的"儿童"，填入人群列
"""

import pandas as pd
import re
from datetime import datetime

class DRSheetProcessor:
    """DR项目名称清单处理器"""
    
    def __init__(self, excel_file_path):
        self.excel_file_path = excel_file_path
        self.df_dr = None
        
    def load_data(self):
        """加载DR项目名称清单数据"""
        print("🔄 正在加载DR项目名称清单...")
        try:
            self.df_dr = pd.read_excel(self.excel_file_path, sheet_name='DR项目名称清单')
            print(f"✅ DR项目名称清单: {self.df_dr.shape[0]} 行 x {self.df_dr.shape[1]} 列")
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise
    
    def clean_spaces(self, text):
        """去除空格"""
        if pd.isna(text):
            return text
            
        text = str(text).strip()
        # 去除所有空格
        text = re.sub(r'\s+', '', text)
        
        return text
    
    def convert_chinese_to_english_brackets(self, text):
        """将中文括号（）替换成英文括号()"""
        if pd.isna(text):
            return text
            
        text = str(text)
        # 将中文括号转换为英文括号
        text = text.replace('（', '(').replace('）', ')')
        
        return text
    
    def remove_dr_from_pose(self, text):
        """从摆位字段中移除DR字符"""
        if pd.isna(text):
            return text
            
        text = str(text)
        # 移除DR字符（大小写不敏感）
        text = re.sub(r'DR', '', text, flags=re.IGNORECASE)
        # 清理可能产生的多余空格
        text = re.sub(r'\s+', '', text)
        
        return text
    
    def extract_bracket_content(self, text):
        """提取文本中所有括号内容"""
        if pd.isna(text):
            return []
            
        text = str(text)
        # 提取圆括号内容
        round_brackets = re.findall(r'\(([^)]+)\)', text)
        # 提取方括号内容
        square_brackets = re.findall(r'\[([^\]]+)\]', text)
        
        return round_brackets + square_brackets
    
    def remove_bracket_content(self, text, content_to_remove):
        """从文本中移除指定的括号内容"""
        if pd.isna(text) or not content_to_remove:
            return text
            
        text = str(text)
        for content in content_to_remove:
            # 移除圆括号形式
            pattern1 = r'\(' + re.escape(content) + r'\)'
            text = re.sub(pattern1, '', text)
            # 移除方括号形式
            pattern2 = r'\[' + re.escape(content) + r'\]'
            text = re.sub(pattern2, '', text)
        
        # 清理多余空格
        text = re.sub(r'\s+', '', text)
        
        return text
    
    def convert_brackets_to_square(self, text):
        """将圆括号转换为方括号"""
        if pd.isna(text):
            return text
            
        text = str(text)
        text = text.replace('(', '[').replace(')', ']')
        
        return text
    
    def process_duplicate_bracket_content(self, part_text, pose_text):
        """处理重复的括号内容"""
        if pd.isna(part_text) or pd.isna(pose_text):
            return str(part_text), str(pose_text)
        
        part_text = str(part_text)
        pose_text = str(pose_text)
        
        # 提取两个字段的括号内容
        part_brackets = self.extract_bracket_content(part_text)
        pose_brackets = self.extract_bracket_content(pose_text)
        
        # 找出重复的内容
        duplicate_content = []
        for content in part_brackets:
            if content in pose_brackets:
                duplicate_content.append(content)
        
        if duplicate_content:
            # 保留三级部位中的括号内容，并转换为方括号
            processed_part = self.convert_brackets_to_square(part_text)
            
            # 从摆位中移除重复的括号内容
            processed_pose = self.remove_bracket_content(pose_text, duplicate_content)
            
            return processed_part, processed_pose
        
        return part_text, pose_text
    
    def extract_population_info(self, part_text, pose_text):
        """从部位和摆位字段中提取人群信息"""
        population = ''
        
        # 检查部位字段
        if not pd.isna(part_text) and '儿童' in str(part_text):
            population = '儿童'
        
        # 检查摆位字段
        if not pd.isna(pose_text) and '儿童' in str(pose_text):
            population = '儿童'
            
        return population
    
    def process_dr_data(self):
        """处理DR项目名称清单数据"""
        print("\n🔧 正在处理DR项目名称清单数据...")
        
        # 创建处理后的数据副本
        df_processed = self.df_dr.copy()
        
        # 1. 处理所有文本字段：去除空格
        text_columns = df_processed.select_dtypes(include=['object']).columns
        
        print("   去除空格...")
        for col in text_columns:
            df_processed[col] = df_processed[col].apply(self.clean_spaces)
        
        # 2. 处理所有文本字段：中文括号转英文括号
        print("   中文括号（）→ 英文括号()...")
        for col in text_columns:
            df_processed[col] = df_processed[col].apply(self.convert_chinese_to_english_brackets)
        
        # 2.5. 特别处理摆位字段：移除DR字符
        if '摆位' in df_processed.columns:
            print("   移除摆位字段中的DR字符...")
            df_processed['摆位'] = df_processed['摆位'].apply(self.remove_dr_from_pose)
        
        # 2.6. 处理重复的括号内容
        if '三级部位' in df_processed.columns and '摆位' in df_processed.columns:
            print("   处理重复的括号内容...")
            duplicate_count = 0
            
            for idx, row in df_processed.iterrows():
                part_text = row.get('三级部位', '')
                pose_text = row.get('摆位', '')
                
                processed_part, processed_pose = self.process_duplicate_bracket_content(part_text, pose_text)
                
                # 如果有变化，说明处理了重复内容
                if processed_part != part_text or processed_pose != pose_text:
                    df_processed.at[idx, '三级部位'] = processed_part
                    df_processed.at[idx, '摆位'] = processed_pose
                    duplicate_count += 1
            
            print(f"      处理了 {duplicate_count} 条重复括号内容")
        
        # 2.7. 统一将所有剩余的圆括号转换为方括号
        print("   统一转换所有圆括号()为方括号[]...")
        unified_count = 0
        for col in ['三级部位']:  # 只对三级部位字段进行统一转换
            if col in df_processed.columns:
                # 统计转换前的圆括号数量
                before_count = df_processed[col].str.contains('\(', na=False).sum()
                df_processed[col] = df_processed[col].apply(self.convert_brackets_to_square)
                # 统计转换后的圆括号数量
                after_count = df_processed[col].str.contains('\(', na=False).sum()
                unified_count = before_count - after_count
        
        print(f"      统一转换了 {unified_count} 条记录的括号格式")
        
        # 3. 新增三列
        print("   新增字段：人群、疾病、平急诊...")
        df_processed['人群'] = ''
        df_processed['疾病'] = ''
        df_processed['平急诊'] = ''
        
        # 4. 识别儿童信息并填入人群列
        print("   识别儿童信息...")
        for idx, row in df_processed.iterrows():
            part_text = row.get('三级部位', '')
            pose_text = row.get('摆位', '')
            
            population = self.extract_population_info(part_text, pose_text)
            if population:
                df_processed.at[idx, '人群'] = population
        
        print("✅ DR数据处理完成")
        return df_processed
    
    def analyze_processing_results(self, df_processed):
        """分析处理结果"""
        print("\n📊 处理结果分析:")
        print("=" * 40)
        
        # 统计人群信息
        population_count = df_processed[df_processed['人群'] != ''].shape[0]
        print(f"含人群信息的记录: {population_count} 条")
        
        if population_count > 0:
            print("\n👥 人群信息分布:")
            population_stats = df_processed['人群'].value_counts()
            for pop, count in population_stats.items():
                if pop != '':
                    print(f"   {pop}: {count} 条")
        
        # 显示处理前后对比样本
        print("\n🔄 处理前后对比样本:")
        
        # 找出有变化的记录
        original_df = pd.read_excel(self.excel_file_path, sheet_name='DR项目名称清单')
        
        # 显示儿童相关的记录
        child_samples = df_processed[df_processed['人群'] == '儿童'].head(6)
        
        print("\n👥 儿童信息识别样本:")
        for i, (idx, row) in enumerate(child_samples.iterrows(), 1):
            original_pose = original_df.loc[idx, '摆位']
            processed_pose = row['摆位']
            
            print(f"\n   {i}.")
            print(f"      三级部位: {row['三级部位']}")
            print(f"      摆位原始: \"{original_pose}\"")
            print(f"      摆位处理: \"{processed_pose}\"")
            print(f"      人群识别: {row['人群']}")
        
        # 显示DR移除效果样本
        print("\n🔧 DR字符移除效果样本:")
        dr_removal_samples = df_processed.head(8)  # 前8个样本
        
        for i, (idx, row) in enumerate(dr_removal_samples.iterrows(), 1):
            if idx < len(original_df):
                original_pose = original_df.loc[idx, '摆位']
                processed_pose = row['摆位']
                
                # 只显示有变化的（包含DR的）
                if 'DR' in str(original_pose):
                    print(f"\n   {i}.")
                    print(f"      原始摆位: \"{original_pose}\"")
                    print(f"      处理摆位: \"{processed_pose}\"")
                    print(f"      变化说明: 移除了DR字符")
        
        # 显示重复括号内容处理效果
        print("\n🔄 重复括号内容处理效果样本:")
        duplicate_samples_found = 0
        
        for i, (idx, row) in enumerate(df_processed.iterrows()):
            if idx < len(original_df) and duplicate_samples_found < 5:
                original_part = original_df.loc[idx, '三级部位']
                original_pose = original_df.loc[idx, '摆位']
                processed_part = row['三级部位']
                processed_pose = row['摆位']
                
                # 检查是否有重复括号内容的处理
                original_part_brackets = self.extract_bracket_content(original_part)
                original_pose_brackets = self.extract_bracket_content(original_pose)
                
                has_duplicate = any(content in original_pose_brackets for content in original_part_brackets)
                
                if has_duplicate and (original_part != processed_part or original_pose != processed_pose):
                    duplicate_samples_found += 1
                    print(f"\n   {duplicate_samples_found}.")
                    print(f"      三级部位原始: \"{original_part}\"")
                    print(f"      三级部位处理: \"{processed_part}\"")
                    print(f"      摆位原始: \"{original_pose}\"")
                    print(f"      摆位处理: \"{processed_pose}\"")
                    print(f"      变化说明: 保留三级部位括号内容并转换为[]，移除摆位重复内容")
        
        if duplicate_samples_found == 0:
            print("   未发现重复的括号内容需要处理")
        
        # 显示统一括号转换效果
        print("\n📐 统一括号转换效果验证:")
        round_bracket_count = df_processed['三级部位'].str.contains('\(', na=False).sum()
        square_bracket_count = df_processed['三级部位'].str.contains('\[', na=False).sum()
        
        print(f"   三级部位字段中圆括号()数量: {round_bracket_count}")
        print(f"   三级部位字段中方括号[]数量: {square_bracket_count}")
        
        if round_bracket_count == 0:
            print("   ✅ 所有三级部位的圆括号已成功转换为方括号")
        else:
            print(f"   ⚠️  仍有{round_bracket_count}条记录包含圆括号")
    
    def save_processed_data(self, df_processed, output_sheet_name='DR项目名称清单_处理后'):
        """将处理后的数据保存到原始文件的新sheet中"""
        print(f"\n💾 正在保存处理后的数据到新sheet: {output_sheet_name}")
        
        try:
            # 读取原始文件的所有sheet
            with pd.ExcelFile(self.excel_file_path) as xls:
                all_sheets = {}
                for sheet_name in xls.sheet_names:
                    all_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)
            
            # 添加处理后的数据
            all_sheets[output_sheet_name] = df_processed
            
            # 保存到原始文件
            with pd.ExcelWriter(self.excel_file_path, engine='openpyxl', mode='w') as writer:
                for sheet_name, df in all_sheets.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            print(f"✅ 数据已保存到 {self.excel_file_path} 的 '{output_sheet_name}' sheet")
            print(f"   新增列: 人群, 疾病, 平急诊")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            # 备用方案：保存到新文件
            backup_file = self.excel_file_path.replace('.xlsx', '_DR处理后.xlsx')
            with pd.ExcelWriter(backup_file, engine='openpyxl') as writer:
                df_processed.to_excel(writer, sheet_name=output_sheet_name, index=False)
            print(f"   已保存到备用文件: {backup_file}")

def main():
    """主函数"""
    print("🏥 DR项目名称清单处理程序")
    print("=" * 50)
    print("📋 处理任务:")
    print("   1. 去除所有字段的空格")
    print("   2. 中文括号（）→ 英文括号()")
    print("   3. 移除摆位字段中的DR字符")
    print("   4. 处理重复括号内容（保留三级部位，转换为[]，移除摆位重复）")
    print("   5. 统一转换三级部位字段所有圆括号()为方括号[]")
    print("   6. 新增三列：人群、疾病、平急诊")
    print("   7. 识别\"儿童\"信息并填入人群列")
    print("=" * 50)
    
    # 数据文件路径
    excel_file = '../data/NEW_检查项目名称结构表 (11).xlsx'
    
    try:
        # 创建处理器
        processor = DRSheetProcessor(excel_file)
        
        # 加载数据
        processor.load_data()
        
        # 处理数据
        df_processed = processor.process_dr_data()
        
        # 分析处理结果
        processor.analyze_processing_results(df_processed)
        
        # 保存处理后的数据
        processor.save_processed_data(df_processed)
        
        print(f"\n🎉 DR数据处理完成!")
        print(f"   原始数据: {len(processor.df_dr)} 行")
        print(f"   处理后数据: {len(df_processed)} 行 x {len(df_processed.columns)} 列")
        print(f"   新增字段: 人群, 疾病, 平急诊")
        print(f"   处理特点: 去除空格、中文括号转英文括号、移除摆位DR字符、处理重复括号内容、统一方括号格式、识别儿童信息")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()