#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看医保项目生成结果
"""

import pandas as pd
import glob
import os

def view_latest_results():
    """查看最新的医保项目生成结果"""
    
    # 查找最新的医保项目文件
    pattern = "../output/医保检查项目清单_*.xlsx"
    files = glob.glob(pattern)
    
    if not files:
        print("❌ 未找到医保项目清单文件")
        return
    
    # 获取最新文件
    latest_file = max(files, key=os.path.getctime)
    print(f"📄 查看文件: {latest_file}")
    
    try:
        # 读取主要数据
        df_main = pd.read_excel(latest_file, sheet_name='医保项目清单')
        print(f"\n📊 医保项目清单概览:")
        print(f"   总项目数: {len(df_main)} 个")
        
        # 显示前几行数据
        print(f"\n📋 前5行数据:")
        print(df_main.head().to_string(index=False))
        
        # 显示模态分布
        print(f"\n📈 模态分布:")
        modality_counts = df_main['模态'].value_counts()
        for modality, count in modality_counts.items():
            print(f"   {modality}: {count} 个")
        
        # 显示编码示例
        print(f"\n🔢 编码示例:")
        for i, row in df_main.head(3).iterrows():
            print(f"   {row['模态']}: {row['检查项目编码']} - {row['检查项目名称']}")
        
        # 读取统计信息
        try:
            df_stats = pd.read_excel(latest_file, sheet_name='统计信息')
            print(f"\n📊 详细统计:")
            print(df_stats.to_string(index=False))
        except:
            print("   (统计信息sheet不存在)")
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

if __name__ == "__main__":
    view_latest_results()
