#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证统一化改进后的医保项目格式
"""

import pandas as pd
import glob
import os

def verify_unified_format():
    """验证统一化改进后的格式"""
    
    # 查找最新的医保项目文件
    pattern = "../output/医保检查项目清单_*.xlsx"
    files = glob.glob(pattern)
    
    if not files:
        print("❌ 未找到医保项目清单文件")
        return
    
    # 获取最新文件
    latest_file = max(files, key=os.path.getctime)
    print(f"📄 验证文件: {latest_file}")
    
    try:
        # 读取数据
        df = pd.read_excel(latest_file, sheet_name='医保项目清单')
        
        print(f"\n🔍 统一化格式验证:")
        
        # 检查字段顺序和存在性
        expected_fields = [
            '模态', '一级编码', '一级部位', '二级编码', '二级部位', 
            '三级编码', '部位编码', '三级部位', '医保映射编码', '医保项目名称', 
            '医保扩展码', '检查项目名称', '检查项目编码', 
            '人群编码', '疾病编码', '平急诊编码', '医保项目码'
        ]
        
        actual_fields = list(df.columns)
        
        print(f"   期望字段数: {len(expected_fields)}")
        print(f"   实际字段数: {len(actual_fields)}")
        
        # 检查字段是否存在
        missing_fields = [field for field in expected_fields if field not in actual_fields]
        extra_fields = [field for field in actual_fields if field not in expected_fields]
        
        if missing_fields:
            print(f"   ❌ 缺失字段: {missing_fields}")
        else:
            print(f"   ✅ 所有必需字段都存在")
            
        if extra_fields:
            print(f"   ⚠️  额外字段: {extra_fields}")
        
        # 检查字段顺序
        print(f"\n📋 字段顺序对比:")
        for i, (expected, actual) in enumerate(zip(expected_fields, actual_fields[:len(expected_fields)])):
            status = "✅" if expected == actual else "❌"
            print(f"   {i+1:2d}. {status} 期望: {expected:12s} | 实际: {actual}")
        
        # 检查关键字段内容
        print(f"\n🔍 关键字段验证:")
        
        # 检查医保映射编码字段
        if '医保映射编码' in df.columns:
            print(f"   ✅ 医保映射编码字段存在")
            print(f"      示例值: {df['医保映射编码'].head(3).tolist()}")
        else:
            print(f"   ❌ 医保映射编码字段缺失")
        
        # 检查医保项目码字段
        if '医保项目码' in df.columns:
            print(f"   ✅ 医保项目码字段存在")
            print(f"      示例值: {df['医保项目码'].head(3).tolist()}")
        else:
            print(f"   ❌ 医保项目码字段缺失")
        
        # 检查是否删除了重复的互认项目名称字段
        if '互认项目名称' in df.columns:
            print(f"   ❌ 互认项目名称字段仍然存在（应该删除）")
        else:
            print(f"   ✅ 互认项目名称字段已删除")
        
        # 检查编码格式
        print(f"\n🔢 编码格式验证:")
        
        # 检查检查项目编码长度
        code_lengths = df['检查项目编码'].astype(str).str.len()
        print(f"   检查项目编码长度分布: {code_lengths.value_counts().to_dict()}")
        
        # 检查部位编码长度
        part_code_lengths = df['部位编码'].astype(str).str.len()
        print(f"   部位编码长度分布: {part_code_lengths.value_counts().to_dict()}")
        
        # 显示完整示例
        print(f"\n📋 完整记录示例:")
        for i, row in df.head(2).iterrows():
            print(f"   第{i+1}行:")
            for field in expected_fields:
                if field in row:
                    print(f"     {field}: {row[field]}")
            print()
        
        # 统计验证
        correct_codes = (code_lengths == 16).sum()
        correct_parts = (part_code_lengths == 5).sum()
        total_records = len(df)
        
        print(f"📊 格式统计:")
        print(f"   总记录数: {total_records}")
        print(f"   16位编码数量: {correct_codes}")
        print(f"   5位部位编码数量: {correct_parts}")
        print(f"   编码格式正确率: {correct_codes/total_records*100:.1f}%")
        print(f"   部位编码正确率: {correct_parts/total_records*100:.1f}%")
        
        # 最终评估
        if (missing_fields == [] and 
            '互认项目名称' not in df.columns and 
            '医保映射编码' in df.columns and 
            '医保项目码' in df.columns and
            correct_codes == total_records and
            correct_parts == total_records):
            print(f"\n🎉 统一化改进验证通过！")
        else:
            print(f"\n⚠️  统一化改进需要进一步调整")
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_unified_format()
