# 归档文件目录

本目录包含项目开发过程中的历史文件和已废弃代码，用于备份和参考。

## 📁 目录结构

```
archive/
├── old_docs/                    # 历史文档文件
│   ├── 医疗检查项目处理系统统一化改进报告.md
│   ├── DR项目生成规则说明.md
│   ├── 编码格式修正说明.md
│   ├── 项目名称和编码生成说明.md
│   ├── 项目结构说明.md
│   ├── README.md
│   ├── 统一Pipeline使用说明.md
│   ├── 使用说明.md
│   └── 项目清理完成报告.md
└── README.md                   # 本文件
```

## 📋 文件说明

### 历史文档 (old_docs/)

这些文档记录了项目的开发历程和各阶段的技术决策：

- **系统改进报告** - 统一化改进的详细说明
- **DR项目生成规则** - DR模块的技术规范
- **编码格式修正** - 编码规范的演进过程
- **项目结构说明** - 早期的项目架构设计
- **使用说明文档** - 各阶段的使用指南

## 🔄 文档演进

这些历史文档已整合到根目录的综合 `README.md` 中，包含：

- 系统概述和功能特点
- 完整的技术规范
- 详细的使用指南
- 架构说明和扩展指南

## 📝 注意事项

- 归档文件仅用于参考，不影响系统运行
- 如需查看最新文档，请参考根目录 `README.md`
- 如需运行系统，请使用 `src/run_enhanced_pipeline.py`

---

**归档日期**: 2025年7月10日  
**归档原因**: 目录清理和文档整合 