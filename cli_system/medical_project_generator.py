#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医学检查项目名称和编码生成验证程序
基于 NEW_检查项目名称结构表 (11).xlsx 数据源

根据项目名称和编码生成说明.md 的规则验证生成过程
直接使用数据源中的"三级部位"和"部位编码"字段
支持根据部位名称智能判断疾病、人群、平急诊编码
"""

import pandas as pd
import numpy as np
from datetime import datetime

class MedicalProjectGenerator:
    """医学检查项目生成器"""
    
    def __init__(self, excel_file_path):
        """初始化生成器"""
        self.excel_file_path = excel_file_path
        self.df_parts = None
        self.df_scan_mapping = None
        self.df_disease = None
        self.df_population = None
        self.load_data()
        
    def load_data(self):
        """加载数据源"""
        print("🔄 正在加载数据源...")
        try:
            # 定义编码列的数据类型为字符串，避免自动转换为数值
            dtype_parts = {
                '一级编码': str,
                '二级编码': str, 
                '三级编码': str,
                '部位编码': str
            }
            
            dtype_scans = {
                '医保映射码': str,
                '医保扩展码': str
            }
            
            # 加载三级部位结构表
            self.df_parts = pd.read_excel(self.excel_file_path, sheet_name='三级部位结构', dtype=dtype_parts)
            print(f"✅ 三级部位结构表: {self.df_parts.shape[0]} 行 x {self.df_parts.shape[1]} 列")
            
            # 加载扫描方式医保映射编码表  
            self.df_scan_mapping = pd.read_excel(self.excel_file_path, sheet_name='扫描方式医保映射编码', dtype=dtype_scans)
            print(f"✅ 扫描方式医保映射编码表: {self.df_scan_mapping.shape[0]} 行 x {self.df_scan_mapping.shape[1]} 列")
            
            # 加载疾病表
            self.df_disease = pd.read_excel(self.excel_file_path, sheet_name='疾病表')
            print(f"✅ 疾病表: {self.df_disease.shape[0]} 行 x {self.df_disease.shape[1]} 列")
            
            # 加载人群表
            self.df_population = pd.read_excel(self.excel_file_path, sheet_name='人群表')
            print(f"✅ 人群表: {self.df_population.shape[0]} 行 x {self.df_population.shape[1]} 列")
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise
    
    def clean_part_data(self):
        """清理部位数据"""
        print("\n🧹 正在清理部位数据...")
        
        # 处理CT/MR/DR适用性标记
        for col in ['CT', 'MR', 'DR']:
            if col in self.df_parts.columns:
                # 将'1'标记为True，其他为False
                self.df_parts[f'{col}_适用'] = self.df_parts[col] == '1'
        
        # 直接使用部位编码字段（已处理好位数调整）
        self.df_parts['部位编码_文本'] = self.df_parts['部位编码'].astype(str)
        
        print(f"✅ 部位数据清理完成，部位编码示例: {self.df_parts['部位编码_文本'].head().tolist()}")
    
    def create_scan_mapping_dict(self):
        """创建扫描方式映射字典"""
        print("\n📚 正在创建扫描方式映射字典...")
        
        scan_dict = {}
        for _, row in self.df_scan_mapping.iterrows():
            scan_name = row['扫描方式']
            scan_dict[scan_name] = {
                '医保映射码': row['医保映射码'],
                '医保扩展码': str(row['医保扩展码']).zfill(2)  # 补0为2位
            }
        
        print(f"✅ 扫描方式映射字典创建完成，包含 {len(scan_dict)} 种扫描方式")
        return scan_dict
    
    def get_applicable_scans_for_part(self, row):
        """获取部位适用的扫描方式"""
        applicable_scans = []
        
        # 获取CT扫描方式
        if row.get('CT_适用', False):
            ct_scan_columns = [col for col in self.df_parts.columns if col.startswith('CT') and col != 'CT' and col != 'CT_适用' and row.get(col) == 1]
            applicable_scans.extend(ct_scan_columns)
        
        # 获取MR扫描方式  
        if row.get('MR_适用', False):
            mr_scan_columns = [col for col in self.df_parts.columns if col.startswith('MR') and col != 'MR' and col != 'MR_适用' and row.get(col) == 1]
            applicable_scans.extend(mr_scan_columns)
        
        return applicable_scans
    
    def generate_project_name(self, modality, part_name, scan_method):
        """生成项目名称"""
        # 基于实际数据源分析，扫描方式都有完整的中文描述
        # 例如：CT血管成像CTA、MR水成像MRH、MR血管平扫MRA
        # 安全地去除前缀，保留完整的描述部分
        
        if scan_method.startswith('CT') and len(scan_method) > 2:
            scan_simple = scan_method[2:]  # 去除CT前缀
        elif scan_method.startswith('MR') and len(scan_method) > 2:
            scan_simple = scan_method[2:]  # 去除MR前缀
        else:
            scan_simple = scan_method
        
        return f"{modality}{part_name}({scan_simple})"
    
    def determine_disease_code(self, part_name):
        """根据部位名称判断疾病编码"""
        part_name = str(part_name).lower()
        
        # 疾病关键词映射
        disease_keywords = {
            1: ['外伤'],  # 外伤
            2: ['认知'],  # 认知
            3: ['癫痫'],  # 癫痫
            4: ['picc'], # PICC
            5: ['卒中']   # 卒中
        }
        
        for disease_code, keywords in disease_keywords.items():
            for keyword in keywords:
                if keyword in part_name:
                    return str(disease_code)
        
        return '0'  # 默认无特定疾病
    
    def determine_population_code(self, part_name):
        """根据部位名称判断人群编码"""
        part_name = str(part_name).lower()
        
        # 人群关键词映射
        population_keywords = {
            1: ['胎儿', '胎'],     # 胎儿
            2: ['新生儿'],         # 新生儿
            3: ['儿童', '小儿'],   # 儿童
            4: ['孕妇', '孕'],     # 孕妇
        }
        
        for pop_code, keywords in population_keywords.items():
            for keyword in keywords:
                if keyword in part_name:
                    return str(pop_code)
        
        return '0'  # 默认普通成人
    
    def determine_emergency_code(self, part_name):
        """根据部位名称判断平急诊编码"""
        part_name = str(part_name).lower()
        
        # 急诊关键词
        emergency_keywords = ['外伤', '急诊', '急性', '卒中']
        
        for keyword in emergency_keywords:
            if keyword in part_name:
                return '1'  # 急诊
        
        return '0'  # 默认平诊
    
    def generate_project_code(self, insurance_code, part_code, extension_code, population_code='0', disease_code='0', emergency_code='0'):
        """生成项目编码（16位）"""
        # [医保映射码6位][部位编码5位][医保扩展码2位][人群编码1位][疾病编码1位][平急诊编码1位]
        return insurance_code + part_code + extension_code + population_code + disease_code + emergency_code
    
    def generate_projects(self, max_parts=None):
        """生成检查项目"""
        if max_parts:
            print(f"\n🏭 正在生成检查项目（演示前{max_parts}个部位）...")
        else:
            print(f"\n🏭 正在生成完整检查项目清单...")
        
        self.clean_part_data()
        scan_mapping = self.create_scan_mapping_dict()
        
        projects = []
        processed_parts = 0
        
        for idx, part_row in self.df_parts.iterrows():
            if max_parts and processed_parts >= max_parts:
                break
                
            # 获取部位适用的扫描方式
            applicable_scans = self.get_applicable_scans_for_part(part_row)
            
            if not applicable_scans:
                continue
                
            processed_parts += 1
            
            # 根据部位名称智能判断编码
            part_name = part_row['三级部位']
            population_code = self.determine_population_code(part_name)
            disease_code = self.determine_disease_code(part_name)
            emergency_code = self.determine_emergency_code(part_name)
            
            for scan_method in applicable_scans:
                if scan_method not in scan_mapping:
                    print(f"⚠️  未找到扫描方式映射: {scan_method}")
                    continue
                
                # 确定模态
                modality = 'CT' if scan_method.startswith('CT') else 'MR'
                
                # 生成项目信息
                scan_info = scan_mapping[scan_method]
                
                # 确保编码保持原始字符格式
                level1_code = str(part_row['一级编码']).replace('.0', '')  # 一级编码1位
                level2_code = str(part_row['二级编码']).replace('.0', '').zfill(2)  # 二级编码2位
                level3_code = str(part_row['三级编码']).replace('.0', '').zfill(2)  # 三级编码2位
                
                project = {
                    '模态': modality,
                    '一级编码': level1_code,
                    '一级部位': part_row['一级部位'],
                    '二级编码': level2_code,
                    '二级部位': part_row['二级部位'],
                    '三级编码': level3_code,
                    '部位编码': part_row['部位编码_文本'],
                    '三级部位': part_row['三级部位'],
                    '扫描方式': scan_method,
                    '医保映射码': scan_info['医保映射码'],
                    '医保扩展码': str(scan_info['医保扩展码']).zfill(2),
                    '检查项目名称': self.generate_project_name(
                        modality, 
                        part_row['三级部位'], 
                        scan_method
                    ),
                    '检查项目编码': self.generate_project_code(
                        scan_info['医保映射码'], 
                        part_row['部位编码_文本'], 
                        scan_info['医保扩展码'],
                        population_code,
                        disease_code,
                        emergency_code
                    ),
                    '人群编码': population_code,
                    '疾病编码': disease_code,
                    '平/急诊编码': emergency_code
                }
                
                projects.append(project)
        
        print(f"✅ 项目生成完成，共生成 {len(projects)} 个项目")
        return projects
    
    def validate_projects(self, projects):
        """验证项目数据质量"""
        print(f"\n🔍 正在验证项目数据质量...")
        
        issues = []
        
                 # 检查项目编码长度
        for project in projects:
            code_length = len(project['检查项目编码'])
            if code_length != 16:
                issues.append(f"项目编码长度错误: {project['检查项目名称']} - {project['检查项目编码']} (长度:{code_length})")
        
        # 检查项目编码唯一性
        codes = [p['检查项目编码'] for p in projects]
        duplicate_codes = [code for code in set(codes) if codes.count(code) > 1]
        if duplicate_codes:
            issues.append(f"发现重复项目编码: {duplicate_codes}")
        
        # 检查项目名称格式
        for project in projects:
            name = project['检查项目名称']
            if not (name.startswith(('CT', 'MR')) and '(' in name and ')' in name):
                issues.append(f"项目名称格式错误: {name}")
        
        if issues:
            print(f"❌ 发现 {len(issues)} 个质量问题:")
            for issue in issues[:5]:  # 只显示前5个
                print(f"   - {issue}")
        else:
            print("✅ 项目数据质量验证通过")
        
        return len(issues) == 0
    
    def export_results(self, projects, output_file=None):
        """导出结果到Excel，按模态分别创建sheet"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"output/CT&MR检查项目清单_{timestamp}.xlsx"
        
        print(f"\n📤 正在导出结果到: {output_file}")
        
        # 创建总的DataFrame
        df_all = pd.DataFrame(projects)
        
        # 按模态分组
        ct_projects = [p for p in projects if p['模态'] == 'CT']
        mr_projects = [p for p in projects if p['模态'] == 'MR']
        
        df_ct = pd.DataFrame(ct_projects) if ct_projects else pd.DataFrame()
        df_mr = pd.DataFrame(mr_projects) if mr_projects else pd.DataFrame()
        
        # 处理编码字段格式的函数
        def format_encoding_fields(df):
            if df.empty:
                return df
                
            # 确保所有编码字段保持为字符串格式，避免Excel自动转换
            encoding_columns = ['一级编码', '二级编码', '三级编码', '部位编码', '检查项目编码', '医保映射码', '医保扩展码', '人群编码', '疾病编码', '平/急诊编码']
            for col in encoding_columns:
                if col in df.columns:
                    df[col] = df[col].astype(str)
                    
            # 为避免Excel自动转换为数字，在所有编码字段前加单引号强制为文本格式
            text_format_columns = ['一级编码', '二级编码', '三级编码', '部位编码', '检查项目编码', '医保映射码', '医保扩展码']
            for col in text_format_columns:
                if col in df.columns:
                    df[col] = "'" + df[col]
            return df
        
        # 通用排序函数
        def sort_dataframe(df, sheet_type='original'):
            """按字段优先级对DataFrame进行排序
            
            Args:
                df: 要排序的DataFrame
                sheet_type: sheet类型，'original'表示原始格式(全部项目/CT项目/MR项目)，'v2'表示新格式(CT-2/MR-2)
            """
            if df.empty:
                return df
            
            # 处理十六进制医保映射码，确保正确排序
            def convert_insurance_code(code_str):
                """将医保映射码转换为数值，支持十六进制"""
                try:
                    # 去除可能的单引号前缀
                    clean_code = str(code_str).replace("'", "")
                    # 尝试按十六进制解析
                    return int(clean_code, 16)
                except ValueError:
                    try:
                        # 如果失败，按十进制解析
                        return int(clean_code)
                    except ValueError:
                        return 0
            
            # 处理部位编码，按数字大小排序
            def convert_part_code(code_str):
                """将部位编码转换为数值，按数字大小排序"""
                try:
                    clean_code = str(code_str).replace("'", "")
                    return int(clean_code)
                except ValueError:
                    return 0
            
            # 处理编码字段，按数字大小排序
            def convert_code(code_str):
                """将编码转换为数值"""
                try:
                    clean_code = str(code_str).replace("'", "")
                    return int(clean_code)
                except ValueError:
                    return 0
            
            # 创建排序字段
            df_sorted = df.copy()
            
            if sheet_type == 'v2':
                # CT-2/MR-2格式：按医保映射码 -> 医保扩展码 -> 扫描方式 -> 一级编码 -> 二级编码 -> 三级编码 -> 部位编码排序
                df_sorted['医保映射码_排序'] = df_sorted['医保映射码'].apply(convert_insurance_code)
                df_sorted['医保扩展码_排序'] = pd.to_numeric(df_sorted['医保扩展码'].astype(str).str.replace("'", ""), errors='coerce')
                df_sorted['一级编码_排序'] = df_sorted['一级编码'].apply(convert_code)
                df_sorted['二级编码_排序'] = df_sorted['二级编码'].apply(convert_code)
                df_sorted['三级编码_排序'] = df_sorted['三级编码'].apply(convert_code)
                df_sorted['部位编码_排序'] = df_sorted['部位编码'].apply(convert_part_code)
                
                sort_columns = ['医保映射码_排序', '医保扩展码_排序', '扫描方式', '一级编码_排序', '二级编码_排序', '三级编码_排序', '部位编码_排序']
                temp_columns = ['医保映射码_排序', '医保扩展码_排序', '一级编码_排序', '二级编码_排序', '三级编码_排序', '部位编码_排序']
                
            else:
                # 原始格式：按一级编码 -> 二级编码 -> 三级编码 -> 部位编码 -> 扫描方式 -> 医保映射码 -> 医保扩展码排序
                df_sorted['一级编码_排序'] = df_sorted['一级编码'].apply(convert_code)
                df_sorted['二级编码_排序'] = df_sorted['二级编码'].apply(convert_code)
                df_sorted['三级编码_排序'] = df_sorted['三级编码'].apply(convert_code)
                df_sorted['部位编码_排序'] = df_sorted['部位编码'].apply(convert_part_code)
                df_sorted['医保映射码_排序'] = df_sorted['医保映射码'].apply(convert_insurance_code)
                df_sorted['医保扩展码_排序'] = pd.to_numeric(df_sorted['医保扩展码'].astype(str).str.replace("'", ""), errors='coerce')
                
                sort_columns = ['一级编码_排序', '二级编码_排序', '三级编码_排序', '部位编码_排序', '扫描方式', '医保映射码_排序', '医保扩展码_排序']
                temp_columns = ['一级编码_排序', '二级编码_排序', '三级编码_排序', '部位编码_排序', '医保映射码_排序', '医保扩展码_排序']
            
            df_sorted = df_sorted.sort_values(sort_columns, ascending=True).reset_index(drop=True)
            
            # 删除临时排序列
            df_sorted = df_sorted.drop(temp_columns, axis=1)
            
            return df_sorted
        
        # 先排序，再格式化各个DataFrame (使用原始格式排序)
        df_all_sorted = sort_dataframe(df_all.copy(), sheet_type='original')
        df_ct_sorted = sort_dataframe(df_ct.copy(), sheet_type='original')
        df_mr_sorted = sort_dataframe(df_mr.copy(), sheet_type='original')
        
        df_all_formatted = format_encoding_fields(df_all_sorted)
        df_ct_formatted = format_encoding_fields(df_ct_sorted)
        df_mr_formatted = format_encoding_fields(df_mr_sorted)
        
        # 创建统计信息
        stats = {
            '统计项目': ['总项目数', 'CT项目数', 'MR项目数', '唯一部位数', '项目编码长度', '唯一编码数'],
            '数值': [
                len(projects),
                len(ct_projects),
                len(mr_projects),
                len(set([p['三级部位'] for p in projects])),
                len(projects[0]['检查项目编码']) if projects else 0,
                len(set([p['检查项目编码'] for p in projects]))
            ]
        }
        df_stats = pd.DataFrame(stats)
        
        # 创建各模态的详细统计
        modality_stats = []
        for modality in ['CT', 'MR']:
            modality_projects = [p for p in projects if p['模态'] == modality]
            if modality_projects:
                part_count = len(set([p['三级部位'] for p in modality_projects]))
                scan_methods = set([p['扫描方式'] for p in modality_projects])
                modality_stats.append({
                    '模态': modality,
                    '项目数': len(modality_projects),
                    '部位数': part_count,
                    '扫描方式数': len(scan_methods),
                    '扫描方式': ', '.join(sorted(scan_methods))
                })
        
        df_modality_stats = pd.DataFrame(modality_stats)
        
        # 创建CT-2和MR-2格式的DataFrame（按指定字段顺序）
        def create_v2_format(projects_list):
            """创建V2格式的DataFrame，使用指定的字段顺序，并按字段排序"""
            if not projects_list:
                return pd.DataFrame()
            
            # 指定的字段顺序
            v2_columns = [
                '模态', '医保映射码', '医保扩展码', '扫描方式', 
                '一级编码', '一级部位', '二级编码', '二级部位', 
                '三级编码', '部位编码', '三级部位', 
                '检查项目名称', '检查项目编码', 
                '人群编码', '疾病编码', '平/急诊编码'
            ]
            
            # 重新排列数据
            v2_data = []
            for project in projects_list:
                v2_project = {}
                for col in v2_columns:
                    v2_project[col] = project.get(col, '')
                v2_data.append(v2_project)
            
            df = pd.DataFrame(v2_data)
            
            # 使用V2格式的排序逻辑
            df = sort_dataframe(df, sheet_type='v2')
            
            return df
        
        df_ct_v2 = create_v2_format(ct_projects)
        df_mr_v2 = create_v2_format(mr_projects)
        
        # 格式化V2版本的DataFrame
        df_ct_v2_formatted = format_encoding_fields(df_ct_v2.copy())
        df_mr_v2_formatted = format_encoding_fields(df_mr_v2.copy())
        
        # 导出到Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 总览sheet
            df_all_formatted.to_excel(writer, sheet_name='全部项目', index=False)
            
            # CT项目sheet
            if not df_ct_formatted.empty:
                df_ct_formatted.to_excel(writer, sheet_name='CT项目', index=False)
            
            # MR项目sheet  
            if not df_mr_formatted.empty:
                df_mr_formatted.to_excel(writer, sheet_name='MR项目', index=False)
            
            # CT-2项目sheet（新格式）
            if not df_ct_v2_formatted.empty:
                df_ct_v2_formatted.to_excel(writer, sheet_name='CT-2', index=False)
            
            # MR-2项目sheet（新格式）
            if not df_mr_v2_formatted.empty:
                df_mr_v2_formatted.to_excel(writer, sheet_name='MR-2', index=False)
            
            # 统计信息sheet
            df_stats.to_excel(writer, sheet_name='统计信息', index=False)
            df_modality_stats.to_excel(writer, sheet_name='模态统计', index=False)
        
        print(f"✅ 结果导出完成: {output_file}")
        print(f"   📋 全部项目: {len(projects)} 个")
        print(f"   🩻 CT项目: {len(ct_projects)} 个")
        print(f"   🧲 MR项目: {len(mr_projects)} 个")
        print(f"   📊 Excel包含: 全部项目、CT项目、MR项目、CT-2、MR-2、统计信息 等sheet")
        
        return output_file

def main():
    """主函数 - 验证程序入口"""
    print("🏥 医学检查项目名称和编码生成验证程序")
    print("=" * 60)
    
    # 数据文件路径
    excel_file = 'data/NEW_检查项目名称结构表 (11).xlsx'
    # 如果相对路径不行，使用绝对路径
    import os
    if not os.path.exists(excel_file):
        excel_file = '/Users/<USER>/Desktop/12-new/data/NEW_检查项目名称结构表 (11).xlsx'
    
    try:
        # 创建生成器
        generator = MedicalProjectGenerator(excel_file)
        
        # 生成完整项目清单
        projects = generator.generate_projects()
        
        # 验证项目质量
        is_valid = generator.validate_projects(projects)
        
        # 显示生成结果示例
        print(f"\n📋 生成结果示例（前5个项目）:")
        print("-" * 80)
        for i, project in enumerate(projects[:5]):
            print(f"{i+1}. {project['检查项目名称']} - {project['检查项目编码']}")
            print(f"   部位: {project['一级部位']}-{project['二级部位']}-{project['三级部位']} (编码: {project['部位编码']})")
            print(f"   编码: {project['医保映射码']}+{project['部位编码']}+{project['医保扩展码']}+{project['人群编码']}{project['疾病编码']}{project['平/急诊编码']}")
            print()
        
        # 导出结果
        output_file = generator.export_results(projects)
        
        print(f"\n🎉 验证程序执行完成!")
        print(f"   - 生成项目数: {len(projects)}")
        print(f"   - 数据质量: {'✅ 通过' if is_valid else '❌ 有问题'}")
        print(f"   - 输出文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 