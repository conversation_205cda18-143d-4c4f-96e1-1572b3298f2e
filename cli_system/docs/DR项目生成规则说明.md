# DR项目生成规则说明

## 数据源和目标

### 输入数据源
- **主要数据源**: sheet "DR项目名称清单" (475行数据)
- **辅助数据源**: 
  - sheet "人群表" (4行：胎儿、新生儿、儿童、孕妇)
  - sheet "疾病表" (3行：外伤、认知、癫痫)

### 输入字段结构
- 基础信息：一级编码、一级部位、二级编码、二级部位、三级部位、三级部位编码
- 核心字段：部位编码、摆位、摆位编码
- 分类字段：人群、疾病、平急诊

## 生成规则详述

### 1. 项目名称生成规则
**格式**: `DR + 三级部位 + "-" + 摆位`

**示例**:
- 输入：三级部位="头颅", 摆位="后前正位"
- 输出：`DR头颅-后前正位`

**处理逻辑**:
- 自动处理空值，确保字符串连接正确
- 去除多余空格，保持格式一致性

### 2. 项目编码生成规则
**格式**: `"110000" + "部位编码"(5位) + "医保扩展码"(5位)` = **16位编码**

#### 2.1 固定前缀
- 所有DR项目编码以 `110000` 开头

#### 2.2 部位编码处理 (5位)
- 直接使用原始数据中的"部位编码"字段
- 自动补零至5位：`str(部位编码).zfill(5)`
- 示例：`10103` → `10103`

#### 2.3 摆位码和医保扩展码处理

**摆位码字段 (2位)**:
- 直接使用原始数据中的"摆位编码"字段
- 自动补零至2位：1位编码补零为2位
- 空值默认为 `00`

**医保扩展码构成 (5位，用于项目编码生成)**:
**组成**: `摆位编码(2位) + 人群编码(1位) + 疾病编码(1位) + 平急诊编码(1位)`

##### 摆位编码处理 (2位)
- 直接使用原始数据中的"摆位编码"字段
- 自动补零至2位：1位编码补零为2位
- 空值默认为 `00`

##### 人群编码映射 (1位)
根据"人群"字段值查找对应编码：
```
胎儿   → 1
新生儿 → 2  
儿童   → 3
孕妇   → 4
空值   → 0 (默认)
```

##### 疾病编码映射 (1位)
根据"疾病"字段值查找对应编码：
```
外伤   → 1
认知   → 2
癫痫   → 3  
空值   → 0 (默认)
```

##### 平急诊编码处理 (1位)
```
急诊   → 1
平诊   → 0
空值   → 0 (默认，按平诊处理)
```

### 3. 编码示例详解

**示例1**:
- 部位编码: `10103`
- 摆位编码: `11` 
- 人群: 空 → `0`
- 疾病: 空 → `0`  
- 平急诊: 空 → `0`
- **摆位码输出**: `11`（2位）
- **项目编码**: `110000` + `10103` + `11000` = `11000010103110000`（16位）

**示例2**:
- 部位编码: `30101`
- 摆位编码: `PA`
- 人群: "儿童" → `3`
- 疾病: "外伤" → `1`
- 平急诊: 空 → `0`
- **摆位码输出**: `PA`（2位）
- **项目编码**: `110000` + `30101` + `PA310` = `11000030101PA310`（16位）

## 输出格式规范

### 1. DR项目专用格式
**重要说明**: DR检查项目清单使用专门针对DR项目优化的字段格式，其中"摆位"和"摆位码"字段更准确地反映了DR检查的业务特点。

### 2. Excel文件结构
输出文件包含6个sheet：

#### Sheet1: DR检查项目清单
包含所有生成的项目数据，按照标准格式字段顺序：

**DR项目专用字段（前16个）：**
1. 模态
2. 一级编码、一级部位
3. 二级编码、二级部位  
4. 三级编码、部位编码、三级部位
5. 摆位
6. 医保映射码、摆位码（2位）
7. 检查项目名称、检查项目编码
8. 人群编码、疾病编码、平急诊编码

**扩展字段（DR项目特有）：**
17. 人群（名称）
18. 疾病（名称）
19. 平急诊（名称）

**总字段数**：19个（16个核心字段 + 3个扩展字段）

#### Sheet2: 统计信息
- 总项目数
- 编码长度验证结果
- 编码格式验证结果  
- 生成时间

#### Sheet3-5: 分类统计
- 人群统计：按人群分组的项目数量
- 疾病统计：按疾病分组的项目数量
- 部位统计：按三级部位分组的项目数量

#### Sheet6: 重复编码项目
包含所有具有重复编码的项目详情：
- **数据来源**: 从主要清单中筛选出编码重复的项目
- **排序方式**: 按检查项目编码排序，便于查看同编码项目
- **用途**: 用于分析重复编码的原因和进行数据清理
- **当前状况**: 发现127个重复编码，涉及275个项目
- **重复原因**: 主要是相同部位+摆位+人群+疾病+平急诊组合导致编码相同

### 3. 编码格式保护
按照 `编码格式修正说明.md` 的要求：
- 所有编码字段前加单引号 `'` 前缀
- 防止Excel自动转换为数字格式
- 确保编码的位数和格式正确性
- 新增医保映射码字段，统一使用"110000"

## 数据质量验证

### 验证项目
1. **编码长度验证**: 确保所有项目编码为16位
2. **编码格式验证**: 确保所有编码以"110000"开头
3. **编码唯一性检查**: 检测重复编码并自动导出到专门sheet

### 重复编码分析
- **检测结果**: 发现127个重复编码，涉及275个项目
- **重复原因**: 相同的部位编码+摆位编码+人群编码+疾病编码+平急诊编码组合
- **数据输出**: 所有重复项目自动导出到"重复编码项目"sheet
- **解决方案**: 可通过增加额外标识位或细化编码规则来避免重复

## 程序使用方法

### 运行环境
- Python 3.x
- 依赖库：pandas, openpyxl

### 执行步骤
```bash
cd src
python3 dr_project_generator.py
```

### 输出位置
`output/DR检查项目清单_YYYYMMDD_HHMMSS.xlsx`

## 技术实现要点

### 1. 数据类型处理
- 强制编码字段为字符串类型
- 处理空值和缺失数据
- 自动补零和格式化

### 2. 映射查找
- 使用字典进行高效的编码映射
- 支持默认值处理
- 大小写和空格容错

### 3. 批量处理
- 一次性处理475条DR项目数据
- 内存友好的数据处理方式
- 完整的错误处理和日志输出

## DR项目专用格式优势

### 1. 业务针对性
- **字段专用化**: "摆位"和"摆位码"更准确反映DR检查特点
- **简化输出**: 摆位码2位显示，便于快速识别和使用
- **编码规范**: 保持16位项目编码格式，确保系统兼容性

### 2. 数据完整性
- **保留原始信息**: 保留DR项目特有的详细信息（人群、疾病、平急诊名称）
- **灵活使用**: 简化字段用于日常操作，扩展字段用于详细分析
- **向下兼容**: 既满足DR业务需求，又不丢失原始信息

## 扩展说明

该生成器设计为通用的DR项目处理框架，可以通过配置修改：
- 编码映射规则
- 输出格式要求  
- 验证标准
- 统计维度

符合医疗信息系统的数据标准化要求，确保生成的项目编码在医保系统中的唯一性和可追溯性。现已实现DR项目专用格式，使用"摆位"和"摆位码"字段更好地体现DR检查业务特点，提升了数据的业务适用性和可维护性。 