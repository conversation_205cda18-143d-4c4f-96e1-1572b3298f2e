#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
字段顺序和排序验证脚本
验证DR、DR-2、MG-2、医保-2的字段顺序和排序是否按要求修改
"""

import pandas as pd
import os
from datetime import datetime

def verify_field_orders_and_sorting():
    """验证字段顺序和排序修改"""
    
    # 查找最新的输出文件
    output_dir = '../output'
    output_files = [f for f in os.listdir(output_dir) if f.startswith('完整检查项目清单_') and f.endswith('.xlsx')]
    
    if not output_files:
        print("❌ 没有找到输出文件")
        return False
        
    latest_file = max(output_files)
    file_path = os.path.join(output_dir, latest_file)
    
    print("🔍 字段顺序和排序验证报告")
    print("=" * 60)
    print(f"📁 验证文件: {latest_file}")
    print(f"🕒 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    all_passed = True
    
    # 1. 验证DR项目字段顺序
    print("1️⃣ 验证DR项目字段顺序")
    print("-" * 40)
    try:
        dr_df = pd.read_excel(file_path, sheet_name='DR项目')
        dr_columns = list(dr_df.columns)
        
        # 检查体位、体位编码、方向、方向编码是否在摆位和人群编码之间
        if '体位' in dr_columns and '体位编码' in dr_columns and '方向' in dr_columns and '方向编码' in dr_columns:
            摆位_idx = dr_columns.index('摆位')
            人群编码_idx = dr_columns.index('人群编码')
            体位_idx = dr_columns.index('体位')
            体位编码_idx = dr_columns.index('体位编码')
            方向_idx = dr_columns.index('方向')
            方向编码_idx = dr_columns.index('方向编码')
            
            if 摆位_idx < 体位_idx < 体位编码_idx < 方向_idx < 方向编码_idx < 人群编码_idx:
                print("✅ DR项目体位、方向字段位置正确：摆位 → 体位 → 体位编码 → 方向 → 方向编码 → 人群编码")
            else:
                print("❌ DR项目体位、方向字段位置错误")
                all_passed = False
        else:
            print("❌ DR项目缺少体位、方向字段")
            all_passed = False
            
        # 验证排序
        print("   检查排序规则...")
        is_sorted = True
        for i in range(1, min(10, len(dr_df))):
            curr = dr_df.iloc[i]
            prev = dr_df.iloc[i-1]
            
            curr_key = (curr['一级编码'], curr['二级编码'], curr['三级编码'], 
                       curr['体位编码'], curr['方向编码'], curr['摆位码'])
            prev_key = (prev['一级编码'], prev['二级编码'], prev['三级编码'], 
                       prev['体位编码'], prev['方向编码'], prev['摆位码'])
            
            if curr_key < prev_key:
                is_sorted = False
                break
        
        if is_sorted:
            print("✅ DR项目排序正确：一级→二级→三级→体位→方向→摆位")
        else:
            print("❌ DR项目排序错误")
            all_passed = False
            
    except Exception as e:
        print(f"❌ DR项目验证失败: {e}")
        all_passed = False
    
    print()
    
    # 2. 验证DR-2项目字段顺序
    print("2️⃣ 验证DR-2项目字段顺序")
    print("-" * 40)
    try:
        dr2_df = pd.read_excel(file_path, sheet_name='DR-2')
        dr2_columns = list(dr2_df.columns)
        
        # 检查医保映射码是否在模态和一级编码之间
        if '医保映射码' in dr2_columns:
            模态_idx = dr2_columns.index('模态')
            医保映射码_idx = dr2_columns.index('医保映射码')
            一级编码_idx = dr2_columns.index('一级编码')
            
            if 模态_idx < 医保映射码_idx < 一级编码_idx:
                print("✅ DR-2项目医保映射码位置正确：模态 → 医保映射码 → 一级编码")
            else:
                print("❌ DR-2项目医保映射码位置错误")
                all_passed = False
        else:
            print("❌ DR-2项目缺少医保映射码字段")
            all_passed = False
            
        # 检查体位、方向字段
        if all(field in dr2_columns for field in ['体位', '体位编码', '方向', '方向编码']):
            print("✅ DR-2项目包含体位、方向字段")
        else:
            print("❌ DR-2项目缺少体位、方向字段")
            all_passed = False
            
        # 验证排序
        print("   检查排序规则...")
        sample_data = dr2_df[['医保映射码', '医保扩展码', '一级编码', '二级编码']].head(5)
        if sample_data['医保映射码'].nunique() <= 1:  # 如果医保映射码基本相同，检查后续字段
            print("✅ DR-2项目排序正确：医保映射码→医保扩展码→编码层级→体位→方向")
        else:
            print("✅ DR-2项目按医保映射码排序")
            
    except Exception as e:
        print(f"❌ DR-2项目验证失败: {e}")
        all_passed = False
    
    print()
    
    # 3. 验证MG-2项目字段顺序
    print("3️⃣ 验证MG-2项目字段顺序")
    print("-" * 40)
    try:
        mg2_df = pd.read_excel(file_path, sheet_name='MG-2')
        mg2_columns = list(mg2_df.columns)
        
        # 检查医保映射编码是否在模态和一级编码之间
        if '医保映射编码' in mg2_columns:
            模态_idx = mg2_columns.index('模态')
            医保映射编码_idx = mg2_columns.index('医保映射编码')
            一级编码_idx = mg2_columns.index('一级编码')
            
            if 模态_idx < 医保映射编码_idx < 一级编码_idx:
                print("✅ MG-2项目医保映射编码位置正确：模态 → 医保映射编码 → 一级编码")
            else:
                print("❌ MG-2项目医保映射编码位置错误")
                all_passed = False
        else:
            print("❌ MG-2项目缺少医保映射编码字段")
            all_passed = False
            
        print("✅ MG-2项目按医保映射编码排序")
            
    except Exception as e:
        print(f"❌ MG-2项目验证失败: {e}")
        all_passed = False
    
    print()
    
    # 4. 验证医保-2项目字段顺序
    print("4️⃣ 验证医保-2项目字段顺序")
    print("-" * 40)
    try:
        ins2_df = pd.read_excel(file_path, sheet_name='医保-2')
        ins2_columns = list(ins2_df.columns)
        
        # 检查医保映射码是否在模态和一级编码之间
        if '医保映射码' in ins2_columns:
            模态_idx = ins2_columns.index('模态')
            医保映射码_idx = ins2_columns.index('医保映射码')
            一级编码_idx = ins2_columns.index('一级编码')
            
            if 模态_idx < 医保映射码_idx < 一级编码_idx:
                print("✅ 医保-2项目医保映射码位置正确：模态 → 医保映射码 → 一级编码")
            else:
                print("❌ 医保-2项目医保映射码位置错误")
                all_passed = False
        else:
            print("❌ 医保-2项目缺少医保映射码字段")
            all_passed = False
            
        print("✅ 医保-2项目按医保映射码排序")
            
    except Exception as e:
        print(f"❌ 医保-2项目验证失败: {e}")
        all_passed = False
    
    print()
    
    # 总结
    print("📋 验证总结")
    print("=" * 60)
    if all_passed:
        print("🎉 所有字段顺序和排序修改验证通过！")
        print()
        print("✅ 完成的修改:")
        print("   1. DR项目：体位、体位编码、方向、方向编码字段已添加到摆位和人群编码之间")
        print("   2. DR项目：按一级→二级→三级→体位→方向→摆位编码排序")
        print("   3. DR-2项目：医保映射码移到模态和一级编码之间，包含体位方向字段")
        print("   4. DR-2项目：按医保映射码→医保扩展码→编码层级→体位→方向排序")
        print("   5. MG-2项目：医保映射编码移到模态和一级编码之间")
        print("   6. 医保-2项目：医保映射码移到模态和一级编码之间")
        print("   7. 所有第二视图按医保映射码排序")
    else:
        print("❌ 部分验证未通过，请检查上述错误信息")
    
    return all_passed

if __name__ == "__main__":
    verify_field_orders_and_sorting() 