# 统一医疗检查项目处理Pipeline

## 概述

`unified_pipeline.py` 是一个统一的处理pipeline，整合了四个不同的医疗检查项目处理流程：

1. **CT/MR项目处理** (`medical_project_generator.py`)
2. **DR项目处理** (`dr_project_generator.py`)  
3. **MG项目处理** (`mg_project_processor.py`)
4. **医保项目处理** (`medical_insurance_generator.py`)

## 特性

✅ **保持原有逻辑**：每个模块的处理逻辑和流程完全不变  
✅ **统一输出**：所有结果合并到一个Excel文件的不同sheet中  
✅ **错误处理**：单个模块失败不影响其他模块运行  
✅ **详细统计**：提供总体统计和各模块详细统计  
✅ **编码格式**：自动处理编码字段格式，防止Excel自动转换  

## 使用方法

### 基本用法

```bash
cd src
python unified_pipeline.py
```

### 输出文件

Pipeline会在 `output/` 目录下生成统一的输出文件：
```
完整检查项目清单_YYYYMMDD_HHMMSS.xlsx
```

## 输出文件结构

生成的Excel文件包含以下sheet：

| Sheet名称 | 内容 | 说明 |
|-----------|------|------|
| 总体统计 | 处理时间、总项目数等 | 整体处理概览 |
| 模块统计 | 各模块处理状态和项目数 | 详细模块信息 |
| CT&MR项目 | 所有CT和MR项目 | 874个项目 |
| CT项目 | 仅CT项目 | 330个项目 |
| MR项目 | 仅MR项目 | 544个项目 |
| DR项目 | DR检查项目 | 337个项目 |
| MG项目 | MG检查项目 | 18个项目 |
| 医保项目 | 医保检查项目 | 148个项目 |

## 项目统计

### 总体数据
- **总项目数**：1,377个检查项目
- **处理模块**：4个模块全部成功
- **数据质量**：所有模块数据验证通过

### 各模块详情

#### CT/MR模块 (874个项目)
- CT项目：330个
- MR项目：544个  
- 覆盖部位：100+个不同部位
- 扫描方式：23种不同扫描方式

#### DR模块 (337个项目)
- 项目数：337个
- 重复项目：0个
- 覆盖部位：多个解剖部位
- 摆位技术：多种摆位方式

#### MG模块 (18个项目)
- 项目数：18个
- 涉及部位：4个部位
- 摆位技术：10种技术
- 数据质量：有3个数据问题已标记

#### 医保模块 (148个项目)
- 项目数：148个
- 重复项目：0个
- 模态分布：DR(6)、IO(6)、MG(2)、RF(28)、CT(53)、MR(53)
- 模态映射：100%成功

## 技术特性

### 编码格式处理
- 所有编码字段前自动添加单引号
- 防止Excel自动转换为数字格式
- 保持编码的完整性和一致性

### 错误处理
- 单个模块失败不影响其他模块
- 详细的错误信息记录
- 处理状态清晰展示

### 数据验证
- 项目编码长度验证（16位）
- 重复编码检测
- 数据完整性检查

## 依赖模块

确保以下四个模块文件存在且可用：
- `medical_project_generator.py`
- `dr_project_generator.py`  
- `mg_project_processor.py`
- `medical_insurance_generator.py`

## 数据源要求

- 数据源文件：`../data/NEW_检查项目名称结构表 (11).xlsx`
- 必须包含所有必要的sheet（三级部位结构、DR项目名称清单、MG项目表、医保编码等）

## 注意事项

1. **内存使用**：处理1,377个项目需要一定内存，建议在内存充足的环境下运行
2. **执行时间**：完整处理大约需要1-2分钟
3. **文件路径**：确保数据源文件路径正确
4. **权限**：确保对output目录有写入权限

## 故障排除

### 常见问题

1. **数据文件不存在**
   ```
   ❌ 数据文件不存在: ../data/NEW_检查项目名称结构表 (11).xlsx
   ```
   解决：检查数据文件路径是否正确

2. **某个模块失败**
   - Pipeline会继续执行其他模块
   - 检查模块统计sheet查看详细错误信息

3. **输出目录不存在**
   - Pipeline会自动创建output目录
   - 确保有文件系统写入权限

## 更新记录

- **2025-07-09**：创建统一Pipeline，整合四个处理模块
- 成功处理1,377个检查项目
- 医保模态映射问题已修复，100%成功率

## 联系信息

如有问题或建议，请检查各个模块的具体实现文件。 