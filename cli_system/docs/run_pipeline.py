#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一医疗检查项目处理Pipeline - 快速启动脚本
增强版：包含CT-2/MR-2格式，DR/MG/医保第二视图，整合统计信息，参考数据表
"""

import os
import sys
from datetime import datetime

def main():
    """主启动函数"""
    print("🏥 统一医疗检查项目处理Pipeline（增强版）")
    print("=" * 70)
    print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("📋 增强功能:")
    print("  1️⃣  CT-2和MR-2格式（指定字段顺序，按医保映射码排序）")
    print("  2️⃣  DR-2、MG-2、医保-2格式（按医保映射码排序的第二视图）") 
    print("  3️⃣  统一统计信息（整合总体统计和模块统计）")
    print("  4️⃣  参考数据表（医保编码、模态表、DR项目名称清单等）")
    print("  💾 输出：18个sheet的完整检查项目清单")
    print()
    
    # 切换到src目录
    src_dir = os.path.join(os.path.dirname(__file__), 'src')
    if not os.path.exists(src_dir):
        print("❌ src目录不存在")
        return
    
    os.chdir(src_dir)
    print(f"📂 工作目录: {os.getcwd()}")
    
    # 检查数据文件
    data_file = "../data/NEW_检查项目名称结构表 (11).xlsx"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        print("   请确保数据文件位于正确位置")
        return
    
    print(f"✅ 数据文件: {os.path.basename(data_file)}")
    
    # 运行pipeline
    print("\n🚀 启动统一处理pipeline...")
    try:
        # 导入并运行
        sys.path.insert(0, '.')
        from unified_pipeline import UnifiedMedicalPipeline
        
        pipeline = UnifiedMedicalPipeline(data_file)
        output_file = pipeline.run_full_pipeline()
        
        print(f"\n🎉 Pipeline执行成功！")
        print(f"📁 输出文件: {output_file}")
        print(f"📂 完整路径: {os.path.abspath(output_file)}")
        print()
        print("📊 输出文件包含18个sheet：")
        print("   📈 主要项目（12个）：统计信息、CT&MR项目、CT项目、MR项目、CT-2、MR-2")
        print("                      DR项目、DR-2、MG项目、MG-2、医保项目、医保-2")
        print("   📚 参考数据（6个）：医保编码、模态表、DR项目名称清单、扫描方式映射表等")
        print()
        print("🔍 验证输出结果:")
        print("   cd src && python verify_enhanced_output.py")
        
    except Exception as e:
        print(f"❌ Pipeline执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 