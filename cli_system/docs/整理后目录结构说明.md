# 整理后的项目目录结构说明

## 📁 目录组织

### 🔧 src/ - 源代码目录
包含所有的核心程序代码：
- `medical_project_generator.py` - CT/MR项目生成器
- `dr_project_generator.py` - DR项目生成器（核心生产程序）
- `dr_sheet_processor.py` - DR数据预处理器
- `data_processor_v2_fixed.py` - 数据处理程序最新版
- `requirements.txt` - Python依赖包列表
- `README.md` - 源代码说明
- `使用说明.md` - 使用指南
- `项目清理完成报告.md` - 项目清理报告

### 📊 data/ - 当前数据目录
存放当前使用的数据文件：
- `NEW_检查项目名称结构表 (11).xlsx` - 最新的检查项目数据

### 📚 docs/ - 文档目录
包含所有的项目文档和报告：
- `README.md` - 项目主说明文档
- `DR项目生成规则说明.md` - DR项目生成规则
- 各种验证报告和处理说明文档
- 项目结构和编码格式说明

### 📦 output/ - 输出目录
存放生成的最新结果文件：
- `完整检查项目清单_20250707_215656.xlsx` - 最新完整项目清单
- `DR检查项目清单_20250707_215517.xlsx` - 最新DR项目清单

### 🗄️ old_data/ - 历史数据目录
存放历史版本的数据文件，用于备份和参考：
- 各个版本的历史数据文件
- 之前的处理结果

## 🧹 整理内容

### ✅ 已完成的整理工作
1. **代码归档**：所有代码文件移动到`src/`目录
2. **文档整理**：所有.md文档移动到`docs/`目录
3. **重复文件清理**：删除重复的代码文件，保留最新版本
4. **代码优化**：删除冗余源代码文件，保留核心功能程序
5. **临时文件清理**：删除所有Excel临时文件（~$开头）和系统文件（.DS_Store）
6. **输出文件优化**：删除过时的输出文件，保留最新版本

### 🗑️ 已删除的文件
- 根目录下的重复代码文件
- 冗余的源代码文件：`data_processor.py`、`data_processor_v2.py`、`dr_complete_matching.py`
- 所有Excel临时文件（~$*.xlsx）
- 系统生成的.DS_Store文件
- 过时的输出文件

### 📋 文件保留原则
- **代码文件**：保留最新的修复版本和核心功能程序
- **数据文件**：当前使用的放在data/，历史版本放在old_data/
- **输出文件**：只保留最新的生成结果
- **文档文件**：全部保留，统一放在docs/目录

### 🎯 **代码优化成果**
**保留的核心程序**：
- `medical_project_generator.py` - CT/MR项目生成器
- `dr_project_generator.py` - DR项目生成器（主要生产程序）
- `dr_sheet_processor.py` - DR数据预处理器（可选）
- `data_processor_v2_fixed.py` - 数据处理器最新版

**删除的冗余文件**：
- `data_processor.py` - 功能被V2修复版替代
- `data_processor_v2.py` - 功能被fixed版本替代
- `dr_complete_matching.py` - 早期版本，功能重复

**优化效果**：代码文件从8个减少到4个，保持了所有核心功能

## 🎯 使用建议

1. **开发工作**：主要在`src/`目录进行
2. **数据处理**：使用`data/`目录中的最新数据
3. **查看文档**：参考`docs/`目录中的相关文档
4. **结果查看**：检查`output/`目录中的最新生成文件
5. **历史追溯**：需要时可参考`old_data/`目录

这样的目录结构清晰明确，便于维护和使用。 