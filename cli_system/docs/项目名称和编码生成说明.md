# 医学检查项目名称和编码生成说明

基于 `NEW_检查项目名称结构表 (12).xlsx` 数据源的标准化项目生成规则。

## 📊 数据源分析

### 1. 三级部位结构表
- **数据规模**: 445行 × 32列
- **核心字段**: 三级部位、部位编码（已处理好位数调整）
- **适用性标记**: CT、MR、DR（值为'1'表示适用）
- **扫描方式列**: CT-平扫、CT-增强、MR-平扫、MR-增强等（值为'1'表示该部位支持该扫描方式）

### 2. 扫描方式医保映射编码表  
- **数据规模**: 23行 × 3列
- **字段结构**: 医保映射码（6位）、医保扩展码（1位，需补0为2位）、扫描方式

## 🏗️ 编码结构设计

### 原始编码结构（16位）
```
[医保映射码][部位编码][医保扩展码][人群编码][疾病编码][平急诊编码]
    6位        6位        2位       1位      1位       1位
```

### 项目编码结构（16位）
使用数据源中已处理好的部位编码：

```
[医保映射码][部位编码][医保扩展码][人群编码][疾病编码][平急诊编码]
    6位       5位      2位       1位      1位       1位
```

**部位编码说明**:
- 直接使用数据源中的"部位编码"字段（已处理好位数调整）
- 部位编码为5位文本格式
- 示例: `"10101"`、`"10201"`等
- **注意**: 所有编码都是文本类型

## 📝 项目名称生成规则

### 格式规范
```
[模态][部位名称]([扫描方式简称])
```

### 扫描方式简化规则
- 去除前缀: 移除"CT-"、"MR-"
- 保持核心描述: 平扫、增强、CTA、MRA等
- 示例: "CT-平扫" → "平扫", "MR-血管增强CE_MRA" → "血管增强CE_MRA"

## 🔄 生成流程

### 步骤1: 部位数据提取
```python
# 从三级部位结构表直接提取
三级部位 = row['三级部位']  # 部位名称
部位编码 = str(row['部位编码'])  # 5位部位编码（已处理）
```

### 步骤2: 适用性筛选
```python
# 根据CT/MR/DR列筛选适用部位
if 模态 == 'CT' and CT列 == '1':
    # 该部位适用CT检查
    pass
```

### 步骤3: 扫描方式匹配
```python
# 从扫描方式医保映射编码表查找
for 扫描方式 in 适用扫描方式:
    if 三级部位结构[扫描方式列] == '1':
        医保映射码, 医保扩展码 = 查找映射编码(扫描方式)
        生成项目(部位, 扫描方式, 医保映射码, 医保扩展码)
```

### 步骤4: 项目编码生成
```python
项目编码 = 医保映射码 + 部位编码 + 医保扩展码.zfill(2) + '000'
# 示例: '210000' + '10101' + '00' + '000' = '2100001010100000'
```

### 步骤5: 项目名称生成
```python
扫描方式简称 = 扫描方式.replace('CT-', '').replace('MR-', '')
项目名称 = f"{模态}{三级部位}({扫描方式简称})"
# 示例: "CT颅脑(平扫)"
```

## 📋 输出格式

### 标准输出列
| 序号 | 字段名称 | 说明 | 示例 |
|------|----------|------|------|
| 1 | 模态 | CT/MR | CT |
| 2 | 一级编码 | 一级编码（去除小数点） | 1 |
| 3 | 一级部位 | 一级部位名称 | 头部 |
| 4 | 二级编码 | 二级编码（补零为两位） | 01 |
| 5 | 二级部位 | 二级部位名称 | 颅脑 |
| 6 | 三级编码 | 三级编码（去除小数点） | 1 |
| 7 | 部位编码 | 5位部位编码（已处理） | 10101 |
| 8 | 三级部位 | 三级部位名称 | 颅脑 |
| 9 | 扫描方式 | 完整扫描方式 | CT-平扫 |
| 10 | 医保映射码 | 6位映射码 | 210000 |
| 11 | 医保扩展码 | 2位扩展码（补0处理） | 00 |
| 12 | 检查项目名称 | 标准名称格式 | CT颅脑(平扫) |
| 13 | 检查项目编码 | 16位完整编码 | 2100001010100000 |
| 14 | 人群编码 | 人群编码（默认值） | 0 |
| 15 | 疾病编码 | 疾病编码（默认值） | 0 |
| 16 | 平急诊编码 | 平急诊编码（默认值） | 0 |

**说明：**
- 一级编码、三级编码：原数据源为浮点数格式（如1.0），已转换为整数字符串（如"1"）
- 二级编码：转换为整数后补零为两位数格式（如"01"）
- 部位编码：数据源中已处理好的5位编码
- 人群编码、疾病编码、平急诊编码：使用默认值"0"
- 检查项目编码：总长度为16位文本格式

## 🎯 生成示例

### 示例1: CT颅脑平扫
- **输入数据**: 
  - 部位: 三级部位=颅脑, 部位编码=10101
  - 扫描方式: CT-平扫, 医保映射码=210000, 医保扩展码=0
- **处理过程**:
  - 项目编码: "210000" + "10101" + "00" + "000" = "2100001010100000"
  - 项目名称: CT + 颅脑 + (平扫) = CT颅脑(平扫)

### 示例2: MR颅脑增强
- **输入数据**:
  - 部位: 三级部位=颅脑, 部位编码=10101
  - 扫描方式: MR-增强, 医保映射码=320000, 医保扩展码=0
- **处理过程**:
  - 项目编码: "320000" + "10101" + "00" + "000" = "3200001010100000"
  - 项目名称: MR + 颅脑 + (增强) = MR颅脑(增强)

## ⚠️ 注意事项

1. **数据清理**: CT/MR/DR列包含全角空格'　'，需要正确处理
2. **编码格式**: 三级编码为float类型，需要转换为整数后格式化
3. **映射查找**: 扫描方式需要精确匹配医保映射编码表
4. **唯一性检查**: 确保生成的项目编码无重复
5. **适用性验证**: 只有标记为适用的部位才生成对应模态项目
6. **编码类型**: 所有编码字段都是文本类型，需要保持字符串格式
7. **默认值设置**: 人群编码、疾病编码、平急诊编码统一使用"000"

## 🔍 质量控制

### 必须验证项
- [ ] 项目编码长度正确（16位）
- [ ] 部位编码格式正确（5位）
- [ ] 项目名称格式正确（模态+部位+(扫描方式)）
- [ ] 无重复项目编码
- [ ] 所有适用部位都已生成项目
- [ ] 医保映射编码匹配正确
- [ ] 人群编码、疾病编码、平急诊编码正确设置为"000"

### 数据统计预期
- **CT项目**: 约163个适用部位 × 7种扫描方式 ≈ 400-500个项目
- **MR项目**: 约196个适用部位 × 16种扫描方式 ≈ 600-800个项目
- **总项目数**: 约1000-1300个项目

---

*本生成说明基于 NEW_检查项目名称结构表 (12).xlsx 数据源分析编写，确保生成结果符合医保编码规范和医学术语标准。* 