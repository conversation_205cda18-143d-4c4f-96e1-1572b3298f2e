import pandas as pd
import re
from datetime import datetime

class DataProcessorV2Fixed:
    """原始数据处理器 V2 修复版 - 清理部位数据并提取疾病和人群信息（保留原内容）"""
    
    def __init__(self, excel_file_path):
        self.excel_file_path = excel_file_path
        self.df_parts = None
        
    def load_data(self):
        """加载原始数据"""
        print("🔄 正在加载原始数据...")
        try:
            # 读取三级部位结构表
            self.df_parts = pd.read_excel(self.excel_file_path, sheet_name='三级部位结构')
            print(f"✅ 三级部位结构表: {self.df_parts.shape[0]} 行 x {self.df_parts.shape[1]} 列")
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise
    
    def clean_spaces_and_characters(self, text):
        """清理空格和特殊字符"""
        if pd.isna(text):
            return text
            
        # 去除首尾空格和多余空格
        text = str(text).strip()
        # 去除中间的多余空格
        text = re.sub(r'\s+', '', text)
        
        return text
    
    def convert_all_brackets_to_square(self, text):
        """将所有类型的括号转换为方括号[]"""
        if pd.isna(text):
            return text
            
        text = str(text)
        
        # 方法：逐个字符检查和转换，确保配对正确
        result = []
        i = 0
        while i < len(text):
            char = text[i]
            if char in '（(':  # 左括号
                result.append('[')
            elif char in '）)':  # 右括号
                result.append(']')
            else:
                result.append(char)
            i += 1
        
        return ''.join(result)
    
    def extract_disease_and_population_info(self, text):
        """提取疾病名称和人群信息（保留原内容）"""
        if pd.isna(text):
            return '', ''
            
        text = str(text)
        disease_info = ''
        population_info = ''
        
        # 提取方括号内的信息
        square_brackets = re.findall(r'\[([^\]]+)\]', text)
        
        # 提取圆括号内的信息（如果还有的话）
        round_brackets = re.findall(r'\(([^\)]+)\)', text)
        
        # 合并所有括号内的信息
        all_brackets = square_brackets + round_brackets
        
        if all_brackets:
            # 定义疾病关键词（扩展列表）
            disease_keywords = ['外伤', '卒中', '认知', '癫痫', '肿瘤', '炎症', '感染', '出血', 
                              '梗塞', '病变', '损伤', '畸形', '囊肿', '积液', '漏', '低颅压',
                              '颅外段', '开闭口位', '颈段', '近肩', '近肘', '近腕', '骨龄',
                              '近髋', '近膝', '近踝', '加收', '鼻漏', '结肠', '空腹', '低张']
            
            # 定义人群关键词
            population_keywords = ['儿童', '成人', '老年', '孕妇', '新生儿', '青少年', '男性', '女性',
                                 '小儿', '婴儿', '胎儿', '少年']
            
            # 逐个分析括号内容
            for content in all_brackets:
                # 检查是否包含疾病关键词
                is_disease = any(keyword in content for keyword in disease_keywords)
                is_population = any(keyword in content for keyword in population_keywords)
                
                if is_disease:
                    if disease_info:
                        disease_info += ', ' + content
                    else:
                        disease_info = content
                elif is_population:
                    if population_info:
                        population_info += ', ' + content
                    else:
                        population_info = content
                else:
                    # 默认归类为疾病信息
                    if disease_info:
                        disease_info += ', ' + content
                    else:
                        disease_info = content
        
        return disease_info, population_info
    
    def get_base_part_name(self, text):
        """获取基础部位名称（移除括号内容）"""
        if pd.isna(text):
            return text
            
        text = str(text)
        # 移除所有括号及其内容
        text = re.sub(r'\[[^\]]*\]', '', text)
        text = re.sub(r'\([^\)]*\)', '', text)
        # 清理可能剩余的空格
        text = text.strip()
        
        return text
    
    def process_data(self):
        """处理数据"""
        print("\n🔧 正在处理数据...")
        
        # 创建处理后的数据副本
        df_processed = self.df_parts.copy()
        
        # 处理所有部位列
        part_columns = ['一级部位', '二级部位', '三级部位']
        
        for col in part_columns:
            if col in df_processed.columns:
                print(f"   处理 {col}...")
                
                # 1. 清理空格和特殊字符
                df_processed[col] = df_processed[col].apply(self.clean_spaces_and_characters)
                
                # 2. 转换所有括号为方括号
                df_processed[col] = df_processed[col].apply(self.convert_all_brackets_to_square)
        
        # 3. 特别处理三级部位，提取疾病和人群信息
        if '三级部位' in df_processed.columns:
            print("   从三级部位提取疾病和人群信息...")
            
            # 提取信息（基于处理后的三级部位）
            extracted_info = df_processed['三级部位'].apply(self.extract_disease_and_population_info)
            
            # 添加新列
            df_processed['疾病信息'] = [info[0] for info in extracted_info]
            df_processed['人群信息'] = [info[1] for info in extracted_info]
            
            # 获取基础部位名称（用于参考）
            df_processed['基础部位名称'] = df_processed['三级部位'].apply(self.get_base_part_name)
        
        print("✅ 数据处理完成")
        return df_processed
    
    def analyze_extraction_results(self, df_processed):
        """分析提取结果"""
        print("\n📊 提取结果分析:")
        print("=" * 40)
        
        # 统计有疾病信息的记录
        disease_count = df_processed[df_processed['疾病信息'] != ''].shape[0]
        print(f"含疾病信息的记录: {disease_count} 条")
        
        # 统计有人群信息的记录
        population_count = df_processed[df_processed['人群信息'] != ''].shape[0]
        print(f"含人群信息的记录: {population_count} 条")
        
        # 显示疾病信息样本
        if disease_count > 0:
            print("\n🔬 疾病信息样本:")
            disease_samples = df_processed[df_processed['疾病信息'] != '']['疾病信息'].unique()[:10]
            for i, disease in enumerate(disease_samples, 1):
                print(f"   {i}. {disease}")
        
        # 显示人群信息样本
        if population_count > 0:
            print("\n👥 人群信息样本:")
            population_samples = df_processed[df_processed['人群信息'] != '']['人群信息'].unique()[:10]
            for i, population in enumerate(population_samples, 1):
                print(f"   {i}. {population}")
        
        # 显示处理前后对比样本
        print("\n🔄 处理前后对比样本:")
        print("   （修复版：所有括号()（）统一转换为方括号[]）")
        
        # 显示原始数据对比
        original_df = pd.read_excel(self.excel_file_path, sheet_name='三级部位结构')
        
        # 特别关注之前有问题的记录
        problem_indices = [3, 148]  # 对应"颅脑(卒中）"和"下腹部(结肠空腹低张]"
        
        print("\n   🔧 修复的问题记录:")
        for idx in problem_indices:
            if idx < len(original_df):
                original_text = original_df.loc[idx, '三级部位']
                processed_text = df_processed.loc[idx, '三级部位']
                disease_info = df_processed.loc[idx, '疾病信息']
                base_name = df_processed.loc[idx, '基础部位名称']
                
                print(f"      行{idx+1}: \"{original_text}\" → \"{processed_text}\"")
                print(f"             基础: \"{base_name}\" | 疾病: \"{disease_info}\"")
        
        # 显示其他样本
        print("\n   📋 其他处理样本:")
        comparison_samples = df_processed[
            (df_processed['疾病信息'] != '') & (df_processed['疾病信息'] != 'nan')
        ][['三级部位', '基础部位名称', '疾病信息', '人群信息']].head(6)
        
        for i, (idx, row) in enumerate(comparison_samples.iterrows(), 1):
            original_text = original_df.loc[idx, '三级部位']
            
            print(f"\n      {i}.")
            print(f"         原始: \"{original_text}\"")
            print(f"         处理: \"{row['三级部位']}\"")
            print(f"         基础: \"{row['基础部位名称']}\"")
            print(f"         疾病: {row['疾病信息']}")
            if row['人群信息'] and row['人群信息'] != 'nan':
                print(f"         人群: {row['人群信息']}")
    
    def save_processed_data(self, df_processed, output_sheet_name='处理后数据V2修复版'):
        """将处理后的数据保存到原始文件的新sheet中"""
        print(f"\n💾 正在保存处理后的数据到新sheet: {output_sheet_name}")
        
        try:
            # 读取原始文件的所有sheet
            with pd.ExcelFile(self.excel_file_path) as xls:
                all_sheets = {}
                for sheet_name in xls.sheet_names:
                    all_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)
            
            # 添加处理后的数据
            all_sheets[output_sheet_name] = df_processed
            
            # 保存到原始文件
            with pd.ExcelWriter(self.excel_file_path, engine='openpyxl', mode='w') as writer:
                for sheet_name, df in all_sheets.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            print(f"✅ 数据已保存到 {self.excel_file_path} 的 '{output_sheet_name}' sheet")
            print(f"   新增列: 疾病信息, 人群信息, 基础部位名称")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            # 备用方案：保存到新文件
            backup_file = self.excel_file_path.replace('.xlsx', '_处理后V2修复版.xlsx')
            with pd.ExcelWriter(backup_file, engine='openpyxl') as writer:
                df_processed.to_excel(writer, sheet_name=output_sheet_name, index=False)
            print(f"   已保存到备用文件: {backup_file}")

def main():
    """主函数"""
    print("🏥 医学检查项目数据处理程序 V2 修复版")
    print("=" * 60)
    print("📋 处理规则:")
    print("   1. 清理空格字符")
    print("   2. 所有括号()（）→ 统一转换为方括号[]")
    print("   3. 提取疾病/人群信息到专门字段")
    print("   4. 保留原始内容，不移除括号信息")
    print("   5. 修复混合括号转换问题")
    print("=" * 60)
    
    # 数据文件路径
    excel_file = '../data/NEW_检查项目名称结构表 (11).xlsx'
    
    try:
        # 创建处理器
        processor = DataProcessorV2Fixed(excel_file)
        
        # 加载数据
        processor.load_data()
        
        # 处理数据
        df_processed = processor.process_data()
        
        # 分析提取结果
        processor.analyze_extraction_results(df_processed)
        
        # 保存处理后的数据
        processor.save_processed_data(df_processed)
        
        print(f"\n🎉 数据处理完成!")
        print(f"   原始数据: {len(processor.df_parts)} 行")
        print(f"   处理后数据: {len(df_processed)} 行 x {len(df_processed.columns)} 列")
        print(f"   新增字段: 疾病信息, 人群信息, 基础部位名称")
        print(f"   处理特点: 保留原始内容，所有括号统一转为方括号")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 