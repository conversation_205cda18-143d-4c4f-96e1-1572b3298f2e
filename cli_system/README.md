# 源代码目录

## 🚀 快速运行

```bash
python run_enhanced_pipeline.py
```

## 📁 核心文件

| 文件名 | 功能说明 |
|--------|----------|
| `run_enhanced_pipeline.py` | 🚀 **主启动脚本** |
| `unified_pipeline.py` | 统一处理Pipeline |
| `medical_project_generator.py` | CT/MR项目生成器 |
| `dr_project_generator.py` | DR项目生成器 |
| `mg_project_processor.py` | MG项目处理器 |
| `medical_insurance_generator.py` | 医保项目生成器 |
| `requirements.txt` | Python依赖文件 |

## 📂 归档目录

- `archive/verification/` - 验证和测试脚本
- `archive/deprecated/` - 已废弃的代码文件

## 📖 完整文档

详细说明请参考根目录的 `README.md` 文件。 