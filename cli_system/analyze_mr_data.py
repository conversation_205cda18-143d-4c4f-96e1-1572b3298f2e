#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def analyze_mr_data():
    """分析MR水成MRLH相关数据"""
    
    # 读取扫描方式医保映射编码表
    print("=== 扫描方式医保映射编码表 ===")
    df_scan = pd.read_excel('data/NEW_检查项目名称结构表 (11).xlsx', sheet_name='扫描方式医保映射编码')
    print(f"总共有 {len(df_scan)} 种扫描方式")
    print("\n扫描方式列表:")
    for idx, row in df_scan.iterrows():
        print(f"{idx+1}. {row['扫描方式']} - 医保映射码: {row['医保映射码']} - 医保扩展码: {row['医保扩展码']}")
    
    # 查找MR水成MRLH相关的扫描方式
    print("\n=== MR水成MRLH相关扫描方式 ===")
    mr_water_scans = df_scan[df_scan['扫描方式'].str.contains('水成|MRLH', na=False)]
    if not mr_water_scans.empty:
        print("找到以下MR水成相关扫描方式:")
        for idx, row in mr_water_scans.iterrows():
            print(f"- {row['扫描方式']} (医保映射码: {row['医保映射码']}, 医保扩展码: {row['医保扩展码']})")
    else:
        print("未找到MR水成MRLH相关的扫描方式")
    
    # 读取三级部位结构表
    print("\n=== 三级部位结构表分析 ===")
    df_parts = pd.read_excel('data/NEW_检查项目名称结构表 (11).xlsx', sheet_name='三级部位结构')
    print(f"总共有 {len(df_parts)} 个部位")
    
    # 查找所有MR相关的列
    mr_cols = [col for col in df_parts.columns if 'MR' in col]
    print(f"\nMR相关列共 {len(mr_cols)} 个:")
    for col in mr_cols:
        print(f"- {col}")
    
    # 查找MR水成MRLH相关的列
    print("\n=== MR水成MRLH列分析 ===")
    mrlh_cols = [col for col in mr_cols if 'MRLH' in col or '水成' in col]
    if mrlh_cols:
        print(f"找到 {len(mrlh_cols)} 个MR水成MRLH相关列:")
        for col in mrlh_cols:
            print(f"\n列名: {col}")
            value_counts = df_parts[col].value_counts()
            print(f"数值分布: {value_counts.to_dict()}")
            
            # 查看哪些部位启用了这个扫描方式
            enabled_parts = df_parts[df_parts[col] == 1]
            if not enabled_parts.empty:
                print(f"启用该扫描方式的部位 ({len(enabled_parts)} 个):")
                for idx, part in enabled_parts.iterrows():
                    print(f"  - {part['三级部位']} (部位编码: {part['部位编码']})")
            else:
                print("没有部位启用该扫描方式")
    else:
        print("未找到MR水成MRLH相关的列")
    
    # 检查MR适用性
    print("\n=== MR适用性检查 ===")
    if 'MR' in df_parts.columns:
        mr_applicable = df_parts[df_parts['MR'] == '1']
        print(f"适用MR的部位共 {len(mr_applicable)} 个")
        
        # 检查这些部位的MR扫描方式配置
        print("\n适用MR的部位及其扫描方式配置:")
        for idx, part in mr_applicable.head(10).iterrows():  # 只显示前10个
            part_name = part['三级部位']
            enabled_scans = []
            for col in mr_cols:
                if col != 'MR' and col != 'MR_适用' and part.get(col) == 1:
                    enabled_scans.append(col)
            print(f"  - {part_name}: {', '.join(enabled_scans) if enabled_scans else '无启用的MR扫描方式'}")
    
if __name__ == "__main__":
    analyze_mr_data()