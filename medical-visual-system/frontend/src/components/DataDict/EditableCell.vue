<template>
  <div class="editable-cell" :class="{ 'is-editing': isEditing }">
    <div v-if="!isEditing" class="cell-display" @click="startEdit" @dblclick="startEdit">
      {{ displayValue || placeholder }}
    </div>
    <div v-else class="cell-editor">
      <el-input
        v-if="type === 'text'"
        v-model="localValue"
        size="small"
        @blur="saveEdit"
        @keyup.enter="saveEdit"
        @keyup.esc="cancelEdit"
        ref="inputRef"
      />
      <el-input-number
        v-else-if="type === 'number'"
        v-model="localValue"
        size="small"
        :controls="false"
        @blur="saveEdit"
        @keyup.enter="saveEdit"
        @keyup.esc="cancelEdit"
        ref="inputRef"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'

// Props定义
interface Props {
  modelValue: string | number
  rowIndex: number
  field: string
  type?: 'text' | 'number'
  placeholder?: string
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  placeholder: '点击编辑',
  readonly: false
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  'change': [rowIndex: number, field: string, value: string | number]
}>()

// 响应式数据
const isEditing = ref(false)
const localValue = ref(props.modelValue)
const inputRef = ref()

// 计算属性
const displayValue = computed(() => {
  if (props.type === 'number' && typeof props.modelValue === 'number') {
    return props.modelValue.toString()
  }
  return props.modelValue || ''
})

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  localValue.value = newValue
})

// 开始编辑
const startEdit = () => {
  if (props.readonly) return
  
  isEditing.value = true
  localValue.value = props.modelValue
  
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus()
      if (inputRef.value.$el && inputRef.value.$el.querySelector('input')) {
        inputRef.value.$el.querySelector('input').select()
      }
    }
  })
}

// 保存编辑
const saveEdit = () => {
  isEditing.value = false
  
  let finalValue = localValue.value
  if (props.type === 'number') {
    finalValue = Number(localValue.value) || 0
  }
  
  if (finalValue !== props.modelValue) {
    emit('update:modelValue', finalValue)
    emit('change', props.rowIndex, props.field, finalValue)
  }
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  localValue.value = props.modelValue
}
</script>

<style scoped>
.editable-cell {
  width: 100%;
  height: 100%;
  position: relative;
}

.cell-display {
  padding: 4px 8px;
  min-height: 20px;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all 0.2s;
  font-size: 12px;
  line-height: 1.4;
}

.cell-display:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
}

.cell-display:empty::before {
  content: attr(data-placeholder);
  color: #c0c4cc;
  font-style: italic;
}

.cell-editor {
  width: 100%;
}

.is-editing .cell-display {
  display: none;
}

/* 输入框样式优化 */
:deep(.el-input__wrapper) {
  padding: 2px 6px;
  min-height: 24px;
}

:deep(.el-input__inner) {
  font-size: 12px;
  line-height: 1.4;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number__wrapper) {
  padding: 2px 6px;
}
</style> 