<template>
  <div class="modality-dict">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <div class="operation-row">
        <el-space>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增模态
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-space>
        
        <el-space>
          <el-switch
            v-model="showInactiveOnly"
            @change="handleFilterChange"
            active-text="显示全部"
            inactive-text="仅启用"
          />
          <el-tag type="info" effect="dark">共 {{ total }} 条记录</el-tag>
        </el-space>
      </div>
      
      <div class="filter-row">
        <el-space wrap>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索模态名称、描述或映射码"
            style="width: 250px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select
            v-model="selectedMapping"
            placeholder="按映射码筛选"
            clearable
            @change="handleMappingFilter"
            style="width: 150px"
          >
            <el-option
              v-for="mapping in mappingOptions"
              :key="mapping"
              :label="mapping"
              :value="mapping"
            />
          </el-select>
          
          <el-button
            type="danger"
            plain
            @click="clearAllFilters"
            :disabled="!hasActiveFilters"
          >
            <el-icon><Delete /></el-icon>
            清空筛选
          </el-button>
        </el-space>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-section">
      <el-row :gutter="15">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ total }}</div>
            <div class="stat-label">总计模态</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ activeCount }}</div>
            <div class="stat-label">启用模态</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ defaultMappingCount }}</div>
            <div class="stat-label">默认映射</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ mappingOptions.length }}</div>
            <div class="stat-label">映射类型</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      border
      style="width: 100%"
      :row-class-name="getRowClassName"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column prop="mapping_code_prefix" label="映射码前缀" width="140">
        <template #default="{ row }">
          <el-tag :type="getTagType(row.mapping_code_prefix)" effect="dark" size="large">
            <el-icon><Grid /></el-icon>
            {{ row.mapping_code_prefix }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="modality_name" label="模态名称" width="120">
        <template #default="{ row }">
          <div class="modality-name">
            <el-icon>{{ getModalityIcon(row.modality_name) }}</el-icon>
            <span>{{ row.modality_name }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="modality_desc" label="模态描述" min-width="200">
        <template #default="{ row }">
          <el-text>{{ row.modality_desc }}</el-text>
        </template>
      </el-table-column>
      
      <el-table-column prop="default_mapping" label="默认映射" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="row.default_mapping ? 'success' : 'info'" effect="dark">
            <el-icon>{{ row.default_mapping ? '<Star />' : '<StarFilled />' }}</el-icon>
            {{ row.default_mapping ? '默认' : '非默认' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="sort_order" label="排序" width="80" align="center">
        <template #default="{ row }">
          <el-tag size="small" type="info">{{ row.sort_order }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="is_active" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'" effect="dark">
            {{ row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          <el-text type="info" size="small">
            <el-icon><Clock /></el-icon>
            {{ formatDateTime(row.created_at) }}
          </el-text>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button-group>
            <el-button link type="primary" size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button link type="primary" size="small" @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页信息 -->
    <div class="pagination-container">
      <div class="pagination-info">
        <el-text>共 {{ total }} 条记录</el-text>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Refresh, Download, Search, Delete, Grid, Clock,
  View, Edit, Monitor, Camera, VideoCamera, Star, StarFilled
} from '@element-plus/icons-vue'
import { dataDictApi } from '@/utils/api'

// 定义模态数据类型
interface ModalityItem {
  id: number
  mapping_code_prefix: string
  modality_name: string
  modality_desc: string
  default_mapping: boolean
  sort_order: number
  is_active: boolean
  created_at: string
}

// 响应式数据
const loading = ref(false)
const tableData = ref<ModalityItem[]>([])
const total = ref(0)
const showInactiveOnly = ref(false)
const selectedRows = ref<ModalityItem[]>([])
const searchKeyword = ref('')
const selectedMapping = ref('')
const mappingOptions = ref<string[]>([])

// 计算属性
const activeCount = computed(() => 
  tableData.value.filter(item => item.is_active).length
)

const defaultMappingCount = computed(() => 
  tableData.value.filter(item => item.default_mapping).length
)

const hasActiveFilters = computed(() => {
  return searchKeyword.value || selectedMapping.value
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await dataDictApi.getModalities(!showInactiveOnly.value)
    const { data, total: totalCount, message } = response.data
    
    let filteredData = data
    
    // 客户端搜索筛选
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      filteredData = filteredData.filter((item: ModalityItem) =>
        item.modality_name.toLowerCase().includes(keyword) ||
        item.modality_desc.toLowerCase().includes(keyword) ||
        item.mapping_code_prefix.toLowerCase().includes(keyword)
      )
    }
    
    if (selectedMapping.value) {
      filteredData = filteredData.filter((item: ModalityItem) =>
        item.mapping_code_prefix === selectedMapping.value
      )
    }
    
    tableData.value = filteredData
    total.value = filteredData.length
    
    // 提取映射码选项
    const mappingSet = new Set((data as ModalityItem[]).map((item: ModalityItem) => item.mapping_code_prefix))
    mappingOptions.value = Array.from(mappingSet).sort()
    
    ElMessage.success(message)
  } catch (error) {
    console.error('获取模态字典失败:', error)
    ElMessage.error('获取模态字典失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchData()
}

// 筛选条件变更
const handleFilterChange = () => {
  fetchData()
}

const handleSearch = () => {
  fetchData()
}

const handleMappingFilter = () => {
  fetchData()
}

const clearAllFilters = () => {
  searchKeyword.value = ''
  selectedMapping.value = ''
  fetchData()
}

// 选择变更
const handleSelectionChange = (selection: ModalityItem[]) => {
  selectedRows.value = selection
}

const getRowClassName = ({ row }: { row: ModalityItem }) => {
  if (!row.is_active) return 'inactive-row'
  return ''
}

// 新增
const handleAdd = () => {
  ElMessage.info('新增功能开发中...')
}

// 编辑
const handleEdit = (row: ModalityItem) => {
  ElMessage.info(`编辑模态: ${row.modality_name}`)
}

// 查看详情
const handleView = (row: ModalityItem) => {
  ElMessage.info(`查看模态详情: ${row.modality_name}`)
}

// 删除
const handleDelete = async (row: ModalityItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模态 "${row.modality_name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    ElMessage.info('删除功能开发中...')
  } catch {
    ElMessage.info('已取消删除')
  }
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 获取标签类型
const getTagType = (prefix: string) => {
  const typeMap: Record<string, string> = {
    '2a': 'success',
    '2b': 'success',
    '3a': 'warning',
    '3b': 'warning', 
    '3c': 'warning',
    '11': 'info',
    '13': 'danger',
    '12': 'default'
  }
  return typeMap[prefix] || 'default'
}

// 获取模态图标
const getModalityIcon = (modality: string) => {
  const iconMap: Record<string, any> = {
    'CT': Monitor,
    'MR': VideoCamera,
    'DR': Camera,
    'MG': View,
    'RF': VideoCamera
  }
  return iconMap[modality] || Grid
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  try {
    return new Date(dateStr).toLocaleString('zh-CN')
  } catch {
    return dateStr
  }
}

// 暴露给父组件的方法
defineExpose({
  refreshData
})

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.modality-dict {
  padding: 0;
}

.operation-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.operation-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  align-items: center;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.modality-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
}

.pagination-info {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

:deep(.el-table .inactive-row) {
  background-color: var(--el-fill-color-light);
  opacity: 0.6;
}

:deep(.el-table .inactive-row td) {
  color: var(--el-text-color-disabled);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .operation-row {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .stats-section .el-col {
    margin-bottom: 10px;
  }
}
</style> 