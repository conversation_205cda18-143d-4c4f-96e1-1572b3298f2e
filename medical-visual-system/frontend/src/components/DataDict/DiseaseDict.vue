<template>
  <div class="disease-dict">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-row :gutter="20" justify="space-between">
        <el-col :span="12">
          <el-space>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增疾病
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-space>
        </el-col>
        <el-col :span="12">
          <el-row justify="end">
            <el-space>
              <el-switch
                v-model="showInactiveOnly"
                @change="handleFilterChange"
                active-text="显示全部"
                inactive-text="仅启用"
              />
              <el-tag type="info">共 {{ total }} 条记录</el-tag>
            </el-space>
          </el-row>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column prop="disease_code" label="疾病编码" width="120">
        <template #default="{ row }">
          <el-tag type="warning">{{ row.disease_code }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="disease_name" label="疾病名称" width="200" />
      <el-table-column prop="description" label="描述" min-width="200" />
      
      <el-table-column prop="sort_order" label="排序" width="80" align="center" />
      
      <el-table-column prop="is_active" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'">
            {{ row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button link type="danger" size="small" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { dataDictApi } from '@/utils/api'

interface DiseaseItem {
  id: number
  disease_code: string
  disease_name: string
  description: string
  sort_order: number
  is_active: boolean
  created_at: string
}

const loading = ref(false)
const tableData = ref<DiseaseItem[]>([])
const total = ref(0)
const showInactiveOnly = ref(false)

const fetchData = async () => {
  loading.value = true
  try {
    const response = await dataDictApi.getDiseases(!showInactiveOnly.value)
    const { data, total: totalCount, message } = response.data
    
    tableData.value = data
    total.value = totalCount
    
    ElMessage.success(message)
  } catch (error) {
    console.error('获取疾病字典失败:', error)
    ElMessage.error('获取疾病字典失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  fetchData()
}

const handleFilterChange = () => {
  fetchData()
}

const handleAdd = () => {
  ElMessage.info('新增功能开发中...')
}

const handleEdit = (row: DiseaseItem) => {
  ElMessage.info(`编辑疾病: ${row.disease_name}`)
}

const handleDelete = async (row: DiseaseItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除疾病 "${row.disease_name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    ElMessage.info('删除功能开发中...')
  } catch {
    ElMessage.info('已取消删除')
  }
}

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  try {
    return new Date(dateStr).toLocaleString('zh-CN')
  } catch {
    return dateStr
  }
}

defineExpose({
  refreshData
})

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.disease-dict {
  padding: 0;
}

.operation-bar {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
}

:deep(.el-table) {
  border-radius: 6px;
}

:deep(.el-table th) {
  background-color: var(--el-fill-color-light);
}
</style> 