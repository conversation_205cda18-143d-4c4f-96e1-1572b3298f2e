<template>
  <div class="body-parts-main">
    <!-- 紧凑统计栏 -->
    <div class="compact-stats">
      <div class="stats-row">
        <div class="stat-item">
          <el-icon class="stat-icon"><Grid /></el-icon>
          <span class="stat-label">总计:</span>
          <span class="stat-value">{{ totalPartsCount }}</span>
        </div>
        <div class="stat-item">
          <el-icon class="stat-icon"><FirstAidKit /></el-icon>
          <span class="stat-label">一级:</span>
          <span class="stat-value">{{ level1Count }}</span>
        </div>
        <div class="stat-item ct-stat">
          <el-icon class="stat-icon"><Monitor /></el-icon>
          <span class="stat-label">CT:</span>
          <span class="stat-value">{{ modalityStats.ct }}</span>
        </div>
        <div class="stat-item mr-stat">
          <el-icon class="stat-icon"><Magnet /></el-icon>
          <span class="stat-label">MR:</span>
          <span class="stat-value">{{ modalityStats.mr }}</span>
        </div>
        <div class="stat-item dr-stat">
          <el-icon class="stat-icon"><Camera /></el-icon>
          <span class="stat-label">DR:</span>
          <span class="stat-value">{{ modalityStats.dr }}</span>
        </div>
        <div class="stat-item other-stat">
          <el-icon class="stat-icon"><More /></el-icon>
          <span class="stat-label">其他:</span>
          <span class="stat-value">{{ modalityStats.rf + modalityStats.mg + modalityStats.ot }}</span>
        </div>
      </div>
    </div>

    <!-- 合并的操作和搜索栏 -->
    <div class="unified-toolbar">
      <div class="toolbar-left">
        <el-space size="small">
          <el-button type="primary" size="small" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增部位
          </el-button>
          <el-button size="small" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button size="small" @click="handleBatchEdit" :disabled="selectedRows.length === 0">
            <el-icon><Edit /></el-icon>
            批量编辑 ({{ selectedRows.length }})
          </el-button>
          <el-button size="small" @click="handleImport">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
          <el-button size="small" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-space>
      </div>
      
      <div class="toolbar-right">
        <el-space size="small">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索部位名称、编码"
            clearable
            size="small"
            style="width: 200px"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select
            v-model="selectedLevel1"
            placeholder="一级部位"
            clearable
            size="small"
            style="width: 120px"
            @change="handleLevel1Filter"
          >
            <el-option
              v-for="level1 in level1Options"
              :key="level1.code"
              :label="`${level1.code}-${level1.name}`"
              :value="level1.code"
            />
          </el-select>
          
          <el-select
            v-model="selectedModality"
            placeholder="模态筛选"
            clearable
            size="small"
            style="width: 100px"
            @change="handleModalityFilter"
          >
            <el-option label="CT" value="CT" />
            <el-option label="MR" value="MR" />
            <el-option label="DR" value="DR" />
            <el-option label="RF" value="RF" />
            <el-option label="MG" value="MG" />
            <el-option label="其他" value="OT" />
          </el-select>
          
          <el-select
            v-model="sortBy"
            size="small"
            style="width: 100px"
            @change="handleSortChange"
          >
            <el-option label="编码排序" value="code" />
            <el-option label="名称排序" value="name" />
            <el-option label="创建时间" value="created" />
          </el-select>
          
          <el-radio-group v-model="sortOrder" size="small" @change="handleSortChange">
            <el-radio-button value="asc">升序</el-radio-button>
            <el-radio-button value="desc">降序</el-radio-button>
          </el-radio-group>
          
          <el-switch
            v-model="showInactive"
            active-text="全部"
            inactive-text="启用"
            size="small"
            @change="handleFilterChange"
          />
          
          <el-button
            type="danger"
            plain
            size="small"
            @click="clearAllFilters"
            :disabled="!hasActiveFilters"
          >
            清空
          </el-button>
        </el-space>
      </div>
    </div>

    <!-- 简化数据表格 -->
    <div class="simple-table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        size="small"
        :row-class-name="getRowClassName"
        @selection-change="handleSelectionChange"
        @row-dblclick="handleRowDoubleClick"
        height="calc(100vh - 200px)"
        style="width: 100%"
      >
        <el-table-column type="selection" width="40" fixed="left" />
        <el-table-column type="index" label="#" width="50" fixed="left" />
        
        <!-- 一级编码 -->
        <el-table-column prop="level1_code" label="一级编码" min-width="80" fixed="left" sortable show-overflow-tooltip>
          <template #default="{ row }">
            <span class="simple-code level1-code">{{ row.level1_code }}</span>
          </template>
        </el-table-column>
        
        <!-- 一级部位 -->
        <el-table-column prop="level1_name" label="一级部位" min-width="100" fixed="left" sortable show-overflow-tooltip>
          <template #default="{ row }">
            <span class="simple-text level1-text">{{ row.level1_name }}</span>
          </template>
        </el-table-column>
        
        <!-- 二级编码 -->
        <el-table-column prop="level2_code" label="二级编码" min-width="80" sortable show-overflow-tooltip>
          <template #default="{ row }">
            <span class="simple-code level2-code">{{ row.level2_code }}</span>
          </template>
        </el-table-column>
        
        <!-- 二级部位 -->
        <el-table-column prop="level2_name" label="二级部位" min-width="100" sortable show-overflow-tooltip>
          <template #default="{ row }">
            <span class="simple-text level2-text">{{ row.level2_name }}</span>
          </template>
        </el-table-column>
        
        <!-- 三级编码 -->
        <el-table-column prop="level3_code" label="三级编码" min-width="80" sortable show-overflow-tooltip>
          <template #default="{ row }">
            <span class="simple-code level3-code">{{ row.level3_code }}</span>
          </template>
        </el-table-column>
        
        <!-- 三级部位 -->
        <el-table-column prop="level3_name" label="三级部位" min-width="120" sortable show-overflow-tooltip>
          <template #default="{ row }">
            <span class="simple-text level3-text">{{ row.level3_name }}</span>
          </template>
        </el-table-column>
        
        <!-- 部位编码 -->
        <el-table-column prop="part_code" label="部位编码" min-width="90" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="simple-code part-code">{{ row.part_code }}</span>
          </template>
        </el-table-column>
        
        <!-- CT适用 -->
        <el-table-column label="CT" width="50" align="center">
          <template #default="{ row }">
            <span :class="['simple-status', row.is_applicable_ct ? 'status-yes' : 'status-no']">
              {{ row.is_applicable_ct ? '✓' : '-' }}
            </span>
          </template>
        </el-table-column>
        
        <!-- MR适用 -->
        <el-table-column label="MR" width="50" align="center">
          <template #default="{ row }">
            <span :class="['simple-status', row.is_applicable_mr ? 'status-yes' : 'status-no']">
              {{ row.is_applicable_mr ? '✓' : '-' }}
            </span>
          </template>
        </el-table-column>
        
        <!-- DR适用 -->
        <el-table-column label="DR" width="50" align="center">
          <template #default="{ row }">
            <span :class="['simple-status', row.is_applicable_dr ? 'status-yes' : 'status-no']">
              {{ row.is_applicable_dr ? '✓' : '-' }}
            </span>
          </template>
        </el-table-column>
        
        <!-- RF适用 -->
        <el-table-column label="RF" width="50" align="center">
          <template #default="{ row }">
            <span :class="['simple-status', row.is_applicable_rf ? 'status-yes' : 'status-no']">
              {{ row.is_applicable_rf ? '✓' : '-' }}
            </span>
          </template>
        </el-table-column>
        
        <!-- MG适用 -->
        <el-table-column label="MG" width="50" align="center">
          <template #default="{ row }">
            <span :class="['simple-status', row.is_applicable_mg ? 'status-yes' : 'status-no']">
              {{ row.is_applicable_mg ? '✓' : '-' }}
            </span>
          </template>
        </el-table-column>
        
        <!-- 其他模态 -->
        <el-table-column label="其他" width="50" align="center">
          <template #default="{ row }">
            <span :class="['simple-status', row.is_applicable_ot ? 'status-yes' : 'status-no']">
              {{ row.is_applicable_ot ? '✓' : '-' }}
            </span>
          </template>
        </el-table-column>
        
        <!-- 拼音码 -->
        <el-table-column prop="pinyin_code" label="拼音码" min-width="80" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="simple-text extra-text">{{ row.pinyin_code || '-' }}</span>
          </template>
        </el-table-column>
        
        <!-- 英文名 -->
        <el-table-column prop="english_name" label="英文名" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="simple-text extra-text">{{ row.english_name || '-' }}</span>
          </template>
        </el-table-column>
        
        <!-- 操作 -->
        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                type="primary" 
                size="small" 
                @click="handleEdit(row)"
                :icon="Edit"
                circle
                title="修改"
              />
              <el-button 
                type="success" 
                size="small" 
                @click="handleSave(row)"
                :icon="Check"
                circle
                title="保存"
              />
              <el-button 
                type="danger" 
                size="small" 
                @click="handleDelete(row)"
                :icon="Delete"
                circle
                title="删除"
              />
              <el-button 
                type="info" 
                size="small" 
                @click="handleCopy(row)"
                :icon="CopyDocument"
                circle
                title="复制"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 紧凑分页 -->
    <div class="compact-pagination">
      <div class="pagination-info">
        显示 {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, totalCount) }} / 共 {{ totalCount }} 条
      </div>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 50, 100, 200]"
        :small="true"
        :disabled="loading"
        :background="true"
        layout="sizes, prev, pager, next, jumper"
        :total="totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 编辑对话框 -->
    <BodyPartEditDialog
      v-model="showEditDialog"
      :body-part="editingBodyPart"
      :mode="editMode"
      @confirm="handleEditConfirm"
      @cancel="handleEditCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Refresh, Edit, Upload, Download, Search, Delete, 
  ArrowRight, Location, Monitor, Camera, VideoCamera, 
  View, CopyDocument, More, Grid, FirstAidKit, Magnet, Check
} from '@element-plus/icons-vue'
import { dataDictApi } from '@/utils/api'
import BodyPartEditDialog from '@/components/DataDict/BodyPartEditDialog.vue'

interface BodyPartItem {
  id: number
  level1_code: string
  level1_name: string
  level2_code: string
  level2_name: string
  level3_code: string
  level3_name: string
  part_code: string
  part_code_text?: string
  pinyin_code?: string
  english_name?: string
  modality_applicability: {
    ct: boolean
    mr: boolean
    dr: boolean
    rf: boolean
    mg: boolean
    ot: boolean
  }
  applicable_modalities: string[]
  is_applicable_ct?: boolean  // 为了兼容性保留
  is_applicable_mr?: boolean
  is_applicable_dr?: boolean
  is_applicable_rf?: boolean
  is_applicable_mg?: boolean
  is_applicable_ot?: boolean
  sort_order?: number
  is_active: boolean
  created_at: string
}

interface Level1Option {
  code: string
  name: string
}

// 响应式数据
const loading = ref(false)
const tableData = ref<BodyPartItem[]>([])
const totalCount = ref(0)  // 分页数据总数
const totalPartsCount = ref(0)  // 全量部位总数
const currentPage = ref(1)
const pageSize = ref(50)
const showInactive = ref(false)
const searchKeyword = ref('')
const selectedLevel1 = ref('')
const selectedModality = ref('')
const selectedRows = ref<BodyPartItem[]>([])
const viewMode = ref('flat') // tree, flat, group
const sortBy = ref('code')
const sortOrder = ref('asc')

// 编辑相关
const showEditDialog = ref(false)
const editingBodyPart = ref<BodyPartItem | null>(null)
const editMode = ref<'add' | 'edit' | 'view'>('view')

// 统计数据
const modalityStats = ref({
  ct: 0,
  mr: 0,
  dr: 0,
  rf: 0,
  mg: 0,
  ot: 0
})

const level1Options = ref<Level1Option[]>([])

// 计算属性
const level1Count = computed(() => level1Options.value.length)

const hasActiveFilters = computed(() => {
  return searchKeyword.value || selectedLevel1.value || selectedModality.value
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      activeOnly: !showInactive.value,
      modality: selectedModality.value || undefined,
      level1: selectedLevel1.value || undefined,
      search: searchKeyword.value || undefined
    }
    
    // 并行获取表格数据和统计数据
    const [response, statsResponse] = await Promise.all([
      dataDictApi.getBodyParts(params),
      dataDictApi.getBodyPartsModalityStats()
    ])
    
    const { data, pagination, message } = response.data
    const { data: statsData } = statsResponse.data
    
    // 更新统计信息（使用后端统计数据）
    // 分页数据的总数用于分页器显示
    totalCount.value = pagination?.total || data.length
    
    // 模态统计数据（基于全量数据）
    modalityStats.value = {
      ct: statsData.modality_stats.CT,
      mr: statsData.modality_stats.MR,
      dr: statsData.modality_stats.DR,
      rf: statsData.modality_stats.RF,
      mg: statsData.modality_stats.MG,
      ot: statsData.modality_stats.OT
    }
    
    // 全量部位总数用于统计栏显示
    totalPartsCount.value = statsData.total_parts
    
    // 转换数据结构，添加兼容性字段
    const convertedData = data.map((item: any) => ({
      ...item,
      is_applicable_ct: item.modality_applicability.ct,
      is_applicable_mr: item.modality_applicability.mr,
      is_applicable_dr: item.modality_applicability.dr,
      is_applicable_rf: item.modality_applicability.rf,
      is_applicable_mg: item.modality_applicability.mg,
      is_applicable_ot: item.modality_applicability.ot,
      pinyin_code: item.pinyin_code || '',
      english_name: item.english_name || ''
    }))
    
    // 排序数据
    let sortedData = [...convertedData]
    if (sortBy.value === 'code') {
      sortedData.sort((a, b) => {
        const codeA = `${a.level1_code}${a.level2_code}${a.level3_code}${a.part_code}`
        const codeB = `${b.level1_code}${b.level2_code}${b.level3_code}${b.part_code}`
        return sortOrder.value === 'asc' ? codeA.localeCompare(codeB) : codeB.localeCompare(codeA)
      })
    } else if (sortBy.value === 'name') {
      sortedData.sort((a, b) => {
        const nameA = `${a.level1_name}${a.level2_name}${a.level3_name}`
        const nameB = `${b.level1_name}${b.level2_name}${b.level3_name}`
        return sortOrder.value === 'asc' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA)
      })
    }
    
    tableData.value = sortedData
    
    // 提取一级部位选项
    const level1Map = new Map()
    sortedData.forEach((item: BodyPartItem) => {
      const key = item.level1_code
      if (!level1Map.has(key)) {
        level1Map.set(key, {
          code: item.level1_code,
          name: item.level1_name
        })
      }
    })
    level1Options.value = Array.from(level1Map.values()).sort((a, b) => a.code.localeCompare(b.code))
    
    ElMessage.success(message)
  } catch (error) {
    console.error('获取部位数据失败:', error)
    ElMessage.error('获取部位数据失败')
  } finally {
    loading.value = false
  }
}

// 统计信息现在直接从后端API获取，不再需要本地计算

// 事件处理
const refreshData = () => {
  fetchData()
}

const handleFilterChange = () => {
  currentPage.value = 1
  fetchData()
}

const handleLevel1Filter = () => {
  currentPage.value = 1
  fetchData()
}

const handleModalityFilter = () => {
  currentPage.value = 1
  fetchData()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const handleSortChange = () => {
  fetchData()
}



const clearAllFilters = () => {
  searchKeyword.value = ''
  selectedLevel1.value = ''
  selectedModality.value = ''
  currentPage.value = 1
  fetchData()
}

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchData()
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  fetchData()
}

const handleSelectionChange = (selection: BodyPartItem[]) => {
  selectedRows.value = selection
}

const handleRowClick = (row: BodyPartItem) => {
  console.log('点击行:', row)
}

const handleRowDoubleClick = (row: BodyPartItem) => {
  editingBodyPart.value = { ...row }
  editMode.value = 'view'
  showEditDialog.value = true
}

const getRowClassName = ({ row }: { row: BodyPartItem }) => {
  if (!row.is_active) return 'inactive-row'
  return ''
}

// CRUD操作
const handleAdd = () => {
  editingBodyPart.value = null
  editMode.value = 'add'
  showEditDialog.value = true
}

const handleEdit = (row: BodyPartItem) => {
  editingBodyPart.value = { ...row }
  editMode.value = 'edit'
  showEditDialog.value = true
}

const handleView = (row: BodyPartItem) => {
  editingBodyPart.value = { ...row }
  editMode.value = 'view'
  showEditDialog.value = true
}

const handleCopy = (row: BodyPartItem) => {
  editingBodyPart.value = { 
    ...row, 
    id: 0, // 重置ID表示新增
    level3_name: `${row.level3_name} - 副本`
  }
  editMode.value = 'add'
  showEditDialog.value = true
}

const handleSave = async (row: BodyPartItem) => {
  try {
    // TODO: 实现保存API调用
    ElMessage.success(`保存部位 "${row.level3_name}" 成功`)
    refreshData()
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleDelete = async (row: BodyPartItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除部位 "${row.level3_name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    ElMessage.info('删除功能开发中...')
    // TODO: 实现删除API
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleBatchEdit = () => {
  ElMessage.info(`批量编辑 ${selectedRows.value.length} 条记录功能开发中...`)
}

const handleImport = () => {
  ElMessage.info('导入功能开发中...')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleEditConfirm = (bodyPart: any) => {
  if (editMode.value === 'add') {
    ElMessage.success('新增部位成功')
  } else {
    ElMessage.success('编辑部位成功')
  }
  showEditDialog.value = false
  refreshData()
}

const handleEditCancel = () => {
  showEditDialog.value = false
}

// 暴露给父组件的方法
defineExpose({
  refreshData
})

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.body-parts-main {
  padding: 8px;
  background-color: #fafbfc;
  min-height: 100vh;
}

/* 紧凑统计栏 */
.compact-stats {
  margin-bottom: 12px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.stats-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: white;
  font-size: 13px;
  min-width: 80px;
}

.stat-item .stat-icon {
  font-size: 16px;
  opacity: 0.9;
}

.stat-label {
  font-weight: 500;
  opacity: 0.9;
}

.stat-value {
  font-weight: 700;
  font-size: 14px;
}

.ct-stat .stat-value { color: #10b981; }
.mr-stat .stat-value { color: #f59e0b; }
.dr-stat .stat-value { color: #3b82f6; }
.other-stat .stat-value { color: #e5e7eb; }

/* 统一工具栏 */
.unified-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  gap: 20px;
  flex-wrap: wrap;
}

.toolbar-left {
  flex: 0 0 auto;
}

.toolbar-right {
  flex: 1 1 auto;
  display: flex;
  justify-content: flex-end;
}

/* 简化表格样式 */
.simple-table-container {
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.simple-code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.level1-code { 
  background-color: #dcfce7; 
  color: #166534; 
  border-color: #bbf7d0; 
}

.level2-code { 
  background-color: #fef3c7; 
  color: #92400e; 
  border-color: #fde68a; 
}

.level3-code { 
  background-color: #dbeafe; 
  color: #1e40af; 
  border-color: #bfdbfe; 
}

.part-code { 
  background-color: #f5f5f5; 
  color: #4b5563; 
  border-color: #d1d5db; 
}

.simple-text {
  font-size: 13px;
  color: #374151;
  font-weight: 500;
}

.level1-text { 
  color: #059669; 
  font-weight: 600; 
}

.level2-text { 
  color: #d97706; 
  font-weight: 600; 
}

.level3-text { 
  color: #2563eb; 
  font-weight: 600; 
}

.extra-text {
  color: #6b7280;
  font-size: 12px;
}

.simple-status {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  text-align: center;
  min-width: 20px;
  display: inline-block;
}

.status-yes {
  color: #059669;
  background-color: #dcfce7;
  border: 1px solid #bbf7d0;
}

.status-no {
  color: #9ca3af;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
}

.status-active {
  color: #059669;
  background-color: #dcfce7;
  border: 1px solid #bbf7d0;
}

.status-inactive {
  color: #dc2626;
  background-color: #fee2e2;
  border: 1px solid #fecaca;
}

/* 紧凑分页 */
.compact-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding: 8px 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-info {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.stat-card {
  padding: 20px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
.stat-card.level1 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; }
.stat-card.ct { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; }
.stat-card.mr { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; }
.stat-card.dr { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; }
.stat-card.other { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333; }

.stat-icon {
  font-size: 32px;
  opacity: 0.9;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 工具栏 */
.toolbar {
  margin-bottom: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.search-filter-bar {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

/* 表格优化为小尺寸 */
:deep(.el-table--small .el-table__cell) {
  padding: 6px 8px;
}

:deep(.el-table--small) {
  font-size: 12px;
}

/* 简化表格样式 */
:deep(.el-table) {
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

:deep(.el-table th) {
  background-color: #f8fafc;
  color: #374151;
  font-weight: 600;
  font-size: 12px;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-table .inactive-row) {
  background-color: #f9fafb;
  opacity: 0.8;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  width: 28px;
  height: 28px;
  padding: 0;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.action-buttons .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.action-buttons .el-button.is-circle {
  border-radius: 50%;
}

/* 操作按钮颜色优化 */
.action-buttons .el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #3a8af7 100%);
}

.action-buttons .el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #5daf34 100%);
}

.action-buttons .el-button--danger {
  background: linear-gradient(135deg, #f56c6c 0%, #f34848 100%);
}

.action-buttons .el-button--info {
  background: linear-gradient(135deg, #909399 0%, #7d8289 100%);
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .unified-toolbar {
    flex-direction: column;
    gap: 12px;
  }
  
  .toolbar-right {
    justify-content: flex-start;
  }
  
  .stats-row {
    justify-content: flex-start;
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .body-parts-main {
    padding: 4px;
  }
  
  .compact-stats {
    padding: 6px 12px;
  }
  
  .stats-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    min-width: auto;
  }
  
  .unified-toolbar {
    padding: 6px 8px;
  }
  
  .compact-pagination {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  /* 移动端操作按钮优化 */
  .action-buttons {
    gap: 2px;
  }
  
  .action-buttons .el-button {
    width: 24px;
    height: 24px;
  }
}



</style> 