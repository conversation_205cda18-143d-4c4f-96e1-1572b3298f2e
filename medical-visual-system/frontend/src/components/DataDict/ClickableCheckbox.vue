<template>
  <div class="clickable-checkbox" @click="toggle">
    <div class="checkbox-display" :class="checkboxClass">
      <el-icon v-if="modelValue" class="check-icon"><Check /></el-icon>
      <el-icon v-else class="close-icon"><Close /></el-icon>
      <span v-if="showText" class="status-text">{{ statusText }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Check, Close } from '@element-plus/icons-vue'

// Props定义
interface Props {
  modelValue: boolean
  rowIndex: number
  field: string
  activeText?: string
  inactiveText?: string
  showText?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  activeText: '✓',
  inactiveText: '-',
  showText: false,
  readonly: false
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'change': [rowIndex: number, field: string, value: boolean]
}>()

// 计算属性
const checkboxClass = computed(() => ({
  'is-checked': props.modelValue,
  'is-unchecked': !props.modelValue,
  'is-readonly': props.readonly
}))

const statusText = computed(() => {
  return props.modelValue ? props.activeText : props.inactiveText
})

// 切换状态
const toggle = () => {
  if (props.readonly) return
  
  const newValue = !props.modelValue
  emit('update:modelValue', newValue)
  emit('change', props.rowIndex, props.field, newValue)
}
</script>

<style scoped>
.clickable-checkbox {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.checkbox-display {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: all 0.2s;
  font-size: 12px;
  font-weight: 600;
}

.checkbox-display:hover:not(.is-readonly) {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.checkbox-display.is-checked {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.checkbox-display.is-unchecked {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: white;
}

.checkbox-display.is-readonly {
  cursor: not-allowed;
  opacity: 0.6;
}

.check-icon {
  font-size: 12px;
  font-weight: bold;
}

.close-icon {
  font-size: 10px;
  font-weight: bold;
}

.status-text {
  margin-left: 4px;
  font-size: 12px;
}

/* 动画效果 */
.checkbox-display {
  position: relative;
  overflow: hidden;
}

.checkbox-display::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.checkbox-display:active::before {
  width: 40px;
  height: 40px;
}
</style> 