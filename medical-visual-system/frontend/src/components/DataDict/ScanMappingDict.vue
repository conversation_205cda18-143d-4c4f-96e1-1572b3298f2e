<template>
  <div class="scan-mapping-dict">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-row :gutter="20" justify="space-between">
        <el-col :span="16">
          <el-space>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增扫描方式
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-select
              v-model="selectedModality"
              placeholder="按模态筛选"
              clearable
              @change="handleModalityFilter"
              style="width: 150px"
            >
              <el-option
                v-for="modality in modalityOptions"
                :key="modality"
                :label="modality"
                :value="modality"
              />
            </el-select>
          </el-space>
        </el-col>
        <el-col :span="8">
          <el-row justify="end">
            <el-space>
              <el-switch
                v-model="showInactiveOnly"
                @change="handleFilterChange"
                active-text="显示全部"
                inactive-text="仅启用"
              />
              <el-tag type="info">共 {{ total }} 条记录</el-tag>
            </el-space>
          </el-row>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column prop="modality" label="模态" width="80">
        <template #default="{ row }">
          <el-tag :type="getModalityTagType(row.modality)">
            {{ row.modality }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="scan_method" label="扫描方式" min-width="150" />
      <el-table-column prop="scan_method_short" label="简称" width="100" />
      
      <el-table-column prop="insurance_mapping_code" label="医保映射码" width="120">
        <template #default="{ row }">
          <el-tag v-if="row.insurance_mapping_code" type="success">
            {{ row.insurance_mapping_code }}
          </el-tag>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="insurance_extension_code" label="医保扩展码" width="120">
        <template #default="{ row }">
          <el-tag v-if="row.insurance_extension_code" type="warning">
            {{ row.insurance_extension_code }}
          </el-tag>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="contrast_agent" label="对比剂" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.contrast_agent ? 'danger' : 'info'">
            {{ row.contrast_agent ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="applicable_parts" label="适用部位" min-width="200">
        <template #default="{ row }">
          <span v-if="row.applicable_parts" class="text-small">
            {{ row.applicable_parts }}
          </span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="sort_order" label="排序" width="80" align="center" />
      
      <el-table-column prop="is_active" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'">
            {{ row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button link type="danger" size="small" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { dataDictApi } from '@/utils/api'

interface ScanMappingItem {
  id: number
  scan_method: string
  scan_method_short: string
  insurance_mapping_code: string
  insurance_extension_code: string
  modality: string
  applicable_parts: string
  contrast_agent: boolean
  sort_order: number
  is_active: boolean
  created_at: string
}

const loading = ref(false)
const tableData = ref<ScanMappingItem[]>([])
const total = ref(0)
const showInactiveOnly = ref(false)
const selectedModality = ref('')
const modalityOptions = ref(['CT', 'MR', 'DR', 'MG', 'RF', 'OT'])

const fetchData = async () => {
  loading.value = true
  try {
    const response = await dataDictApi.getScanMappings(
      selectedModality.value || undefined,
      !showInactiveOnly.value
    )
    const { data, total: totalCount, message } = response.data
    
    tableData.value = data
    total.value = totalCount
    
    ElMessage.success(message)
  } catch (error) {
    console.error('获取扫描方式映射失败:', error)
    ElMessage.error('获取扫描方式映射失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  fetchData()
}

const handleFilterChange = () => {
  fetchData()
}

const handleModalityFilter = () => {
  fetchData()
}

const handleAdd = () => {
  ElMessage.info('新增功能开发中...')
}

const handleEdit = (row: ScanMappingItem) => {
  ElMessage.info(`编辑扫描方式: ${row.scan_method}`)
}

const handleDelete = async (row: ScanMappingItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除扫描方式 "${row.scan_method}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    ElMessage.info('删除功能开发中...')
  } catch {
    ElMessage.info('已取消删除')
  }
}

const getModalityTagType = (modality: string) => {
  const typeMap: Record<string, string> = {
    'CT': 'success',
    'MR': 'warning',
    'DR': 'info',
    'MG': 'danger',
    'RF': 'default',
    'OT': 'default'
  }
  return typeMap[modality] || 'default'
}

defineExpose({
  refreshData
})

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.scan-mapping-dict {
  padding: 0;
}

.operation-bar {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
}

.text-small {
  font-size: 12px;
  line-height: 1.2;
}

.text-muted {
  color: var(--el-text-color-placeholder);
}

:deep(.el-table) {
  border-radius: 6px;
}

:deep(.el-table th) {
  background-color: var(--el-fill-color-light);
}
</style> 