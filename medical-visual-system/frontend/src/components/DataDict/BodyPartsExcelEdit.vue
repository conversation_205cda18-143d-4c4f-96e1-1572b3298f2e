<template>
  <div class="body-parts-excel-editor">
    <!-- 顶部工具栏 -->
    <div class="excel-toolbar">
      <div class="toolbar-left">
        <h3>医疗部位数据管理</h3>
        <div class="stats-info">
          <span>总计: {{ totalCount }}</span>
          <span>CT: {{ modalityStats.ct }}</span>
          <span>MR: {{ modalityStats.mr }}</span>
          <span>DR: {{ modalityStats.dr }}</span>
        </div>
      </div>
      <div class="toolbar-right">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="saveChanges" :disabled="!hasChanges">
          <el-icon><Check /></el-icon>
          保存修改
        </el-button>
        <el-button @click="exportData">
          <el-icon><Download /></el-icon>
          导出Excel
        </el-button>
        <el-button @click="importData">
          <el-icon><Upload /></el-icon>
          导入Excel
        </el-button>
      </div>
    </div>

    <!-- Excel编辑器 -->
    <div class="excel-container">
      <vue3-excel-editor
        v-model="excelData"
        :height="editorHeight"
        :readonly="false"
        autocomplete
        filter-row
        @update="onDataUpdate"
        @delete="onDataDelete"
        @select="onRowSelect"
        ref="excelEditor"
      >
        <!-- 定义列结构 -->
        <vue3-excel-column field="level1_code" label="一级编码" type="string" width="80px" readonly />
        <vue3-excel-column field="level1_name" label="一级部位" type="string" width="100px" />
        <vue3-excel-column field="level2_code" label="二级编码" type="string" width="80px" readonly />
        <vue3-excel-column field="level2_name" label="二级部位" type="string" width="120px" />
        <vue3-excel-column field="level3_code" label="三级编码" type="string" width="80px" readonly />
        <vue3-excel-column field="level3_name" label="三级部位" type="string" width="150px" />
        <vue3-excel-column field="part_code" label="部位编码" type="string" width="100px" readonly />
        
        <!-- 模态适用性列 - 使用checkYN类型 -->
        <vue3-excel-column field="is_applicable_ct" label="CT" type="checkYN" width="50px" text-align="center" />
        <vue3-excel-column field="is_applicable_mr" label="MR" type="checkYN" width="50px" text-align="center" />
        <vue3-excel-column field="is_applicable_dr" label="DR" type="checkYN" width="50px" text-align="center" />
        <vue3-excel-column field="is_applicable_rf" label="RF" type="checkYN" width="50px" text-align="center" />
        <vue3-excel-column field="is_applicable_mg" label="MG" type="checkYN" width="50px" text-align="center" />
        <vue3-excel-column field="is_applicable_ot" label="其他" type="checkYN" width="50px" text-align="center" />
        
        <vue3-excel-column field="pinyin_code" label="拼音码" type="string" width="80px" />
        <vue3-excel-column field="english_name" label="英文名" type="string" width="120px" />
        <vue3-excel-column field="sort_order" label="排序" type="number" width="60px" />
        <vue3-excel-column field="is_active" label="状态" type="checkYN" width="60px" text-align="center" />
      </vue3-excel-editor>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <span>共 {{ totalCount }} 条记录</span>
      <span v-if="selectedRows.length > 0">已选择 {{ selectedRows.length }} 行</span>
      <span v-if="hasChanges" class="changes-indicator">● 有未保存的修改</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Check, Download, Upload } from '@element-plus/icons-vue'
import { dataDictApi } from '@/utils/api'

// 导入Vue3 Excel Editor（实际上应该是vue-excel-editor的Vue3版本）
// 注意：搜索结果显示真正的包名是cscan/vue3-excel-editor
// 让我们先用一个简化版本来实现功能

// 数据结构定义
interface BodyPartItem {
  id: number
  level1_code: string
  level1_name: string
  level2_code: string
  level2_name: string
  level3_code: string
  level3_name: string
  part_code: string
  pinyin_code?: string
  english_name?: string
  is_applicable_ct: string // Y/N
  is_applicable_mr: string // Y/N
  is_applicable_dr: string // Y/N
  is_applicable_rf: string // Y/N
  is_applicable_mg: string // Y/N
  is_applicable_ot: string // Y/N
  sort_order: number
  is_active: string // Y/N
  created_at?: string
}

// 响应式数据
const loading = ref(false)
const excelData = ref<BodyPartItem[]>([])
const totalCount = ref(0)
const selectedRows = ref<number[]>([])
const hasChanges = ref(false)
const excelEditor = ref()

// 模态统计
const modalityStats = ref({
  ct: 0,
  mr: 0,
  dr: 0,
  rf: 0,
  mg: 0,
  ot: 0
})

// 编辑器高度
const editorHeight = computed(() => 'calc(100vh - 250px)')

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await dataDictApi.getBodyParts({
      page: 1,
      pageSize: 1000, // 获取所有数据
      activeOnly: false
    })
    
    const { data, pagination } = response.data
    
    // 转换数据格式以适配Excel编辑器
    excelData.value = data.map((item: any) => ({
      ...item,
      is_applicable_ct: item.modality_applicability?.ct ? 'Y' : 'N',
      is_applicable_mr: item.modality_applicability?.mr ? 'Y' : 'N',
      is_applicable_dr: item.modality_applicability?.dr ? 'Y' : 'N',
      is_applicable_rf: item.modality_applicability?.rf ? 'Y' : 'N',
      is_applicable_mg: item.modality_applicability?.mg ? 'Y' : 'N',
      is_applicable_ot: item.modality_applicability?.ot ? 'Y' : 'N',
      is_active: item.is_active ? 'Y' : 'N',
      pinyin_code: item.pinyin_code || '',
      english_name: item.english_name || ''
    }))
    
    totalCount.value = pagination?.total || data.length
    calculateStats()
    hasChanges.value = false
    
    ElMessage.success('数据加载成功')
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 计算统计数据
const calculateStats = () => {
  modalityStats.value = {
    ct: excelData.value.filter(item => item.is_applicable_ct === 'Y').length,
    mr: excelData.value.filter(item => item.is_applicable_mr === 'Y').length,
    dr: excelData.value.filter(item => item.is_applicable_dr === 'Y').length,
    rf: excelData.value.filter(item => item.is_applicable_rf === 'Y').length,
    mg: excelData.value.filter(item => item.is_applicable_mg === 'Y').length,
    ot: excelData.value.filter(item => item.is_applicable_ot === 'Y').length
  }
}

// 刷新数据
const refreshData = async () => {
  if (hasChanges.value) {
    try {
      await ElMessageBox.confirm('有未保存的修改，刷新将丢失这些修改，是否继续？', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    } catch {
      return
    }
  }
  await fetchData()
}

// 数据更新事件
const onDataUpdate = (updates: any[]) => {
  console.log('数据更新:', updates)
  hasChanges.value = true
  calculateStats()
}

// 数据删除事件
const onDataDelete = (deletes: any[]) => {
  console.log('数据删除:', deletes)
  hasChanges.value = true
  calculateStats()
}

// 行选择事件
const onRowSelect = (selected: number[]) => {
  selectedRows.value = selected
}

// 保存修改
const saveChanges = async () => {
  if (!hasChanges.value) {
    ElMessage.info('没有需要保存的修改')
    return
  }
  
  loading.value = true
  try {
    // 转换数据格式回后端格式
    const updateData = excelData.value.map(item => ({
      ...item,
      modality_applicability: {
        ct: item.is_applicable_ct === 'Y',
        mr: item.is_applicable_mr === 'Y',
        dr: item.is_applicable_dr === 'Y',
        rf: item.is_applicable_rf === 'Y',
        mg: item.is_applicable_mg === 'Y',
        ot: item.is_applicable_ot === 'Y'
      },
      is_active: item.is_active === 'Y'
    }))
    
    // TODO: 实现批量保存API
    console.log('保存数据:', updateData)
    
    hasChanges.value = false
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 导出数据
const exportData = () => {
  if (excelEditor.value) {
    excelEditor.value.exportTable('xlsx', false, '医疗部位数据')
    ElMessage.success('导出成功')
  }
}

// 导入数据
const importData = () => {
  if (excelEditor.value) {
    excelEditor.value.importTable((data: any[]) => {
      excelData.value = data
      hasChanges.value = true
      calculateStats()
      ElMessage.success('导入成功')
    })
  }
}

// 组件挂载
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.body-parts-excel-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.excel-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.toolbar-left h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.stats-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #606266;
}

.stats-info span {
  padding: 4px 8px;
  background: #f0f2f5;
  border-radius: 4px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.excel-container {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-top: 1px solid #e4e7ed;
  font-size: 12px;
  color: #909399;
}

.changes-indicator {
  color: #f56c6c;
  font-weight: 500;
}

/* Vue3 Excel Editor 自定义样式 */
:deep(.vue-excel-editor) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.vue-excel-editor .excel-table) {
  font-size: 13px;
}

:deep(.vue-excel-editor .excel-header) {
  background: #f8f9fa;
  font-weight: 600;
}

:deep(.vue-excel-editor .excel-cell) {
  border-right: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.vue-excel-editor .excel-cell:focus) {
  border: 2px solid #409eff;
  background: #ecf5ff;
}

/* 模态适用性列样式 */
:deep(.vue-excel-editor .excel-cell[data-field*="is_applicable"]) {
  text-align: center;
  font-weight: 600;
}

:deep(.vue-excel-editor .excel-cell[data-field*="is_applicable"][data-value="Y"]) {
  background: #f0f9ff;
  color: #409eff;
}

:deep(.vue-excel-editor .excel-cell[data-field*="is_applicable"][data-value="N"]) {
  background: #fef0f0;
  color: #f56c6c;
}
</style> 