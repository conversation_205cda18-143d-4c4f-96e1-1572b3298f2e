<template>
  <div class="body-parts-editable-table">
    <!-- 顶部工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <h3>医疗部位数据管理 - 可编辑表格</h3>
        <div class="stats-info">
          <span>总计: {{ totalCount }}</span>
          <span>CT: {{ modalityStats.ct }}</span>
          <span>MR: {{ modalityStats.mr }}</span>
          <span>DR: {{ modalityStats.dr }}</span>
        </div>
      </div>
      <div class="toolbar-right">
        <el-button @click="refreshData" :loading="loading" size="small">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="saveAllChanges" :disabled="!hasChanges" size="small">
          <el-icon><Check /></el-icon>
          保存修改 ({{ changedRows.size }})
        </el-button>
        <el-button @click="batchEditMode = !batchEditMode" size="small" :type="batchEditMode ? 'warning' : 'default'">
          <el-icon><Edit /></el-icon>
          {{ batchEditMode ? '退出批量编辑' : '批量编辑' }}
        </el-button>
        <el-button @click="exportData" size="small">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 批量编辑工具栏 -->
    <div v-if="batchEditMode && selectedRows.length > 0" class="batch-toolbar">
      <span>已选择 {{ selectedRows.length }} 行</span>
      <div class="batch-actions">
        <span>批量设置：</span>
        <el-button size="small" @click="batchSetModality('ct', true)">启用CT</el-button>
        <el-button size="small" @click="batchSetModality('ct', false)">禁用CT</el-button>
        <el-button size="small" @click="batchSetModality('mr', true)">启用MR</el-button>
        <el-button size="small" @click="batchSetModality('mr', false)">禁用MR</el-button>
        <el-button size="small" @click="batchSetModality('dr', true)">启用DR</el-button>
        <el-button size="small" @click="batchSetModality('dr', false)">禁用DR</el-button>
      </div>
    </div>

    <!-- 可编辑表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        :height="tableHeight"
        size="small"
        border
        stripe
        row-key="id"
        @selection-change="handleSelectionChange"
        @cell-click="handleCellClick"
        @cell-dblclick="handleCellDoubleClick"
        class="editable-table"
      >
        <!-- 批量编辑模式下的选择列 -->
        <el-table-column v-if="batchEditMode" type="selection" width="40" fixed="left" />

        <!-- 基本信息列 -->
        <el-table-column prop="level1_code" label="一级编码" width="80" fixed="left" sortable />
        <el-table-column prop="level1_name" label="一级部位" width="100" fixed="left" sortable />
        <el-table-column prop="level2_code" label="二级编码" width="80" sortable />
        <el-table-column prop="level2_name" label="二级部位" width="120" sortable>
          <template #default="{ row, $index }">
            <editable-cell
              v-model="row.level2_name"
              :row-index="$index"
              field="level2_name"
              @change="onCellChange"
            />
          </template>
        </el-table-column>
        <el-table-column prop="level3_code" label="三级编码" width="80" sortable />
        <el-table-column prop="level3_name" label="三级部位" width="150" sortable>
          <template #default="{ row, $index }">
            <editable-cell
              v-model="row.level3_name"
              :row-index="$index"
              field="level3_name"
              @change="onCellChange"
            />
          </template>
        </el-table-column>

        <!-- 模态适用性列 - 可点击切换 -->
        <el-table-column label="CT" width="60" align="center">
          <template #default="{ row, $index }">
            <clickable-checkbox
              v-model="row.is_applicable_ct"
              :row-index="$index"
              field="is_applicable_ct"
              @change="onCellChange"
            />
          </template>
        </el-table-column>
        <el-table-column label="MR" width="60" align="center">
          <template #default="{ row, $index }">
            <clickable-checkbox
              v-model="row.is_applicable_mr"
              :row-index="$index"
              field="is_applicable_mr"
              @change="onCellChange"
            />
          </template>
        </el-table-column>
        <el-table-column label="DR" width="60" align="center">
          <template #default="{ row, $index }">
            <clickable-checkbox
              v-model="row.is_applicable_dr"
              :row-index="$index"
              field="is_applicable_dr"
              @change="onCellChange"
            />
          </template>
        </el-table-column>
        <el-table-column label="RF" width="60" align="center">
          <template #default="{ row, $index }">
            <clickable-checkbox
              v-model="row.is_applicable_rf"
              :row-index="$index"
              field="is_applicable_rf"
              @change="onCellChange"
            />
          </template>
        </el-table-column>
        <el-table-column label="MG" width="60" align="center">
          <template #default="{ row, $index }">
            <clickable-checkbox
              v-model="row.is_applicable_mg"
              :row-index="$index"
              field="is_applicable_mg"
              @change="onCellChange"
            />
          </template>
        </el-table-column>
        <el-table-column label="其他" width="60" align="center">
          <template #default="{ row, $index }">
            <clickable-checkbox
              v-model="row.is_applicable_ot"
              :row-index="$index"
              field="is_applicable_ot"
              @change="onCellChange"
            />
          </template>
        </el-table-column>

        <!-- 其他可编辑列 -->
        <el-table-column prop="pinyin_code" label="拼音码" width="100">
          <template #default="{ row, $index }">
            <editable-cell
              v-model="row.pinyin_code"
              :row-index="$index"
              field="pinyin_code"
              @change="onCellChange"
            />
          </template>
        </el-table-column>
        <el-table-column prop="english_name" label="英文名" width="150">
          <template #default="{ row, $index }">
            <editable-cell
              v-model="row.english_name"
              :row-index="$index"
              field="english_name"
              @change="onCellChange"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sort_order" label="排序" width="80">
          <template #default="{ row, $index }">
            <editable-cell
              v-model="row.sort_order"
              :row-index="$index"
              field="sort_order"
              type="number"
              @change="onCellChange"
            />
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row, $index }">
            <clickable-checkbox
              v-model="row.is_active"
              :row-index="$index"
              field="is_active"
              active-text="启用"
              inactive-text="禁用"
              @change="onCellChange"
            />
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row, $index }">
            <div class="action-buttons">
              <el-button
                v-if="changedRows.has($index)"
                size="small"
                type="success"
                @click="saveRowChanges($index)"
                :loading="saving"
              >
                保存
              </el-button>
              <el-button
                v-if="changedRows.has($index)"
                size="small"
                @click="revertRowChanges($index)"
              >
                撤销
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span>共 {{ totalCount }} 条记录</span>
        <span v-if="changedRows.size > 0" class="changes-indicator">
          ● {{ changedRows.size }} 行有未保存修改
        </span>
      </div>
      <div class="status-right">
        <span v-if="selectedRows.length > 0">已选择 {{ selectedRows.length }} 行</span>
        <span v-if="lastSaveTime">最后保存：{{ lastSaveTime }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Check, Edit, Download } from '@element-plus/icons-vue'
import { dataDictApi } from '@/utils/api'

// 子组件
import EditableCell from './EditableCell.vue'
import ClickableCheckbox from './ClickableCheckbox.vue'

// 数据结构定义
interface BodyPartItem {
  id: number
  level1_code: string
  level1_name: string
  level2_code: string
  level2_name: string
  level3_code: string
  level3_name: string
  part_code: string
  pinyin_code: string
  english_name: string
  is_applicable_ct: boolean
  is_applicable_mr: boolean
  is_applicable_dr: boolean
  is_applicable_rf: boolean
  is_applicable_mg: boolean
  is_applicable_ot: boolean
  sort_order: number
  is_active: boolean
  created_at?: string
}

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const tableData = ref<BodyPartItem[]>([])
const originalData = ref<BodyPartItem[]>([])
const totalCount = ref(0)
const selectedRows = ref<BodyPartItem[]>([])
const changedRows = ref(new Set<number>())
const batchEditMode = ref(false)
const lastSaveTime = ref('')

// 模态统计
const modalityStats = ref({
  ct: 0,
  mr: 0,
  dr: 0,
  rf: 0,
  mg: 0,
  ot: 0
})

// 计算属性
const hasChanges = computed(() => changedRows.value.size > 0)
const tableHeight = computed(() => 'calc(100vh - 280px)')

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await dataDictApi.getBodyParts({
      page: 1,
      pageSize: 1000,
      activeOnly: false
    })
    
    const { data, pagination } = response.data
    
    // 转换数据格式
    const convertedData = data.map((item: any) => ({
      ...item,
      is_applicable_ct: item.modality_applicability?.ct || false,
      is_applicable_mr: item.modality_applicability?.mr || false,
      is_applicable_dr: item.modality_applicability?.dr || false,
      is_applicable_rf: item.modality_applicability?.rf || false,
      is_applicable_mg: item.modality_applicability?.mg || false,
      is_applicable_ot: item.modality_applicability?.ot || false,
      pinyin_code: item.pinyin_code || '',
      english_name: item.english_name || ''
    }))
    
    tableData.value = convertedData
    originalData.value = JSON.parse(JSON.stringify(convertedData))
    totalCount.value = pagination?.total || data.length
    
    calculateStats()
    changedRows.value.clear()
    
    ElMessage.success('数据加载成功')
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 计算统计数据
const calculateStats = () => {
  modalityStats.value = {
    ct: tableData.value.filter(item => item.is_applicable_ct).length,
    mr: tableData.value.filter(item => item.is_applicable_mr).length,
    dr: tableData.value.filter(item => item.is_applicable_dr).length,
    rf: tableData.value.filter(item => item.is_applicable_rf).length,
    mg: tableData.value.filter(item => item.is_applicable_mg).length,
    ot: tableData.value.filter(item => item.is_applicable_ot).length
  }
}

// 刷新数据
const refreshData = async () => {
  if (hasChanges.value) {
    try {
      await ElMessageBox.confirm('有未保存的修改，刷新将丢失这些修改，是否继续？', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    } catch {
      return
    }
  }
  await fetchData()
}

// 单元格变化处理
const onCellChange = (rowIndex: number, field: string, newValue: any) => {
  changedRows.value.add(rowIndex)
  calculateStats()
}

// 处理选择变化
const handleSelectionChange = (selection: BodyPartItem[]) => {
  selectedRows.value = selection
}

// 处理单元格点击
const handleCellClick = (row: any, column: any, cell: any, event: any) => {
  // 可以在这里添加单元格点击逻辑
}

// 处理双击编辑
const handleCellDoubleClick = (row: any, column: any, cell: any, event: any) => {
  // 双击进入编辑模式
}

// 批量设置模态适用性
const batchSetModality = (modality: string, enabled: boolean) => {
  selectedRows.value.forEach((row) => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      const field = `is_applicable_${modality}` as keyof BodyPartItem
      ;(tableData.value[index] as any)[field] = enabled
      changedRows.value.add(index)
    }
  })
  calculateStats()
  ElMessage.success(`已批量${enabled ? '启用' : '禁用'}${modality.toUpperCase()}`)
}

// 保存单行修改
const saveRowChanges = async (rowIndex: number) => {
  saving.value = true
  try {
    const row = tableData.value[rowIndex]
    // TODO: 实现单行保存API
    console.log('保存单行:', row)
    
    changedRows.value.delete(rowIndex)
    originalData.value[rowIndex] = JSON.parse(JSON.stringify(row))
    
    ElMessage.success('保存成功')
    updateLastSaveTime()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 撤销单行修改
const revertRowChanges = (rowIndex: number) => {
  const original = originalData.value[rowIndex]
  if (original) {
    tableData.value[rowIndex] = JSON.parse(JSON.stringify(original))
    changedRows.value.delete(rowIndex)
    calculateStats()
    ElMessage.success('已撤销修改')
  }
}

// 保存所有修改
const saveAllChanges = async () => {
  if (!hasChanges.value) {
    ElMessage.info('没有需要保存的修改')
    return
  }
  
  saving.value = true
  try {
    const changedData = Array.from(changedRows.value).map(index => {
      const row = tableData.value[index]
      return {
        ...row,
        modality_applicability: {
          ct: row.is_applicable_ct,
          mr: row.is_applicable_mr,
          dr: row.is_applicable_dr,
          rf: row.is_applicable_rf,
          mg: row.is_applicable_mg,
          ot: row.is_applicable_ot
        }
      }
    })
    
    // TODO: 实现批量保存API
    console.log('批量保存:', changedData)
    
    changedRows.value.clear()
    originalData.value = JSON.parse(JSON.stringify(tableData.value))
    
    ElMessage.success(`成功保存 ${changedData.length} 条修改`)
    updateLastSaveTime()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 导出数据
const exportData = () => {
  // TODO: 实现导出功能
  ElMessage.success('导出功能待实现')
}

// 更新最后保存时间
const updateLastSaveTime = () => {
  lastSaveTime.value = new Date().toLocaleString()
}

// 组件挂载
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.body-parts-editable-table {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.toolbar-left h3 {
  margin: 0 0 6px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.stats-info {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #606266;
}

.stats-info span {
  padding: 2px 6px;
  background: #f0f2f5;
  border-radius: 3px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #fff7e6;
  border-bottom: 1px solid #ffd591;
  font-size: 13px;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-container {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.editable-table {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-top: 1px solid #e4e7ed;
  font-size: 12px;
  color: #909399;
}

.status-left {
  display: flex;
  gap: 12px;
}

.status-right {
  display: flex;
  gap: 12px;
}

.changes-indicator {
  color: #f56c6c;
  font-weight: 500;
}

/* 表格自定义样式 */
:deep(.el-table .cell) {
  padding: 0 4px;
  font-size: 12px;
}

:deep(.el-table .el-table__cell) {
  padding: 4px 0;
}

:deep(.el-table .changed-row) {
  background-color: #fff7e6;
}

:deep(.el-table .changed-cell) {
  background-color: #ecf5ff;
  border: 1px solid #b3d8ff;
}
</style> 