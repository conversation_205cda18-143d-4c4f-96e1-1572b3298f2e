<template>
  <div class="data-dict-container">
    <!-- 标题栏 -->
    <div class="page-header">
      <h2>数据字典管理</h2>
      <div class="header-actions">
        <el-button @click="toggleDrawer" type="primary">
          <el-icon><Menu /></el-icon>
          {{ showDrawer ? '隐藏' : '显示' }}字典管理
        </el-button>
      </div>
    </div>

    <div class="content-layout">
      <!-- 主内容区 -->
      <div class="main-content" :class="{ 'full-width': !showDrawer }">
        <!-- 选项卡 -->
        <el-tabs v-model="activeTab" class="main-tabs">
          <el-tab-pane label="标准表格视图" name="standard">
            <BodyPartsMain />
          </el-tab-pane>
          <el-tab-pane label="可编辑表格" name="editable">
            <BodyPartsEditableTable />
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 侧边抽屉 -->
      <el-drawer
        v-model="showDrawer"
        title="数据字典管理"
        :size="360"
        direction="rtl"
        class="dict-drawer"
      >
        <!-- 抽屉内容 -->
        <div class="drawer-content">
          <!-- 模态字典 -->
          <div class="dict-section">
            <h3>检查模态</h3>
            <div class="dict-items">
              <el-tag v-for="item in modalityDict" :key="item.id" class="dict-tag">
                {{ item.name }}
              </el-tag>
            </div>
          </div>

          <!-- 人群字典 -->
          <div class="dict-section">
            <h3>目标人群</h3>
            <div class="dict-items">
              <el-tag v-for="item in populationDict" :key="item.id" class="dict-tag" type="success">
                {{ item.name }}
              </el-tag>
            </div>
          </div>

          <!-- 疾病字典 -->
          <div class="dict-section">
            <h3>疾病分类</h3>
            <div class="dict-items">
              <el-tag v-for="item in diseaseDict" :key="item.id" class="dict-tag" type="warning">
                {{ item.name }}
              </el-tag>
            </div>
          </div>

          <!-- 扫描映射 -->
          <div class="dict-section">
            <h3>扫描映射</h3>
            <div class="dict-items">
              <el-tag v-for="item in scanMappingDict" :key="item.id" class="dict-tag" type="info">
                {{ item.description }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Menu } from '@element-plus/icons-vue'
import { dataDictApi } from '@/utils/api'
import BodyPartsMain from './BodyPartsMain.vue'
import BodyPartsEditableTable from './BodyPartsEditableTable.vue'

// 响应式数据
const showDrawer = ref(true)
const activeTab = ref('editable') // 默认显示可编辑表格
const modalityDict = ref<any[]>([])
const populationDict = ref<any[]>([])
const diseaseDict = ref<any[]>([])
const scanMappingDict = ref<any[]>([])

// 切换抽屉
const toggleDrawer = () => {
  showDrawer.value = !showDrawer.value
}

// 获取字典数据
const fetchDictData = async () => {
  try {
    // 获取所有字典数据
    const [modalityRes, populationRes, diseaseRes, scanMappingRes] = await Promise.all([
      dataDictApi.getModalities(),
      dataDictApi.getPopulations(),
      dataDictApi.getDiseases(),
      dataDictApi.getScanMappings()
    ])

    modalityDict.value = modalityRes.data.data || []
    populationDict.value = populationRes.data.data || []
    diseaseDict.value = diseaseRes.data.data || []
    scanMappingDict.value = scanMappingRes.data.data || []
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 组件挂载
onMounted(() => {
  fetchDictData()
})
</script>

<style scoped>
.data-dict-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.content-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
  overflow: hidden;
}

.main-content.full-width {
  margin-right: 0;
}

.main-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
}

.main-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow: hidden;
}

.dict-drawer {
  z-index: 1000;
}

.drawer-content {
  padding: 16px;
}

.dict-section {
  margin-bottom: 24px;
}

.dict-section h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.dict-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dict-tag {
  margin: 0;
  cursor: pointer;
  transition: all 0.2s;
}

.dict-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 抽屉样式覆盖 */
:deep(.el-drawer__header) {
  margin-bottom: 16px;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-drawer__body) {
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 12px 16px;
  }
  
  .page-header h2 {
    font-size: 18px;
  }
  
  .dict-drawer {
    width: 100% !important;
  }
}
</style> 