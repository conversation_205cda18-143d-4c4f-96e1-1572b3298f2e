<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
      :disabled="mode === 'view'"
      @submit.prevent
    >
      <el-row :gutter="20">
        <!-- 一级部位信息 -->
        <el-col :span="24">
          <el-divider content-position="left">
            <el-text type="primary" size="large" tag="b">一级部位信息</el-text>
          </el-divider>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="一级编码" prop="level1_code">
            <el-input v-model="formData.level1_code" placeholder="请输入一级编码" maxlength="1" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="一级部位名称" prop="level1_name">
            <el-input v-model="formData.level1_name" placeholder="请输入一级部位名称" maxlength="50" />
          </el-form-item>
        </el-col>
        
        <!-- 二级部位信息 -->
        <el-col :span="24">
          <el-divider content-position="left">
            <el-text type="primary" size="large" tag="b">二级部位信息</el-text>
          </el-divider>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="二级编码" prop="level2_code">
            <el-input v-model="formData.level2_code" placeholder="请输入二级编码" maxlength="2" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="二级部位名称" prop="level2_name">
            <el-input v-model="formData.level2_name" placeholder="请输入二级部位名称" maxlength="50" />
          </el-form-item>
        </el-col>
        
        <!-- 三级部位信息 -->
        <el-col :span="24">
          <el-divider content-position="left">
            <el-text type="primary" size="large" tag="b">三级部位信息</el-text>
          </el-divider>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="三级编码" prop="level3_code">
            <el-input v-model="formData.level3_code" placeholder="请输入三级编码" maxlength="2" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="三级部位名称" prop="level3_name">
            <el-input v-model="formData.level3_name" placeholder="请输入三级部位名称" maxlength="100" />
          </el-form-item>
        </el-col>
        
        <!-- 部位编码和扩展信息 -->
        <el-col :span="24">
          <el-divider content-position="left">
            <el-text type="primary" size="large" tag="b">扩展信息</el-text>
          </el-divider>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="部位编码" prop="part_code">
            <el-input v-model="formData.part_code" placeholder="请输入部位编码" maxlength="10" />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="拼音码" prop="pinyin_code">
            <el-input v-model="formData.pinyin_code" placeholder="请输入拼音码" maxlength="20" />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="排序顺序" prop="sort_order">
            <el-input-number 
              v-model="formData.sort_order" 
              :min="0" 
              :max="9999" 
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="24">
          <el-form-item label="英文名称" prop="english_name">
            <el-input v-model="formData.english_name" placeholder="请输入英文名称" maxlength="200" />
          </el-form-item>
        </el-col>
        
        <!-- 模态适用性 -->
        <el-col :span="24">
          <el-divider content-position="left">
            <el-text type="primary" size="large" tag="b">模态适用性</el-text>
          </el-divider>
        </el-col>
        
        <el-col :span="24">
          <el-form-item label="适用模态">
            <el-space wrap>
              <el-checkbox v-model="formData.is_applicable_ct">
                <el-tag type="success" effect="dark">CT</el-tag>
                计算机断层扫描
              </el-checkbox>
              <el-checkbox v-model="formData.is_applicable_mr">
                <el-tag type="warning" effect="dark">MR</el-tag>
                磁共振成像
              </el-checkbox>
              <el-checkbox v-model="formData.is_applicable_dr">
                <el-tag type="info" effect="dark">DR</el-tag>
                数字化X线摄影
              </el-checkbox>
              <el-checkbox v-model="formData.is_applicable_rf">
                <el-tag type="default" effect="dark">RF</el-tag>
                透视造影
              </el-checkbox>
              <el-checkbox v-model="formData.is_applicable_mg">
                <el-tag type="danger" effect="dark">MG</el-tag>
                乳腺摄影
              </el-checkbox>
              <el-checkbox v-model="formData.is_applicable_ot">
                <el-tag type="default">OT</el-tag>
                其他模态
              </el-checkbox>
            </el-space>
          </el-form-item>
        </el-col>
        
        <!-- 状态 -->
        <el-col :span="24">
          <el-form-item label="状态">
            <el-switch
              v-model="formData.is_active"
              active-text="启用"
              inactive-text="禁用"
              :active-value="true"
              :inactive-value="false"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button v-if="mode === 'view'" type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-button v-else type="primary" @click="handleConfirm" :loading="submitting">
          <el-icon><Check /></el-icon>
          {{ mode === 'add' ? '新增' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { Edit, Check } from '@element-plus/icons-vue'

interface BodyPartFormData {
  id: number
  level1_code: string
  level1_name: string
  level2_code: string
  level2_name: string
  level3_code: string
  level3_name: string
  part_code: string
  part_code_text?: string
  pinyin_code?: string
  english_name?: string
  modality_applicability?: {
    ct: boolean
    mr: boolean
    dr: boolean
    rf: boolean
    mg: boolean
    ot: boolean
  }
  applicable_modalities?: string[]
  is_applicable_ct?: boolean
  is_applicable_mr?: boolean
  is_applicable_dr?: boolean
  is_applicable_rf?: boolean
  is_applicable_mg?: boolean
  is_applicable_ot?: boolean
  sort_order?: number
  is_active: boolean
  created_at?: string
}

interface Props {
  modelValue: boolean
  bodyPart?: BodyPartFormData | null
  mode: 'add' | 'edit' | 'view'
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', bodyPart: BodyPartFormData): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  bodyPart: null,
  mode: 'view'
})

const emit = defineEmits<Emits>()

// 表单相关
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 初始表单数据
const getInitialFormData = (): BodyPartFormData => ({
  id: 0,
  level1_code: '',
  level1_name: '',
  level2_code: '',
  level2_name: '',
  level3_code: '',
  level3_name: '',
  part_code: '',
  part_code_text: '',
  pinyin_code: '',
  english_name: '',
  modality_applicability: {
    ct: false,
    mr: false,
    dr: false,
    rf: false,
    mg: false,
    ot: false
  },
  applicable_modalities: [],
  is_applicable_ct: false,
  is_applicable_mr: false,
  is_applicable_dr: false,
  is_applicable_rf: false,
  is_applicable_mg: false,
  is_applicable_ot: false,
  sort_order: 0,
  is_active: true,
  created_at: ''
})

const formData = ref<BodyPartFormData>(getInitialFormData())

// 表单验证规则
const formRules: FormRules = {
  level1_code: [
    { required: true, message: '请输入一级编码', trigger: 'blur' },
    { pattern: /^[0-9A-Z]$/, message: '一级编码必须为1位数字或字母', trigger: 'blur' }
  ],
  level1_name: [
    { required: true, message: '请输入一级部位名称', trigger: 'blur' },
    { max: 50, message: '一级部位名称不能超过50个字符', trigger: 'blur' }
  ],
  level2_code: [
    { required: true, message: '请输入二级编码', trigger: 'blur' },
    { pattern: /^[0-9A-Z]{2}$/, message: '二级编码必须为2位数字或字母', trigger: 'blur' }
  ],
  level2_name: [
    { required: true, message: '请输入二级部位名称', trigger: 'blur' },
    { max: 50, message: '二级部位名称不能超过50个字符', trigger: 'blur' }
  ],
  level3_code: [
    { required: true, message: '请输入三级编码', trigger: 'blur' },
    { pattern: /^[0-9A-Z]{2}$/, message: '三级编码必须为2位数字或字母', trigger: 'blur' }
  ],
  level3_name: [
    { required: true, message: '请输入三级部位名称', trigger: 'blur' },
    { max: 100, message: '三级部位名称不能超过100个字符', trigger: 'blur' }
  ],
  part_code: [
    { required: true, message: '请输入部位编码', trigger: 'blur' },
    { max: 10, message: '部位编码不能超过10个字符', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add': return '新增部位'
    case 'edit': return '编辑部位'
    case 'view': return '查看部位详情'
    default: return '部位信息'
  }
})

// 监听props变化
watch(
  () => props.bodyPart,
  (newValue) => {
    if (newValue) {
      formData.value = { ...newValue }
    } else {
      formData.value = getInitialFormData()
    }
  },
  { immediate: true, deep: true }
)

watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) {
      // 对话框关闭时重置表单
      if (formRef.value) {
        formRef.value.resetFields()
      }
    }
  }
)

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
  emit('cancel')
}

const handleEdit = () => {
  // 从查看模式切换到编辑模式
  emit('update:modelValue', false)
  setTimeout(() => {
    // 使用setTimeout确保对话框先关闭再重新打开
    emit('confirm', { ...formData.value })
  }, 100)
}

const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    // 验证表单
    await formRef.value.validate()
    
    submitting.value = true
    
    // 验证至少选择一种模态
    const hasModality = (formData.value.is_applicable_ct || formData.value.modality_applicability?.ct) ||
                       (formData.value.is_applicable_mr || formData.value.modality_applicability?.mr) ||
                       (formData.value.is_applicable_dr || formData.value.modality_applicability?.dr) ||
                       (formData.value.is_applicable_rf || formData.value.modality_applicability?.rf) ||
                       (formData.value.is_applicable_mg || formData.value.modality_applicability?.mg) ||
                       (formData.value.is_applicable_ot || formData.value.modality_applicability?.ot)
    
    if (!hasModality) {
      ElMessage.error('请至少选择一种适用的模态')
      return
    }
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    emit('confirm', { ...formData.value })
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单内容')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.el-checkbox) {
  margin-right: 20px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-checkbox__label) {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 表单项间距优化 */
:deep(.el-form-item) {
  margin-bottom: 22px;
}

/* 标签组样式 */
:deep(.el-tag) {
  font-weight: 600;
  font-size: 12px;
}

/* 数字输入框样式 */
:deep(.el-input-number) {
  width: 100%;
}

/* 开关样式 */
:deep(.el-switch) {
  --el-switch-on-color: var(--el-color-success);
  --el-switch-off-color: var(--el-color-info);
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95vw !important;
    margin: 20px auto;
  }
  
  .el-col {
    margin-bottom: 10px;
  }
}
</style> 