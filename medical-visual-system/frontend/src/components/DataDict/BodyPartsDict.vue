<template>
  <div class="body-parts-dict">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-row :gutter="20">
        <el-col :span="24">
          <!-- 第一行：主要操作 -->
          <div class="operation-row">
            <el-space>
              <el-button type="primary" @click="handleAdd">
                <el-icon><Plus /></el-icon>
                新增部位
              </el-button>
              <el-button @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
              <el-button @click="handleExport">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
              <el-button @click="handleImport">
                <el-icon><Upload /></el-icon>
                导入
              </el-button>
            </el-space>
            
            <el-space>
              <el-switch
                v-model="showInactiveOnly"
                @change="handleFilterChange"
                active-text="显示全部"
                inactive-text="仅启用"
              />
              <el-tag type="info" effect="dark">共 {{ total }} 条记录</el-tag>
            </el-space>
          </div>
          
          <!-- 第二行：搜索和筛选 -->
          <div class="filter-row">
            <el-space wrap>
              <el-input
                v-model="searchKeyword"
                placeholder="搜索部位名称、编码或拼音码"
                style="width: 250px"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              
              <el-select
                v-model="selectedModality"
                placeholder="按模态筛选"
                clearable
                @change="handleModalityFilter"
                style="width: 150px"
              >
                <el-option label="CT适用" value="CT" />
                <el-option label="MR适用" value="MR" />
                <el-option label="DR适用" value="DR" />
                <el-option label="RF适用" value="RF" />
                <el-option label="MG适用" value="MG" />
                <el-option label="其他适用" value="OT" />
              </el-select>
              
              <el-select
                v-model="selectedLevel1"
                placeholder="按一级部位筛选"
                clearable
                @change="handleLevel1Filter"
                style="width: 150px"
              >
                <el-option
                  v-for="level1 in level1Options"
                  :key="level1"
                  :label="level1"
                  :value="level1"
                />
              </el-select>
              
              <el-button
                type="danger"
                plain
                @click="clearAllFilters"
                :disabled="!hasActiveFilters"
              >
                <el-icon><Delete /></el-icon>
                清空筛选
              </el-button>
            </el-space>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-section">
      <el-row :gutter="15">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ total }}</div>
            <div class="stat-label">总计部位</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ level1Count }}</div>
            <div class="stat-label">一级部位</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ modalityStats.ct || 0 }}</div>
            <div class="stat-label">CT适用</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ modalityStats.mr || 0 }}</div>
            <div class="stat-label">MR适用</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      border
      style="width: 100%"
      :row-class-name="getRowClassName"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" fixed="left" />
      <el-table-column prop="id" label="ID" width="80" fixed="left" />
      
      <el-table-column label="部位编码" width="220" fixed="left">
        <template #default="{ row }">
          <div class="code-group">
            <div class="code-row">
              <el-tag size="small" type="success" effect="dark">{{ row.level1_code }}</el-tag>
              <el-tag size="small" type="warning" effect="dark">{{ row.level2_code }}</el-tag>
              <el-tag size="small" type="info" effect="dark">{{ row.level3_code }}</el-tag>
            </div>
            <div class="code-row">
              <el-tag size="small" effect="plain">{{ row.part_code }}</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="部位名称" min-width="250" fixed="left">
        <template #default="{ row }">
          <div class="part-name-group">
            <div class="level1">
              <el-icon><Location /></el-icon>
              {{ row.level1_name }}
            </div>
            <div class="level2">
              <el-icon><ArrowRight /></el-icon>
              {{ row.level2_name }}
            </div>
            <div class="level3">
              <el-icon><ArrowRight /></el-icon>
              {{ row.level3_name }}
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="模态适用性" width="250">
        <template #default="{ row }">
          <div class="modality-applicability">
            <el-tag 
              v-if="row.is_applicable_ct" 
              size="small" 
              type="success" 
              effect="dark"
              class="modality-tag"
            >
              <el-icon><Monitor /></el-icon>
              CT
            </el-tag>
            <el-tag 
              v-if="row.is_applicable_mr" 
              size="small" 
              type="warning" 
              effect="dark"
              class="modality-tag"
            >
              <el-icon><Magnet /></el-icon>
              MR
            </el-tag>
            <el-tag 
              v-if="row.is_applicable_dr" 
              size="small" 
              type="info" 
              effect="dark"
              class="modality-tag"
            >
              <el-icon><Camera /></el-icon>
              DR
            </el-tag>
            <el-tag 
              v-if="row.is_applicable_rf" 
              size="small" 
              type="default" 
              effect="dark"
              class="modality-tag"
            >
              <el-icon><VideoCamera /></el-icon>
              RF
            </el-tag>
            <el-tag 
              v-if="row.is_applicable_mg" 
              size="small" 
              type="danger" 
              effect="dark"
              class="modality-tag"
            >
              <el-icon><View /></el-icon>
              MG
            </el-tag>
            <el-tag 
              v-if="row.is_applicable_ot" 
              size="small" 
              type="default"
              class="modality-tag"
            >
              <el-icon><More /></el-icon>
              其他
            </el-tag>
            <span v-if="!hasAnyModality(row)" class="no-modality">无适用模态</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="pinyin_code" label="拼音码" width="120">
        <template #default="{ row }">
          <el-text type="info" size="small">{{ row.pinyin_code || '-' }}</el-text>
        </template>
      </el-table-column>
      
      <el-table-column prop="english_name" label="英文名" width="180">
        <template #default="{ row }">
          <el-text type="info" size="small">{{ row.english_name || '-' }}</el-text>
        </template>
      </el-table-column>
      
      <el-table-column prop="sort_order" label="排序" width="80" align="center">
        <template #default="{ row }">
          <el-tag size="small" type="info">{{ row.sort_order }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="is_active" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'" effect="dark">
            {{ row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button-group>
            <el-button link type="primary" size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button link type="primary" size="small" @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 增强分页 -->
    <div class="pagination-container">
      <div class="pagination-info">
        <el-text>
          显示第 {{ (currentPage - 1) * pageSize + 1 }} - 
          {{ Math.min(currentPage * pageSize, total) }} 条，
          共 {{ total }} 条记录
        </el-text>
      </div>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :disabled="loading"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Refresh, Download, Upload, Search, Delete, 
  ArrowRight, Location, Monitor, Camera, VideoCamera, 
  View, Edit, More, Magnet
} from '@element-plus/icons-vue'
import { dataDictApi } from '@/utils/api'

interface BodyPartItem {
  id: number
  level1_code: string
  level1_name: string
  level2_code: string
  level2_name: string
  level3_code: string
  level3_name: string
  part_code: string
  pinyin_code: string
  english_name: string
  is_applicable_ct: boolean
  is_applicable_mr: boolean
  is_applicable_dr: boolean
  is_applicable_rf: boolean
  is_applicable_mg: boolean
  is_applicable_ot: boolean
  sort_order: number
  is_active: boolean
  created_at: string
}

const loading = ref(false)
const tableData = ref<BodyPartItem[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const showInactiveOnly = ref(false)
const selectedModality = ref('')
const selectedLevel1 = ref('')
const level1Options = ref<string[]>([])
const searchKeyword = ref('')
const selectedRows = ref<BodyPartItem[]>([])

// 统计数据
const modalityStats = ref({
  ct: 0,
  mr: 0,
  dr: 0,
  rf: 0,
  mg: 0,
  ot: 0
})

// 计算属性
const level1Count = computed(() => level1Options.value.length)

const hasActiveFilters = computed(() => {
  return selectedModality.value || selectedLevel1.value || searchKeyword.value
})

const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      activeOnly: !showInactiveOnly.value,
    } as any
    
    if (selectedModality.value) {
      params.modality = selectedModality.value
    }
    if (selectedLevel1.value) {
      params.level1 = selectedLevel1.value
    }
    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }
    
    const response = await dataDictApi.getBodyParts(params)
    const { data, pagination, message } = response.data
    
    tableData.value = data
    total.value = pagination?.total || data.length
    
    // 提取一级部位选项和统计
    const level1Set = new Set((data as BodyPartItem[]).map((item: BodyPartItem) => item.level1_name))
    level1Options.value = Array.from(level1Set).sort()
    
    // 计算模态统计
    modalityStats.value = {
      ct: data.filter((item: BodyPartItem) => item.is_applicable_ct).length,
      mr: data.filter((item: BodyPartItem) => item.is_applicable_mr).length,
      dr: data.filter((item: BodyPartItem) => item.is_applicable_dr).length,
      rf: data.filter((item: BodyPartItem) => item.is_applicable_rf).length,
      mg: data.filter((item: BodyPartItem) => item.is_applicable_mg).length,
      ot: data.filter((item: BodyPartItem) => item.is_applicable_ot).length
    }
    
    ElMessage.success(message)
  } catch (error) {
    console.error('获取部位数据失败:', error)
    ElMessage.error('获取部位数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  fetchData()
}

const handleFilterChange = () => {
  currentPage.value = 1
  fetchData()
}

const handleModalityFilter = () => {
  currentPage.value = 1
  fetchData()
}

const handleLevel1Filter = () => {
  currentPage.value = 1
  fetchData()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const clearAllFilters = () => {
  selectedModality.value = ''
  selectedLevel1.value = ''
  searchKeyword.value = ''
  currentPage.value = 1
  fetchData()
}

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchData()
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  fetchData()
}

const handleSelectionChange = (selection: BodyPartItem[]) => {
  selectedRows.value = selection
}

const getRowClassName = ({ row }: { row: BodyPartItem }) => {
  if (!row.is_active) return 'inactive-row'
  return ''
}

const hasAnyModality = (row: BodyPartItem) => {
  return row.is_applicable_ct || row.is_applicable_mr || row.is_applicable_dr || 
         row.is_applicable_rf || row.is_applicable_mg || row.is_applicable_ot
}

const handleAdd = () => {
  ElMessage.info('新增功能开发中...')
}

const handleEdit = (row: BodyPartItem) => {
  ElMessage.info(`编辑部位: ${row.level3_name}`)
}

const handleView = (row: BodyPartItem) => {
  ElMessage.info(`查看部位详情: ${row.level3_name}`)
}

const handleDelete = async (row: BodyPartItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除部位 "${row.level3_name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    ElMessage.info('删除功能开发中...')
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleImport = () => {
  ElMessage.info('导入功能开发中...')
}

defineExpose({
  refreshData
})

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.body-parts-dict {
  padding: 0;
}

.operation-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.operation-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  align-items: center;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.code-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.code-row {
  display: flex;
  gap: 4px;
}

.part-name-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.part-name-group .level1 {
  font-weight: 600;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  gap: 4px;
}

.part-name-group .level2 {
  font-size: 13px;
  color: var(--el-text-color-regular);
  display: flex;
  align-items: center;
  gap: 4px;
  padding-left: 12px;
}

.part-name-group .level3 {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  display: flex;
  align-items: center;
  gap: 4px;
  padding-left: 24px;
}

.modality-applicability {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.modality-tag {
  display: flex;
  align-items: center;
  gap: 2px;
}

.no-modality {
  color: var(--el-text-color-placeholder);
  font-size: 12px;
  font-style: italic;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
}

.pagination-info {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

:deep(.el-table .inactive-row) {
  background-color: var(--el-fill-color-light);
  opacity: 0.6;
}

:deep(.el-table .inactive-row td) {
  color: var(--el-text-color-disabled);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .operation-row {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .stats-section .el-col {
    margin-bottom: 10px;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 12px;
  }
}
</style> 