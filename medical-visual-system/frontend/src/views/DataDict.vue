<template>
  <div class="data-dict-container">
    <el-page-header @back="$router.push('/')">
      <template #content>
        <span class="text-large font-600">医疗检查项目数据管理</span>
      </template>
      <template #extra>
        <el-space>
          <el-tag type="success" effect="dark">部位结构管理</el-tag>
          <el-button type="primary" @click="showDictionaries = !showDictionaries">
            <el-icon><Setting /></el-icon>
            {{ showDictionaries ? '隐藏字典' : '字典管理' }}
          </el-button>
        </el-space>
      </template>
    </el-page-header>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 部位结构主页面 -->
      <div class="body-parts-main">
        <BodyPartsMain ref="bodyPartsMainRef" />
      </div>
      
      <!-- 字典管理侧边栏 -->
      <el-drawer
        v-model="showDictionaries"
        title="数据字典管理"
        direction="rtl"
        size="60%"
        :with-header="true"
      >
        <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
          <el-tab-pane label="模态字典" name="modalities">
            <ModalityDict ref="modalityDictRef" />
          </el-tab-pane>
          
          <el-tab-pane label="人群字典" name="populations">
            <PopulationDict ref="populationDictRef" />
          </el-tab-pane>
          
          <el-tab-pane label="疾病字典" name="diseases">
            <DiseaseDict ref="diseaseDictRef" />
          </el-tab-pane>
          
          <el-tab-pane label="扫描方式" name="scan-mappings">
            <ScanMappingDict ref="scanMappingDictRef" />
          </el-tab-pane>
        </el-tabs>
      </el-drawer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Setting } from '@element-plus/icons-vue'
import BodyPartsMain from '@/components/DataDict/BodyPartsMain.vue'
import ModalityDict from '@/components/DataDict/ModalityDict.vue'
import PopulationDict from '@/components/DataDict/PopulationDict.vue'
import DiseaseDict from '@/components/DataDict/DiseaseDict.vue'
import ScanMappingDict from '@/components/DataDict/ScanMappingDict.vue'

// 当前激活的标签页
const activeTab = ref('modalities')
const showDictionaries = ref(false)

// 各个字典组件的引用
const bodyPartsMainRef = ref()
const modalityDictRef = ref()
const populationDictRef = ref()
const diseaseDictRef = ref()
const scanMappingDictRef = ref()

// 标签页切换处理
const handleTabClick = (tab: any) => {
  console.log('切换到字典标签页:', tab.props.name)
}

// 刷新当前标签页的数据
const refreshCurrentTab = () => {
  switch (activeTab.value) {
    case 'modalities':
      modalityDictRef.value?.refreshData()
      break
    case 'populations':
      populationDictRef.value?.refreshData()
      break
    case 'diseases':
      diseaseDictRef.value?.refreshData()
      break
    case 'scan-mappings':
      scanMappingDictRef.value?.refreshData()
      break
  }
}

// 刷新主要内容
const refreshMainContent = () => {
  bodyPartsMainRef.value?.refreshData()
}
</script>

<style scoped>
.data-dict-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: 100vh;
}

.text-large {
  font-size: 18px;
}

.font-600 {
  font-weight: 600;
}

.main-content {
  margin-top: 20px;
}

.body-parts-main {
  width: 100%;
}

:deep(.el-drawer__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 0;
  padding: 20px;
}

:deep(.el-drawer__title) {
  color: white;
  font-weight: 600;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
}
</style> 