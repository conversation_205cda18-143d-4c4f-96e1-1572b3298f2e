<template>
  <div class="dashboard">
    <!-- 头部导航 -->
    <div class="dashboard-header">
      <el-page-header>
        <template #content>
          <span class="text-large font-600">医疗检查项目可视化数据处理系统</span>
        </template>
        <template #extra>
          <el-space>
            <el-tag type="success">v1.0.0</el-tag>
            <el-tag type="info">FastAPI + Vue.js</el-tag>
          </el-space>
        </template>
      </el-page-header>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-icon modality">
                <el-icon><Collection /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-number">{{ stats.modalities }}</div>
                <div class="stats-label">模态类型</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-icon population">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-number">{{ stats.populations }}</div>
                <div class="stats-label">人群分类</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-icon disease">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-number">{{ stats.diseases }}</div>
                <div class="stats-label">疾病字典</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-icon body-parts">
                <el-icon><Grid /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-number">{{ stats.bodyParts }}</div>
                <div class="stats-label">部位结构</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 快速导航 -->
      <el-row :gutter="20" class="navigation-row">
        <el-col :span="12">
          <el-card class="navigation-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">
                  <el-icon><Management /></el-icon>
                  数据管理
                </span>
              </div>
            </template>
            
            <div class="navigation-grid">
              <div class="nav-item" @click="navigateTo('/data-dict')">
                <el-icon class="nav-icon"><Setting /></el-icon>
                <div class="nav-content">
                  <div class="nav-title">数据字典管理</div>
                  <div class="nav-desc">管理模态、人群、疾病等基础字典</div>
                </div>
              </div>
              
              <div class="nav-item" @click="navigateTo('/projects')">
                <el-icon class="nav-icon"><Folder /></el-icon>
                <div class="nav-content">
                  <div class="nav-title">项目数据管理</div>
                  <div class="nav-desc">查看和管理检查项目数据</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="navigation-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">
                  <el-icon><DataAnalysis /></el-icon>
                  系统功能
                </span>
              </div>
            </template>
            
            <div class="navigation-grid">
              <div class="nav-item" @click="navigateTo('/rules')">
                <el-icon class="nav-icon"><Tools /></el-icon>
                <div class="nav-content">
                  <div class="nav-title">生成规则配置</div>
                  <div class="nav-desc">配置项目生成规则和参数</div>
                </div>
              </div>
              
              <div class="nav-item" @click="navigateTo('/analytics')">
                <el-icon class="nav-icon"><PieChart /></el-icon>
                <div class="nav-content">
                  <div class="nav-title">统计分析</div>
                  <div class="nav-desc">数据可视化和分析报告</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 系统状态 -->
      <el-row>
        <el-col :span="24">
          <el-card class="system-status-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">
                  <el-icon><Monitor /></el-icon>
                  系统状态
                </span>
                <el-button size="small" @click="refreshSystemStatus">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
      </div>
            </template>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="status-item">
                  <el-tag :type="systemStatus.backend ? 'success' : 'danger'">
                    后端API: {{ systemStatus.backend ? '正常' : '异常' }}
                  </el-tag>
      </div>
              </el-col>
              <el-col :span="8">
                <div class="status-item">
                  <el-tag :type="systemStatus.database ? 'success' : 'danger'">
                    数据库: {{ systemStatus.database ? '连接正常' : '连接异常' }}
                  </el-tag>
      </div>
              </el-col>
              <el-col :span="8">
                <div class="status-item">
                  <el-tag type="info">
                    最后更新: {{ systemStatus.lastUpdate }}
                  </el-tag>
      </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { dataDictApi } from '@/utils/api'

const router = useRouter()

// 统计数据
const stats = ref({
  modalities: 0,
  populations: 0,
  diseases: 0,
  bodyParts: 0
})

// 系统状态
const systemStatus = ref({
  backend: false,
  database: false,
  lastUpdate: ''
})

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path)
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const summaryResponse = await dataDictApi.getDataDictSummary()
    const summaryData = summaryResponse.data.data
    
    stats.value = {
      modalities: summaryData.modality_count || 0,
      populations: summaryData.population_count || 0,
      diseases: summaryData.disease_count || 0,
      bodyParts: summaryData.body_parts_count || 0
    }
    
    systemStatus.value.backend = true
    systemStatus.value.database = true
  } catch (error) {
    console.error('获取统计数据失败:', error)
    systemStatus.value.backend = false
    systemStatus.value.database = false
  }
  
  systemStatus.value.lastUpdate = new Date().toLocaleString('zh-CN')
}

// 刷新系统状态
const refreshSystemStatus = () => {
  ElMessage.info('正在刷新系统状态...')
  fetchStats()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: 100vh;
}

.dashboard-header {
  margin-bottom: 20px;
}

.text-large {
  font-size: 18px;
}

.font-600 {
  font-weight: 600;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 统计卡片样式 */
.stats-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.stats-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-icon.modality { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-icon.population { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-icon.disease { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-icon.body-parts { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--el-color-primary);
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

/* 导航卡片样式 */
.navigation-card {
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

.navigation-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  background-color: var(--el-fill-color-lighter);
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-item:hover {
  background-color: var(--el-fill-color-light);
  transform: translateX(4px);
}

.nav-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.nav-content {
  flex: 1;
}

.nav-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.nav-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

/* 系统状态样式 */
.system-status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.system-status-card :deep(.el-card__header) {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.system-status-card :deep(.el-card__body) {
  background: transparent;
}

.system-status-card .card-title {
  color: white;
}

.status-item {
  text-align: center;
}

.status-item .el-tag {
  font-size: 13px;
  padding: 8px 16px;
}
</style> 