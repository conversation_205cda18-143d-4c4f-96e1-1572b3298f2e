import axios, { type AxiosInstance, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    
    // 如果响应包含success字段，检查是否成功
    if (data.hasOwnProperty('success') && !data.success) {
      ElMessage.error(data.message || '请求失败')
      throw new Error(data.message || '请求失败')
    }
    
    return response
  },
  (error) => {
    console.error('响应错误:', error)
    
    // 处理不同的错误状态
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          ElMessage.error(data?.message || '请求参数错误')
          break
        case 401:
          ElMessage.error('未授权，请重新登录')
          break
        case 403:
          ElMessage.error('禁止访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error(data?.message || '服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.request) {
      ElMessage.error('网络错误，请检查网络连接')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API接口定义
export const dataDictApi = {
  // 模态字典
  getModalities: (activeOnly = true) => 
    api.get(`/data-dict/modalities?active_only=${activeOnly}`),
  
  // 人群字典
  getPopulations: (activeOnly = true) => 
    api.get(`/data-dict/populations?active_only=${activeOnly}`),
  
  // 疾病字典
  getDiseases: (activeOnly = true) => 
    api.get(`/data-dict/diseases?active_only=${activeOnly}`),
  
  // 扫描方式映射
  getScanMappings: (modality?: string, activeOnly = true) => {
    let url = `/data-dict/scan-mappings?active_only=${activeOnly}`
    if (modality) {
      url += `&modality=${modality}`
    }
    return api.get(url)
  },
  
  // 部位数据
  getBodyParts: (params: {
    modality?: string
    level1?: string
    search?: string
    activeOnly?: boolean
    page?: number
    pageSize?: number
  } = {}) => {
    const {
      modality,
      level1,
      search,
      activeOnly = true,
      page = 1,
      pageSize = 20
    } = params
    
    let url = `/data-dict/body-parts?active_only=${activeOnly}&page=${page}&page_size=${pageSize}`
    if (modality) url += `&modality=${modality}`
    if (level1) url += `&level1=${level1}`
    if (search) url += `&search=${encodeURIComponent(search)}`
    
    return api.get(url)
  },
  
  // 部位模态适用性统计
  getBodyPartsModalityStats: () => 
    api.get('/data-dict/body-parts/modality-stats'),
  
  // 数据字典汇总信息
  getDataDictSummary: () => 
    api.get('/data-dict/summary')
}

export default api 