{"name": "medical-visual-frontend", "version": "1.0.0", "description": "医疗检查项目可视化数据处理系统前端", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "element-plus": "^2.3.8", "pinia": "^2.1.4", "vue": "^3.3.4", "vue-router": "^4.2.4", "vue3-excel-editor": "^1.0.60"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "typescript": "^5.2.2", "vite": "^4.5.0", "vue-tsc": "^1.8.22"}}