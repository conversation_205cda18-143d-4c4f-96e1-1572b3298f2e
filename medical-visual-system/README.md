# 医疗检查项目可视化数据处理系统

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Python](https://img.shields.io/badge/python-3.11+-green.svg)
![Vue](https://img.shields.io/badge/vue-3.0+-brightgreen.svg)
![FastAPI](https://img.shields.io/badge/fastapi-0.104+-red.svg)

## 📖 项目简介

医疗检查项目可视化数据处理系统是一个专业的医疗检查项目数据管理和可视化平台，支持多模态医疗项目的标准化管理、智能生成和数据分析。

### 🏥 核心功能

- **📋 多模态项目管理**: 支持CT、MR、DR、MG、医保项目的完整管理
- **📤 Excel数据导入**: 多sheet数据批量导入，支持复杂的数据验证
- **🔧 项目生成引擎**: 基于规则的自动项目生成，支持自定义规则配置
- **📊 数据可视化**: 实时统计分析和图表展示
- **🔍 高级筛选**: 多条件组合筛选和自定义视图
- **📱 响应式设计**: 支持桌面端和移动端的完美适配

### 📋 支持的数据格式

- **🏗️ 部位结构**: 四级部位分类体系（一级→二级→三级→部位码）
- **🩻 DR项目**: 包含摆位、体位、方向等完整信息
- **💊 医保编码**: 标准医保项目编码体系
- **🔬 MG项目**: 乳腺钼靶专项数据管理

## 🛠️ 技术架构

### 后端技术栈
- **🐍 Python 3.11+** - 核心开发语言
- **⚡ FastAPI** - 现代高性能Web框架
- **🗄️ SQLAlchemy** - ORM数据库操作
- **📚 SQLite/PostgreSQL** - 数据库支持
- **🔄 Uvicorn** - ASGI服务器

### 前端技术栈
- **💚 Vue.js 3** - 渐进式前端框架
- **📘 TypeScript** - 类型安全的JavaScript
- **⚡ Vite** - 现代前端构建工具
- **🎨 Element Plus** - 企业级UI组件库
- **📊 ECharts** - 数据可视化图表库
- **📋 ag-Grid** - 高性能数据表格

## 🚀 快速开始

### 📋 环境要求

**后端环境：**
- Python 3.11+
- pip（Python包管理器）

**前端环境：**
- Node.js 18+
- npm（Node包管理器）

### 🔧 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd medical-visual-system
```

#### 2. 后端设置
```bash
# 进入后端目录
cd backend

# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Mac/Linux
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 初始化数据库（如需要）
python database/init_db.py
```

#### 3. 前端设置
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install
```

### ▶️ 启动项目

#### 方法一：分别启动（推荐开发环境）

**启动后端服务：**
```bash
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**启动前端服务（新开终端）：**
```bash
cd frontend
npm run dev
```

#### 方法二：Docker启动（推荐生产环境）
```bash
# 使用Docker Compose一键启动
docker-compose up -d
```

### 🌐 访问地址

| 服务 | 地址 | 描述 |
|------|------|------|
| 🖥️ **前端界面** | http://localhost:3000 | 主要操作界面 |
| 📊 **数据字典** | http://localhost:3000/data-dict | 数据字典管理 |
| 🔧 **API文档** | http://localhost:8000/docs | Swagger API文档 |
| ❤️ **健康检查** | http://localhost:8000/health | 系统状态检查 |

## 📚 API文档

### 主要API端点

#### 数据字典相关
```bash
GET  /api/v1/data-dict/modalities     # 获取模态字典
GET  /api/v1/data-dict/populations    # 获取人群字典
GET  /api/v1/data-dict/diseases       # 获取疾病字典
GET  /api/v1/data-dict/scan-mappings  # 获取扫描方式映射
GET  /api/v1/data-dict/body-parts     # 获取部位数据（分页）
GET  /api/v1/data-dict/summary        # 获取数据字典汇总
```

#### 系统相关
```bash
GET  /health                          # 系统健康检查
GET  /info                           # 系统信息
GET  /docs                           # API文档
```

### 🔍 API使用示例

**获取模态字典：**
```bash
curl -X GET "http://localhost:8000/api/v1/data-dict/modalities?active_only=true"
```

**获取部位数据（分页）：**
```bash
curl -X GET "http://localhost:8000/api/v1/data-dict/body-parts?page=1&page_size=20&modality=CT"
```

## 📁 项目目录结构

```
medical-visual-system/
├── backend/                    # 后端 FastAPI 应用
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI 应用入口
│   │   ├── api/               # API 路由
│   │   │   ├── __init__.py
│   │   │   ├── router.py      # 主路由
│   │   │   └── endpoints/     # 各功能端点
│   │   │       ├── data_dict.py
│   │   │       ├── projects.py
│   │   │       ├── rules.py
│   │   │       ├── system.py
│   │   │       └── upload.py
│   │   ├── core/              # 核心配置
│   │   │   ├── config.py      # 配置管理
│   │   │   └── database.py    # 数据库配置
│   │   ├── models/            # 数据模型
│   │   ├── services/          # 业务逻辑
│   │   ├── utils/             # 工具函数
│   │   └── legacy/            # 原有代码保留
│   ├── database/              # 数据库相关
│   │   ├── init_db.py        # 数据库初始化
│   │   ├── schema.sql        # 数据库结构
│   │   └── migrations/       # 数据迁移
│   ├── logs/                  # 日志文件
│   ├── uploads/               # 上传文件
│   ├── exports/               # 导出文件
│   ├── requirements.txt       # Python 依赖
│   ├── Dockerfile
│   └── env.example
├── frontend/                   # 前端 Vue.js 应用
│   ├── src/
│   │   ├── main.ts            # 应用入口
│   │   ├── App.vue            # 根组件
│   │   ├── components/        # 可复用组件
│   │   │   └── DataDict/      # 数据字典组件
│   │   │       ├── ModalityDict.vue
│   │   │       ├── PopulationDict.vue
│   │   │       ├── DiseaseDict.vue
│   │   │       ├── ScanMappingDict.vue
│   │   │       └── BodyPartsDict.vue
│   │   ├── views/             # 页面视图
│   │   │   ├── Dashboard.vue  # 首页
│   │   │   ├── DataDict.vue   # 数据字典
│   │   │   ├── Projects.vue   # 项目管理
│   │   │   ├── Rules.vue      # 规则配置
│   │   │   └── Analytics.vue  # 数据分析
│   │   ├── router/            # 路由配置
│   │   ├── stores/            # 状态管理
│   │   ├── utils/             # 工具函数
│   │   │   └── api.ts         # API封装
│   │   ├── types/             # TypeScript 类型
│   │   └── assets/            # 静态资源
│   ├── public/                # 公共文件
│   ├── package.json           # 依赖配置
│   ├── vite.config.ts         # Vite配置
│   ├── tsconfig.json          # TypeScript配置
│   └── Dockerfile
├── shared/                     # 共享资源
│   ├── data/                  # 数据文件
│   └── docs/                  # 文档
├── docker-compose.yml          # 容器编排
├── .gitignore
├── README.md                   # 项目说明
└── TodoList-任务进度跟踪表.md
```

## 🔧 开发指南

### 🐛 调试和开发

**后端开发：**
```bash
# 开发模式启动（自动重载）
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 查看日志
tail -f logs/app.log

# 运行测试
python -m pytest
```

**前端开发：**
```bash
# 开发模式启动
npm run dev

# 类型检查
npm run type-check

# 代码格式化
npm run format

# 构建生产版本
npm run build
```

### 📊 数据库管理

**查看数据库状态：**
```bash
cd backend
python database/init_db.py --check
```

**导入示例数据：**
```bash
python import_excel_data.py
```

## ❗ 常见问题

### 🔧 端口冲突
```bash
# 查看端口占用
lsof -i :8000  # 后端端口
lsof -i :3000  # 前端端口

# 杀死占用进程
kill -9 <PID>

# 或者修改端口
uvicorn app.main:app --port 8001  # 后端
npm run dev -- --port 3001        # 前端
```

### 📦 依赖问题
```bash
# Python依赖冲突
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall

# Node.js依赖问题
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### 🗄️ 数据库问题
```bash
# 重新初始化数据库
rm medical_visual_system.db
python database/init_db.py
python import_excel_data.py
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 联系方式

- 项目维护者：医疗检查项目系统团队
- 邮箱：<EMAIL>
- 项目地址：[GitHub Repository](https://github.com/your-org/medical-visual-system)

## 🎯 路线图

### v1.0 (当前版本)
- [x] 数据字典管理
- [x] 基础数据展示
- [x] API接口完善
- [x] 前端界面优化

### v1.1 (开发中)
- [ ] CRUD操作完善
- [ ] 数据导入导出
- [ ] 项目生成引擎
- [ ] 数据可视化分析

### v2.0 (规划中)
- [ ] 用户权限管理
- [ ] 审计日志系统
- [ ] 高级报表功能
- [ ] 微服务架构升级

---

**🚀 现在就开始使用医疗检查项目可视化系统，体验现代化的医疗数据管理！** 