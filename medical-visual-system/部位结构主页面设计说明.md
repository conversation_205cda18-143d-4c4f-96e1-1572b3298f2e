# 🏥 部位结构主页面设计说明

## 📋 设计概述

根据用户需求，已完成部位结构管理系统的重新设计，将部位结构作为主显示页面，其他数据字典作为辅助管理工具。

## 🎨 界面设计特点

### 1. 主页面布局
- **部位结构** 作为主显示内容，占据整个页面主体
- **数据字典** 通过右侧抽屉(Drawer)进行管理
- 顶部导航显示"部位结构管理"标签，右侧按钮控制字典显示/隐藏

### 2. 三级结构展示
按照要求的顺序进行列排布：
```
一级编码 → 一级部位 → 二级编码 → 二级部位 → 三级编码 → 三级部位 → 模态适用性(CT/MR/DR/RF/MG/OT)
```

### 3. 统计卡片区域
- **总计部位**：显示所有部位数量
- **一级部位**：显示一级部位分类数量  
- **CT适用**：显示CT模态适用的部位数量
- **MR适用**：显示MR模态适用的部位数量
- **DR适用**：显示DR模态适用的部位数量
- **其他模态**：显示RF/MG/OT模态适用的部位数量

## 🔧 功能特性

### 1. 数据操作功能
- ✅ **新增部位**：支持添加新的部位结构
- ✅ **编辑部位**：支持修改现有部位信息
- ✅ **查看详情**：支持查看部位完整信息
- ✅ **复制部位**：支持基于现有部位创建副本
- ✅ **批量操作**：支持选择多个部位进行批量编辑
- 🔄 **删除部位**：删除功能预留（待实现）

### 2. 筛选和搜索
- **关键字搜索**：支持按部位名称、编码、拼音码搜索
- **一级部位筛选**：按一级部位分类筛选
- **模态筛选**：按CT/MR/DR/RF/MG/OT模态适用性筛选
- **状态筛选**：显示全部或仅显示启用状态的部位
- **清空筛选**：一键清除所有筛选条件

### 3. 排序功能
- **编码排序**：按编码层级排序（默认）
- **名称排序**：按部位名称排序
- **创建时间**：按创建时间排序
- **模态数量**：按适用模态数量排序
- **升序/降序**：支持正序和倒序排列

### 4. 视图模式
- **平铺视图**：传统表格显示（当前实现）
- **树形视图**：层级树状结构显示（预留）
- **分组视图**：按一级部位分组显示（预留）

## 📊 表格列设计

### 主要信息列
| 列名 | 宽度 | 说明 | 样式 |
|------|------|------|------|
| 一级编码 | 80px | 显示一级编码 | 绿色标签 |
| 一级部位 | 120px | 显示一级部位名称 | 带图标 |
| 二级编码 | 80px | 显示二级编码 | 橙色标签 |
| 二级部位 | 120px | 显示二级部位名称 | 带箭头图标 |
| 三级编码 | 80px | 显示三级编码 | 蓝色标签 |
| 三级部位 | 150px | 显示三级部位名称 | 强调样式 |
| 部位编码 | 100px | 显示部位编码 | 普通标签 |

### 模态适用性列
| 列名 | 宽度 | 说明 | 样式 |
|------|------|------|------|
| CT | 60px | CT适用性 | 绿色✓或- |
| MR | 60px | MR适用性 | 橙色✓或- |
| DR | 60px | DR适用性 | 蓝色✓或- |
| RF | 60px | RF适用性 | 灰色✓或- |
| MG | 60px | MG适用性 | 红色✓或- |
| 其他 | 60px | 其他模态适用性 | 默认✓或- |

### 扩展信息列
| 列名 | 宽度 | 说明 |
|------|------|------|
| 拼音码 | 100px | 拼音简码 |
| 英文名 | 120px | 英文名称 |
| 状态 | 80px | 启用/禁用状态 |
| 操作 | 200px | 详情/编辑/复制/删除按钮 |

## 🗂️ 数据字典管理

通过右侧抽屉访问以下字典：

### 1. 模态字典
- 管理CT、MR、DR、RF、MG、OT等检查模态
- 维护模态基础信息和参数

### 2. 人群字典  
- 管理胎儿、新生儿、儿童、孕妇等人群分类
- 维护人群特殊要求

### 3. 疾病字典
- 管理疾病分类和编码
- 维护疾病相关信息

### 4. 扫描方式字典
- 管理各种扫描方式和技术参数
- 维护扫描方式与模态的映射关系

## 🎯 操作流程

### 1. 部位管理流程
```mermaid
graph LR
    A[进入部位结构主页] --> B[查看统计概览]
    B --> C[使用筛选和搜索]
    C --> D[选择目标部位]
    D --> E[执行操作]
    E --> F[新增/编辑/查看/删除]
    F --> G[保存确认]
    G --> H[刷新数据]
```

### 2. 批量操作流程
```mermaid
graph LR
    A[勾选多个部位] --> B[点击批量编辑]
    B --> C[批量修改属性]
    C --> D[确认变更]
    D --> E[应用到所有选中项]
```

### 3. 字典维护流程
```mermaid
graph LR
    A[点击字典管理] --> B[选择字典类型]
    B --> C[维护字典数据]
    C --> D[保存字典变更]
    D --> E[影响部位结构]
```

## 📱 响应式设计

### 桌面端 (>1200px)
- 完整显示所有列和功能
- 6列统计卡片布局
- 完整工具栏和筛选条

### 平板端 (768px-1200px)  
- 部分列可横向滚动
- 统计卡片可能换行
- 工具栏按钮可能换行

### 移动端 (<768px)
- 抽屉改为全屏模式
- 统计卡片纵向排列
- 表格支持横向滚动
- 操作按钮简化显示

## 🔮 后续开发计划

### v1.1 增强功能
- [ ] 实现真实的CRUD API对接
- [ ] 完善批量操作功能
- [ ] 添加数据导入导出功能
- [ ] 实现树形和分组视图模式

### v1.2 高级功能
- [ ] 添加数据变更历史记录
- [ ] 实现权限控制和审核流程
- [ ] 添加数据统计和报表功能
- [ ] 集成医保编码生成引擎

### v2.0 完整系统
- [ ] 集成项目生成引擎
- [ ] 添加工作流管理
- [ ] 实现多租户支持
- [ ] 添加API开放平台

## ⚡ 技术特点

- **Vue 3 + TypeScript**：现代前端技术栈
- **Element Plus**：企业级UI组件库  
- **响应式设计**：适配各种设备屏幕
- **模块化架构**：组件化开发便于维护
- **类型安全**：TypeScript提供类型检查
- **实时数据**：支持数据实时刷新和同步

---

## 📞 使用支持

如需使用帮助或功能定制，请参考：
- 📖 [项目文档](./README.md)
- 🔧 [API文档](http://localhost:8000/docs)
- 📋 [开发计划](./项目开发计划.md) 