#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目可视化数据处理系统 - Excel数据导入脚本
创建时间: 2025-01-11
版本: v1.0
"""

import pandas as pd
import sqlite3
import os
from datetime import datetime
import sys

def connect_database():
    """连接数据库"""
    db_path = 'medical_visual_system.db'
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return None
    
    conn = sqlite3.connect(db_path)
    print(f"✅ 数据库连接成功: {db_path}")
    return conn

def import_body_parts(excel_file, conn):
    """导入部位表数据"""
    print("\n🏥 导入部位表数据...")
    
    try:
        # 读取三级部位结构
        df = pd.read_excel(excel_file, sheet_name='三级部位结构')
        print(f"📋 读取到 {len(df)} 条部位数据")
        
        # 显示前几行数据结构
        print("📊 数据结构预览:")
        print(df.head(3))
        print("\n📝 列名列表:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. {col}")
        
        # 清空现有数据
        cursor = conn.cursor()
        cursor.execute("DELETE FROM body_parts")
        
        # 准备数据插入
        insert_count = 0
        
        for index, row in df.iterrows():
            try:
                # 根据实际列名调整字段映射，正确处理编码格式
                # 一级编码：浮点数转为1位字符串
                level1_code_raw = row.iloc[0]
                level1_code = str(int(level1_code_raw)) if not pd.isna(level1_code_raw) else ""
                
                level1_name = str(row.iloc[1]) if not pd.isna(row.iloc[1]) else ""
                
                # 二级编码：已经是字符串格式，直接使用
                level2_code = str(row.iloc[2]) if not pd.isna(row.iloc[2]) else ""
                level2_name = str(row.iloc[3]) if not pd.isna(row.iloc[3]) else ""
                
                # 三级编码：浮点数转为2位字符串（补零）
                level3_code_raw = row.iloc[4]
                level3_code = f"{int(level3_code_raw):02d}" if not pd.isna(level3_code_raw) else ""
                
                level3_name = str(row.iloc[5]) if not pd.isna(row.iloc[5]) else ""
                
                # 生成部位编码：一级(1位) + 二级(2位) + 三级(2位) = 5位字符串
                part_code = level1_code + level2_code + level3_code
                
                # 验证编码格式
                if len(part_code) != 5:
                    print(f"⚠️ 第{index+1}行编码格式不正确: {part_code} (期望5位)")
                    continue
                
                # 设置模态适用性（示例规则，可根据实际情况调整）
                is_applicable_ct = True if '头' in level1_name or '胸' in level1_name or '腹' in level1_name else False
                is_applicable_mr = True if '头' in level1_name or '脊' in level1_name or '关节' in level1_name else False
                is_applicable_dr = True if '胸' in level1_name or '骨' in level1_name or '关节' in level1_name else False
                is_applicable_rf = True if '胸' in level1_name or '消化' in level1_name else False
                is_applicable_mg = True if '乳腺' in level3_name else False
                is_applicable_ot = False
                
                # 插入数据
                cursor.execute("""
                    INSERT INTO body_parts (
                        level1_code, level1_name, level2_code, level2_name,
                        level3_code, level3_name, part_code,
                        is_applicable_ct, is_applicable_mr, is_applicable_dr,
                        is_applicable_rf, is_applicable_mg, is_applicable_ot,
                        is_active
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    level1_code, level1_name, level2_code, level2_name,
                    level3_code, level3_name, part_code,
                    is_applicable_ct, is_applicable_mr, is_applicable_dr,
                    is_applicable_rf, is_applicable_mg, is_applicable_ot,
                    True
                ))
                insert_count += 1
                
            except Exception as e:
                print(f"⚠️ 第{index+1}行数据插入失败: {e}")
                continue
        
        conn.commit()
        print(f"✅ 部位表导入完成: {insert_count}/{len(df)} 条记录")
        
    except Exception as e:
        print(f"❌ 部位表导入失败: {e}")

def import_modality_dict(excel_file, conn):
    """导入模态字典数据"""
    print("\n🔧 导入模态字典数据...")
    
    try:
        # 读取模态表
        df = pd.read_excel(excel_file, sheet_name='模态表')
        print(f"📋 读取到 {len(df)} 条模态数据")
        
        cursor = conn.cursor()
        cursor.execute("DELETE FROM modality_dict")
        
        # 预定义模态映射
        modality_mappings = [
            ('2a', 'CT', 'CT计算机断层扫描', True, 1),
            ('2b', 'CT', 'CT增强扫描', False, 2),
            ('3a', 'MR', 'MR磁共振平扫', True, 3),
            ('3b', 'MR', 'MR磁共振增强', False, 4),
            ('3c', 'MR', 'MR磁共振功能', False, 5),
            ('11', 'DR', 'DR数字化X线摄影', True, 6),
            ('13', 'MG', 'MG乳腺钼靶摄影', True, 7),
            ('12', 'RF', 'RF透视检查', True, 8)
        ]
        
        insert_count = 0
        for mapping_code, modality, desc, is_default, sort_order in modality_mappings:
            cursor.execute("""
                INSERT INTO modality_dict (
                    mapping_code_prefix, modality_name, modality_desc,
                    default_mapping, sort_order, is_active
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (mapping_code, modality, desc, is_default, sort_order, True))
            insert_count += 1
        
        conn.commit()
        print(f"✅ 模态字典导入完成: {insert_count} 条记录")
        
    except Exception as e:
        print(f"❌ 模态字典导入失败: {e}")

def import_population_dict(excel_file, conn):
    """导入人群字典数据"""
    print("\n👥 导入人群字典数据...")
    
    try:
        # 读取人群表
        df = pd.read_excel(excel_file, sheet_name='人群表')
        print(f"📋 读取到 {len(df)} 条人群数据")
        print("📊 数据预览:")
        print(df.head())
        
        cursor = conn.cursor()
        cursor.execute("DELETE FROM population_dict")
        
        insert_count = 0
        for index, row in df.iterrows():
            try:
                # 根据实际列名调整
                population_code = str(row.iloc[0]) if not pd.isna(row.iloc[0]) else ""
                population_name = str(row.iloc[1]) if not pd.isna(row.iloc[1]) else ""
                description = str(row.iloc[2]) if len(row) > 2 and not pd.isna(row.iloc[2]) else ""
                
                cursor.execute("""
                    INSERT INTO population_dict (
                        population_code, population_name, description, sort_order, is_active
                    ) VALUES (?, ?, ?, ?, ?)
                """, (population_code, population_name, description, index + 1, True))
                insert_count += 1
                
            except Exception as e:
                print(f"⚠️ 第{index+1}行人群数据插入失败: {e}")
                continue
        
        conn.commit()
        print(f"✅ 人群字典导入完成: {insert_count}/{len(df)} 条记录")
        
    except Exception as e:
        print(f"❌ 人群字典导入失败: {e}")

def import_disease_dict(excel_file, conn):
    """导入疾病字典数据"""
    print("\n🏥 导入疾病字典数据...")
    
    try:
        # 读取疾病表
        df = pd.read_excel(excel_file, sheet_name='疾病表')
        print(f"📋 读取到 {len(df)} 条疾病数据")
        print("📊 数据预览:")
        print(df.head())
        
        cursor = conn.cursor()
        cursor.execute("DELETE FROM disease_dict")
        
        insert_count = 0
        for index, row in df.iterrows():
            try:
                # 根据实际列名调整
                disease_code = str(row.iloc[0]) if not pd.isna(row.iloc[0]) else ""
                disease_name = str(row.iloc[1]) if not pd.isna(row.iloc[1]) else ""
                description = str(row.iloc[2]) if len(row) > 2 and not pd.isna(row.iloc[2]) else ""
                
                cursor.execute("""
                    INSERT INTO disease_dict (
                        disease_code, disease_name, description, sort_order, is_active
                    ) VALUES (?, ?, ?, ?, ?)
                """, (disease_code, disease_name, description, index + 1, True))
                insert_count += 1
                
            except Exception as e:
                print(f"⚠️ 第{index+1}行疾病数据插入失败: {e}")
                continue
        
        conn.commit()
        print(f"✅ 疾病字典导入完成: {insert_count}/{len(df)} 条记录")
        
    except Exception as e:
        print(f"❌ 疾病字典导入失败: {e}")

def import_scan_mapping(excel_file, conn):
    """导入扫描方式映射数据"""
    print("\n🔍 导入扫描方式映射数据...")
    
    try:
        # 读取扫描方式医保映射编码
        df = pd.read_excel(excel_file, sheet_name='扫描方式医保映射编码')
        print(f"📋 读取到 {len(df)} 条扫描映射数据")
        print("📊 数据预览:")
        print(df.head())
        
        cursor = conn.cursor()
        cursor.execute("DELETE FROM scan_mapping")
        
        insert_count = 0
        for index, row in df.iterrows():
            try:
                # 根据实际列名调整字段映射
                scan_method = str(row.iloc[0]) if not pd.isna(row.iloc[0]) else ""
                insurance_mapping_code = str(row.iloc[1]) if not pd.isna(row.iloc[1]) else ""
                insurance_extension_code = str(row.iloc[2]) if len(row) > 2 and not pd.isna(row.iloc[2]) else ""
                modality = str(row.iloc[3]) if len(row) > 3 and not pd.isna(row.iloc[3]) else "CT"
                
                # 简化扫描方式名称
                scan_method_short = scan_method[:50] if len(scan_method) > 50 else scan_method
                
                cursor.execute("""
                    INSERT INTO scan_mapping (
                        scan_method, scan_method_short, insurance_mapping_code,
                        insurance_extension_code, modality, sort_order, is_active
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (scan_method, scan_method_short, insurance_mapping_code,
                      insurance_extension_code, modality, index + 1, True))
                insert_count += 1
                
            except Exception as e:
                print(f"⚠️ 第{index+1}行扫描映射数据插入失败: {e}")
                continue
        
        conn.commit()
        print(f"✅ 扫描方式映射导入完成: {insert_count}/{len(df)} 条记录")
        
    except Exception as e:
        print(f"❌ 扫描方式映射导入失败: {e}")

def verify_import(conn):
    """验证导入结果"""
    print("\n📊 验证导入结果...")
    
    cursor = conn.cursor()
    
    # 检查各表数据量
    tables = [
        ('body_parts', '部位表'),
        ('modality_dict', '模态字典'),
        ('population_dict', '人群字典'),
        ('disease_dict', '疾病字典'),
        ('scan_mapping', '扫描方式映射')
    ]
    
    for table_name, table_desc in tables:
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"  📋 {table_desc} ({table_name}): {count} 条记录")
    
    # 显示部位表的模态适用性统计
    print("\n🏥 部位表模态适用性统计:")
    modalities = ['ct', 'mr', 'dr', 'rf', 'mg', 'ot']
    for modality in modalities:
        cursor.execute(f"SELECT COUNT(*) FROM body_parts WHERE is_applicable_{modality} = 1")
        count = cursor.fetchone()[0]
        print(f"  🔧 适用{modality.upper()}的部位: {count} 个")

def main():
    """主函数"""
    print("🏥 医疗检查项目数据导入工具")
    print("=" * 60)
    
    # Excel文件路径
    excel_file = '../shared/data/NEW_检查项目名称结构表 (11).xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return
    
    # 连接数据库
    conn = connect_database()
    if not conn:
        return
    
    try:
        # 按顺序导入数据
        import_body_parts(excel_file, conn)           # 1. 部位表
        import_modality_dict(excel_file, conn)        # 2. 模态字典
        import_population_dict(excel_file, conn)      # 3. 人群字典
        import_disease_dict(excel_file, conn)         # 4. 疾病字典
        import_scan_mapping(excel_file, conn)         # 5. 扫描方式映射
        
        # 验证导入结果
        verify_import(conn)
        
        print("\n🎉 数据导入完成!")
        print("💡 提示: 可以通过API接口查询导入的数据")
        
    except Exception as e:
        print(f"❌ 数据导入过程出错: {e}")
    
    finally:
        conn.close()
        print("🔐 数据库连接已关闭")

if __name__ == "__main__":
    main() 