#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目可视化数据处理系统 - FastAPI主应用
创建时间: 2025-01-11
版本: v1.0
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import time
import logging
from typing import Dict, Any
import uvicorn
import os
from pathlib import Path

# 导入核心配置
from app.core.config import settings
from app.core.database import engine, Base, get_db
from app.api.router import api_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("logs/app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用启动和关闭时的生命周期管理
    """
    # 启动时的操作
    logger.info("🚀 医疗检查项目可视化系统正在启动...")
    
    # 确保数据库表存在
    try:
        # 这里可以添加数据库初始化逻辑
        logger.info("📊 数据库连接检查完成")
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        raise
    
    # 确保必要的目录存在
    os.makedirs("logs", exist_ok=True)
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("exports", exist_ok=True)
    
    logger.info("✅ 系统启动完成!")
    
    yield  # 应用运行
    
    # 关闭时的操作
    logger.info("🔄 系统正在关闭...")
    logger.info("✅ 系统已安全关闭")

# 创建FastAPI应用实例
app = FastAPI(
    title="医疗检查项目可视化数据处理系统",
    description="""
    ## 医疗检查项目可视化数据处理系统 API
    
    专业的医疗检查项目数据管理和可视化平台，支持：
    
    ### 🏥 核心功能
    - **多模态项目管理**: CT、MR、DR、MG、医保项目
    - **Excel数据导入**: 多sheet数据批量导入
    - **项目生成引擎**: 基于规则的自动项目生成
    - **数据可视化**: 实时统计和图表展示
    - **高级筛选**: 多条件组合筛选和自定义视图
    
    ### 📋 支持的数据格式
    - **部位结构**: 三级部位分类体系
    - **DR项目**: 包含摆位、体位、方向信息
    - **医保编码**: 标准医保项目编码
    - **MG项目**: 乳腺钼靶专项数据
    
    ### 🔧 技术特性
    - **RESTful API**: 标准REST接口设计
    - **异步处理**: 高性能异步数据处理
    - **批次管理**: 完整的数据处理历史追踪
    - **数据验证**: 严格的数据完整性检查
    
    ### 📊 数据统计
    - **实时统计**: 项目数量、模态分布统计
    - **生成报告**: 多格式数据导出
    - **历史追踪**: 完整的操作历史记录
    """,
    version="1.0.0",
    contact={
        "name": "医疗检查项目系统",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    openapi_url="/api/v1/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加中间件

# 1. 信任主机中间件（安全）
app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=settings.ALLOWED_HOSTS
)

# 2. CORS中间件（跨域）
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 3. 自定义请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录所有HTTP请求"""
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"📥 {request.method} {request.url.path} - Client: {request.client.host}")
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    
    # 记录响应信息
    logger.info(
        f"📤 {request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.4f}s"
    )
    
    # 添加响应头
    response.headers["X-Process-Time"] = str(process_time)
    
    return response

# 4. 全局异常处理中间件
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """处理HTTP异常"""
    logger.error(f"❌ HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "path": str(request.url.path)
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """处理一般异常"""
    logger.error(f"💥 系统异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "服务器内部错误",
            "detail": str(exc) if settings.DEBUG else "请联系系统管理员",
            "status_code": 500,
            "path": str(request.url.path)
        }
    )

# 静态文件服务（用于前端构建产物）
if Path("static").exists():
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 上传文件服务
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# 导出文件服务
app.mount("/exports", StaticFiles(directory="exports"), name="exports")

# 注册API路由
app.include_router(api_router, prefix="/api/v1")

# 根路径处理
@app.get("/", tags=["系统信息"])
async def root() -> Dict[str, Any]:
    """
    系统根路径，返回基本信息
    """
    return {
        "message": "医疗检查项目可视化数据处理系统",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "redoc": "/redoc",
        "api_prefix": "/api/v1"
    }

# 健康检查端点
@app.get("/health", tags=["系统信息"])
async def health_check() -> Dict[str, Any]:
    """
    系统健康检查
    """
    try:
        # 使用专门的数据库健康检查函数
        from app.core.database import check_database_health
        db_health = check_database_health()
        db_status = db_health["status"]
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        db_status = "unhealthy"
    
    return {
        "status": "healthy" if db_status == "healthy" else "unhealthy",
        "timestamp": time.time(),
        "services": {
            "database": db_status,
            "api": "healthy"
        },
        "version": "1.0.0"
    }

# 系统信息端点
@app.get("/info", tags=["系统信息"])
async def system_info() -> Dict[str, Any]:
    """
    获取系统详细信息
    """
    return {
        "system": {
            "name": "医疗检查项目可视化数据处理系统",
            "version": "1.0.0",
            "environment": settings.ENVIRONMENT,
            "debug": settings.DEBUG
        },
        "api": {
            "docs_url": "/docs",
            "redoc_url": "/redoc",
            "openapi_url": "/api/v1/openapi.json",
            "api_prefix": "/api/v1"
        },
        "features": {
            "data_import": "Excel多sheet导入",
            "project_generation": "多模态项目生成",
            "data_visualization": "实时统计图表",
            "batch_management": "批次管理和追踪",
            "advanced_filtering": "高级筛选和搜索"
        },
        "supported_modalities": ["CT", "MR", "DR", "MG", "医保"],
        "database": {
            "type": "SQLite/PostgreSQL",
            "tables": 10,
            "views": 3
        }
    }

# 开发环境直接运行
if __name__ == "__main__":
    logger.info("🔧 开发模式启动")
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning",
        access_log=settings.DEBUG
    ) 