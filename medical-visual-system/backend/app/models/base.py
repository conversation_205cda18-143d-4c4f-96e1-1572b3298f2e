"""
数据库基础模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.sql import func
from app.core.database import Base


class TimestampMixin:
    """时间戳混入类"""
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")


class BaseModel(Base, TimestampMixin):
    """基础模型类"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"


class HealthCheck(Base):
    """健康检查表"""
    __tablename__ = "health_checks"
    
    id = Column(Integer, primary_key=True)
    service_name = Column(String(50), nullable=False)
    status = Column(String(20), nullable=False)
    checked_at = Column(DateTime(timezone=True), server_default=func.now())
    details = Column(Text) 