#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目可视化数据处理系统 - 配置管理
创建时间: 2025-01-11
版本: v1.0
"""

import os
from typing import List, Union, Optional
from pydantic_settings import BaseSettings
from pydantic import Field, validator
from pathlib import Path

class Settings(BaseSettings):
    """
    系统配置类
    使用Pydantic BaseSettings进行配置管理
    支持环境变量、.env文件等多种配置来源
    """
    
    # ================================================================
    # 基本应用配置
    # ================================================================
    
    PROJECT_NAME: str = Field(
        default="医疗检查项目可视化数据处理系统",
        description="项目名称"
    )
    
    VERSION: str = Field(
        default="1.0.0",
        description="系统版本"
    )
    
    DESCRIPTION: str = Field(
        default="专业的医疗检查项目数据管理和可视化平台",
        description="系统描述"
    )
    
    ENVIRONMENT: str = Field(
        default="development",
        description="运行环境: development, testing, production"
    )
    
    DEBUG: bool = Field(
        default=True,
        description="调试模式"
    )
    
    # ================================================================
    # 服务器配置
    # ================================================================
    
    HOST: str = Field(
        default="0.0.0.0",
        description="服务器绑定地址"
    )
    
    PORT: int = Field(
        default=8000,
        description="服务器端口"
    )
    
    # ================================================================
    # 数据库配置
    # ================================================================
    
    # SQLite配置（开发环境默认）
    SQLITE_URL: str = Field(
        default="sqlite:///./medical_visual_system.db",
        description="SQLite数据库URL"
    )
    
    # PostgreSQL配置（生产环境）
    POSTGRES_HOST: str = Field(
        default="localhost",
        description="PostgreSQL主机地址"
    )
    
    POSTGRES_PORT: int = Field(
        default=5432,
        description="PostgreSQL端口"
    )
    
    POSTGRES_USER: str = Field(
        default="postgres",
        description="PostgreSQL用户名"
    )
    
    POSTGRES_PASSWORD: str = Field(
        default="password",
        description="PostgreSQL密码"
    )
    
    POSTGRES_DB: str = Field(
        default="medical_visual_system",
        description="PostgreSQL数据库名"
    )
    
    DATABASE_URL: Optional[str] = Field(
        default=None,
        description="完整数据库连接URL"
    )
    
    # 数据库连接池配置
    DB_POOL_SIZE: int = Field(
        default=10,
        description="数据库连接池大小"
    )
    
    DB_MAX_OVERFLOW: int = Field(
        default=20,
        description="数据库连接池最大溢出"
    )
    
    DB_ECHO: bool = Field(
        default=False,
        description="是否打印SQL语句"
    )
    
    @validator('DATABASE_URL', pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        """
        根据环境自动组装数据库连接URL
        """
        if isinstance(v, str):
            return v
        
        # 开发环境默认使用SQLite
        if values.get("ENVIRONMENT") == "development":
            return values.get("SQLITE_URL")
        
        # 生产环境使用PostgreSQL
        return (
            f"postgresql://"
            f"{values.get('POSTGRES_USER')}:"
            f"{values.get('POSTGRES_PASSWORD')}@"
            f"{values.get('POSTGRES_HOST')}:"
            f"{values.get('POSTGRES_PORT')}/"
            f"{values.get('POSTGRES_DB')}"
        )
    
    # ================================================================
    # 安全配置
    # ================================================================
    
    SECRET_KEY: str = Field(
        default="medical-visual-system-secret-key-change-in-production",
        description="应用密钥"
    )
    
    ALGORITHM: str = Field(
        default="HS256",
        description="JWT算法"
    )
    
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30,
        description="访问令牌过期时间（分钟）"
    )
    
    # ================================================================
    # CORS配置
    # ================================================================
    
    CORS_ORIGINS: List[str] = Field(
        default=[
            "http://localhost:3000",
            "http://localhost:5173",
            "http://localhost:8080",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:5173",
            "http://127.0.0.1:8080"
        ],
        description="允许的跨域源"
    )
    
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1", "*"],
        description="允许的主机"
    )
    
    # ================================================================
    # 文件处理配置
    # ================================================================
    
    # 文件上传配置
    UPLOAD_DIR: str = Field(
        default="uploads",
        description="文件上传目录"
    )
    
    EXPORT_DIR: str = Field(
        default="exports",
        description="文件导出目录"
    )
    
    MAX_UPLOAD_SIZE: int = Field(
        default=50 * 1024 * 1024,  # 50MB
        description="最大上传文件大小（字节）"
    )
    
    ALLOWED_EXTENSIONS: List[str] = Field(
        default=[".xlsx", ".xls", ".csv"],
        description="允许的文件扩展名"
    )
    
    # ================================================================
    # Excel处理配置
    # ================================================================
    
    # Excel读取配置
    EXCEL_MAX_ROWS: int = Field(
        default=100000,
        description="Excel最大读取行数"
    )
    
    EXCEL_CHUNK_SIZE: int = Field(
        default=1000,
        description="Excel分块处理大小"
    )
    
    # 数据表sheet名称映射
    EXCEL_SHEET_MAPPING: dict = Field(
        default={
            "body_parts": ["三级部位结构", "部位结构", "Body Parts"],
            "dr_projects": ["DR项目名称清单", "DR项目", "DR Projects"],
            "insurance_codes": ["医保编码", "医保", "Insurance"],
            "mg_projects": ["MG项目表", "MG项目", "MG Projects"],
            "scan_mapping": ["扫描方式医保映射编码", "扫描映射", "Scan Mapping"]
        },
        description="Excel sheet名称映射"
    )
    
    # ================================================================
    # 项目生成配置
    # ================================================================
    
    # 编码生成规则
    ENCODING_RULES: dict = Field(
        default={
            "ct_mr_prefix_mapping": {
                "2a": "CT", "2b": "CT",
                "3a": "MR", "3b": "MR", "3c": "MR"
            },
            "dr_prefix": "110000",
            "mg_prefix": "130",
            "project_code_length": 16,
            "part_code_length": 5,
            "mapping_code_length": 6,
            "extension_code_length": 2
        },
        description="项目编码生成规则"
    )
    
    # 项目名称生成规则
    NAMING_RULES: dict = Field(
        default={
            "ct_format": "CT{part_name}({scan_method})",
            "mr_format": "MR{part_name}({scan_method})",
            "dr_format": "DR{part_name}-{position}",
            "mg_format": "MG{part_name}",
            "insurance_format": "{insurance_project_name}"
        },
        description="项目名称生成规则"
    )
    
    # ================================================================
    # 日志配置
    # ================================================================
    
    LOG_LEVEL: str = Field(
        default="INFO",
        description="日志级别"
    )
    
    LOG_DIR: str = Field(
        default="logs",
        description="日志目录"
    )
    
    LOG_FILE: str = Field(
        default="app.log",
        description="日志文件名"
    )
    
    LOG_MAX_SIZE: int = Field(
        default=10 * 1024 * 1024,  # 10MB
        description="单个日志文件最大大小"
    )
    
    LOG_BACKUP_COUNT: int = Field(
        default=5,
        description="日志文件备份数量"
    )
    
    # ================================================================
    # 缓存配置
    # ================================================================
    
    CACHE_TTL: int = Field(
        default=300,  # 5分钟
        description="缓存过期时间（秒）"
    )
    
    CACHE_ENABLED: bool = Field(
        default=True,
        description="是否启用缓存"
    )
    
    # ================================================================
    # 任务处理配置
    # ================================================================
    
    # 异步任务配置
    MAX_WORKERS: int = Field(
        default=4,
        description="异步处理最大工作线程数"
    )
    
    TASK_TIMEOUT: int = Field(
        default=3600,  # 1小时
        description="任务超时时间（秒）"
    )
    
    BATCH_PROCESS_SIZE: int = Field(
        default=1000,
        description="批处理大小"
    )
    
    # ================================================================
    # 统计和监控配置
    # ================================================================
    
    ENABLE_METRICS: bool = Field(
        default=True,
        description="是否启用性能指标收集"
    )
    
    METRICS_INTERVAL: int = Field(
        default=60,
        description="指标收集间隔（秒）"
    )
    
    # ================================================================
    # 验证器
    # ================================================================
    
    @validator('ENVIRONMENT')
    def validate_environment(cls, v):
        """验证环境配置"""
        allowed = ['development', 'testing', 'production']
        if v not in allowed:
            raise ValueError(f'环境必须是: {", ".join(allowed)}')
        return v
    
    @validator('PORT')
    def validate_port(cls, v):
        """验证端口配置"""
        if not 1 <= v <= 65535:
            raise ValueError('端口必须在 1-65535 范围内')
        return v
    
    @validator('CORS_ORIGINS')
    def validate_cors_origins(cls, v):
        """验证CORS源配置"""
        if not isinstance(v, list):
            raise ValueError('CORS_ORIGINS 必须是列表')
        return v
    
    @validator('MAX_UPLOAD_SIZE')
    def validate_upload_size(cls, v):
        """验证上传大小限制"""
        if v <= 0:
            raise ValueError('最大上传大小必须大于0')
        return v
    
    # ================================================================
    # 辅助方法
    # ================================================================
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"
    
    @property
    def upload_path(self) -> Path:
        """上传目录路径"""
        return Path(self.UPLOAD_DIR)
    
    @property
    def export_path(self) -> Path:
        """导出目录路径"""
        return Path(self.EXPORT_DIR)
    
    @property
    def log_path(self) -> Path:
        """日志目录路径"""
        return Path(self.LOG_DIR)
    
    def get_sheet_names(self, table_name: str) -> List[str]:
        """
        获取指定表对应的Excel sheet名称
        
        Args:
            table_name: 数据表名称
            
        Returns:
            对应的sheet名称列表
        """
        return self.EXCEL_SHEET_MAPPING.get(table_name, [])
    
    def get_encoding_rule(self, key: str) -> Union[str, dict, int]:
        """
        获取编码规则
        
        Args:
            key: 规则键名
            
        Returns:
            对应的规则值
        """
        return self.ENCODING_RULES.get(key)
    
    def get_naming_rule(self, modality: str) -> str:
        """
        获取项目命名规则
        
        Args:
            modality: 模态类型
            
        Returns:
            对应的命名规则模板
        """
        key = f"{modality.lower()}_format"
        return self.NAMING_RULES.get(key, "{part_name}")
    
    class Config:
        """Pydantic配置"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"

# 创建全局配置实例
settings = Settings()

# 环境配置验证和初始化
def validate_and_setup_environment():
    """
    验证和设置环境配置
    """
    # 确保必要的目录存在
    directories = [
        settings.upload_path,
        settings.export_path,
        settings.log_path
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
    
    # 输出配置信息（开发环境）
    if settings.is_development:
        print("🔧 系统配置信息:")
        print(f"   环境: {settings.ENVIRONMENT}")
        print(f"   调试模式: {settings.DEBUG}")
        print(f"   数据库: {settings.DATABASE_URL}")
        print(f"   服务器: {settings.HOST}:{settings.PORT}")
        print(f"   CORS源: {', '.join(settings.CORS_ORIGINS)}")

# 开发环境下自动验证配置
if __name__ == "__main__":
    validate_and_setup_environment()
    print("✅ 配置验证完成!") 