#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目可视化数据处理系统 - 数据库连接管理
创建时间: 2025-01-11
版本: v1.0
"""

from sqlalchemy import create_engine, MetaData, event, inspect, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from typing import Generator
import logging
from contextlib import contextmanager

from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 创建数据库引擎
def create_database_engine():
    """
    创建数据库引擎
    根据配置自动选择SQLite或PostgreSQL
    """
    database_url = settings.DATABASE_URL
    logger.info(f"🔌 正在连接数据库: {database_url}")
    
    # SQLite配置
    if database_url.startswith("sqlite"):
        engine = create_engine(
            database_url,
            connect_args={
                "check_same_thread": False,  # SQLite特有配置
                "timeout": 20  # 连接超时
            },
            poolclass=StaticPool,  # SQLite使用静态连接池
            echo=settings.DB_ECHO,  # 是否打印SQL
            future=True  # 使用SQLAlchemy 2.0风格
        )
        
        # SQLite性能优化
        @event.listens_for(engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """设置SQLite性能优化参数"""
            cursor = dbapi_connection.cursor()
            # 启用外键约束
            cursor.execute("PRAGMA foreign_keys=ON")
            # 设置WAL模式（提高并发性能）
            cursor.execute("PRAGMA journal_mode=WAL")
            # 设置同步模式
            cursor.execute("PRAGMA synchronous=NORMAL")
            # 设置缓存大小
            cursor.execute("PRAGMA cache_size=10000")
            # 设置临时存储
            cursor.execute("PRAGMA temp_store=memory")
            cursor.close()
    
    # PostgreSQL配置
    else:
        engine = create_engine(
            database_url,
            pool_size=settings.DB_POOL_SIZE,
            max_overflow=settings.DB_MAX_OVERFLOW,
            pool_pre_ping=True,  # 连接前测试
            pool_recycle=3600,   # 连接回收时间（1小时）
            echo=settings.DB_ECHO,
            future=True
        )
    
    logger.info("✅ 数据库引擎创建成功")
    return engine

# 创建引擎实例
engine = create_database_engine()

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    future=True  # 使用SQLAlchemy 2.0风格
)

# 创建基础模型类
Base = declarative_base()

# 配置元数据
metadata = MetaData(
    naming_convention={
        "ix": "ix_%(column_0_label)s",
        "uq": "uq_%(table_name)s_%(column_0_name)s",
        "ck": "ck_%(table_name)s_%(constraint_name)s",
        "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
        "pk": "pk_%(table_name)s"
    }
)
Base.metadata = metadata

# 数据库会话依赖
def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    用于FastAPI依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话异常: {e}")
        db.rollback()
        raise
    finally:
        db.close()

# 上下文管理器
@contextmanager
def get_db_session():
    """
    数据库会话上下文管理器
    用于手动管理数据库会话
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        logger.error(f"数据库操作异常: {e}")
        db.rollback()
        raise
    finally:
        db.close()

# 数据库初始化函数
def init_database():
    """
    初始化数据库
    创建所有表结构
    """
    try:
        logger.info("🔄 开始初始化数据库...")
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ 数据库初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise

# 数据库连接测试
def test_database_connection():
    """
    测试数据库连接
    """
    try:
        with engine.connect() as connection:
            # 执行简单查询测试连接
            result = connection.execute(text("SELECT 1 as test_connection"))
            test_value = result.scalar()
            
            if test_value == 1:
                logger.info("✅ 数据库连接测试成功")
                return True
            else:
                logger.error("❌ 数据库连接测试失败：返回值异常")
                return False
                
    except Exception as e:
        logger.error(f"❌ 数据库连接测试失败: {e}")
        return False

# 数据库健康检查
def check_database_health():
    """
    检查数据库健康状态
    返回详细的健康信息
    """
    health_info = {
        "status": "unknown",
        "connection": False,
        "tables_exist": False,
        "can_write": False,
        "error": None
    }
    
    try:
        # 1. 测试连接
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
            health_info["connection"] = True
            
            # 2. 检查表是否存在
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            health_info["tables_exist"] = len(tables) > 0
            health_info["table_count"] = len(tables)
            
            # 3. 测试写入权限（如果表存在）
            if health_info["tables_exist"]:
                # 这里可以添加具体的写入测试
                health_info["can_write"] = True
            
            health_info["status"] = "healthy"
            
    except Exception as e:
        health_info["error"] = str(e)
        health_info["status"] = "unhealthy"
        logger.error(f"数据库健康检查失败: {e}")
    
    return health_info

# 数据库统计信息
def get_database_stats():
    """
    获取数据库统计信息
    """
    stats = {
        "engine_info": {
            "url": str(engine.url).replace(str(engine.url.password), "***") if engine.url.password else str(engine.url),
            "driver": engine.dialect.name,
            "pool_size": getattr(engine.pool, 'size', 'N/A'),
            "checked_out": getattr(engine.pool, 'checkedout', 'N/A')
        },
        "tables": {}
    }
    
    try:
        inspector = inspect(engine)
        table_names = inspector.get_table_names()
        
        with engine.connect() as connection:
            for table_name in table_names:
                try:
                    # 获取表行数
                    result = connection.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    row_count = result.scalar()
                    
                    stats["tables"][table_name] = {
                        "row_count": row_count,
                        "columns": len(inspector.get_columns(table_name))
                    }
                except Exception as e:
                    stats["tables"][table_name] = {
                        "error": str(e)
                    }
    
    except Exception as e:
        stats["error"] = str(e)
        logger.error(f"获取数据库统计信息失败: {e}")
    
    return stats

# 数据库备份函数（SQLite）
def backup_sqlite_database(backup_path: str = None):
    """
    备份SQLite数据库
    """
    if not settings.DATABASE_URL.startswith("sqlite"):
        raise ValueError("此函数仅适用于SQLite数据库")
    
    try:
        import shutil
        from datetime import datetime
        
        # 获取数据库文件路径
        db_file = settings.DATABASE_URL.replace("sqlite:///", "")
        
        # 生成备份文件名
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{db_file}.backup_{timestamp}"
        
        # 复制数据库文件
        shutil.copy2(db_file, backup_path)
        
        logger.info(f"✅ 数据库备份成功: {backup_path}")
        return backup_path
        
    except Exception as e:
        logger.error(f"❌ 数据库备份失败: {e}")
        raise

# 清理连接池
def cleanup_database_connections():
    """
    清理数据库连接池
    """
    try:
        engine.dispose()
        logger.info("✅ 数据库连接池已清理")
    except Exception as e:
        logger.error(f"❌ 清理数据库连接池失败: {e}")

# 开发环境测试
if __name__ == "__main__":
    # 测试数据库连接
    if test_database_connection():
        print("✅ 数据库连接正常")
        
        # 显示数据库统计信息
        stats = get_database_stats()
        print(f"📊 数据库统计: {stats}")
        
        # 健康检查
        health = check_database_health()
        print(f"🏥 健康状态: {health}")
    else:
        print("❌ 数据库连接失败") 