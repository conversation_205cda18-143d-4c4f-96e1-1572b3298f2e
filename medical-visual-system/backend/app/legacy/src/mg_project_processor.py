#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MG项目表处理程序
从 NEW_检查项目名称结构表 (11).xlsx 中提取MG项目表并单独输出
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

class MGProjectProcessor:
    """MG项目表处理器"""
    
    def __init__(self, excel_file_path):
        """初始化处理器"""
        self.excel_file_path = excel_file_path
        self.df_mg = None
        self.load_data()
        
    def load_data(self):
        """加载MG项目表数据"""
        print("🔄 正在加载MG项目表数据...")
        try:
            # 加载MG项目表，保持编码为字符串格式
            dtype_mg = {
                '一级编码': str,
                '二级编码': str,
                '三级编码': str,
                '部位编码': str,
                '医保映射编码': str,
                '医保扩展码': str,
                '检查项目编码': str,
                '人群编码': str,
                '疾病编码': str,
                '平急诊编码': str,
                '医保项目码': str
            }
            
            self.df_mg = pd.read_excel(self.excel_file_path, sheet_name='MG项目表', dtype=dtype_mg)
            print(f"✅ MG项目表加载成功: {self.df_mg.shape[0]} 行 x {self.df_mg.shape[1]} 列")
            
            # 显示列名信息
            print(f"📋 MG项目表包含以下字段:")
            for i, col in enumerate(self.df_mg.columns, 1):
                print(f"   {i:2d}. {col}")
                
        except Exception as e:
            print(f"❌ MG项目表加载失败: {e}")
            raise
    
    def clean_and_validate_data(self):
        """清理和验证MG项目数据"""
        print("\n🧹 正在清理和验证MG项目数据...")
        
        # 填充空值
        self.df_mg = self.df_mg.fillna('')
        
        # 确保编码字段为字符串
        encoding_columns = ['一级编码', '二级编码', '三级编码', '部位编码', '医保映射编码', 
                          '医保扩展码', '检查项目编码', '人群编码', '疾病编码', '平急诊编码', '医保项目码']
        
        for col in encoding_columns:
            if col in self.df_mg.columns:
                # 将数值转换为字符串，去除.0后缀
                self.df_mg[col] = self.df_mg[col].astype(str).str.replace('.0', '', regex=False)
        
        # 验证数据完整性
        issues = []
        
        # 检查必要字段是否为空
        required_fields = ['模态', '检查项目名称', '检查项目编码']
        for field in required_fields:
            if field in self.df_mg.columns:
                empty_count = (self.df_mg[field] == '').sum()
                if empty_count > 0:
                    issues.append(f"{field}字段有{empty_count}个空值")
        
        # 检查编码长度
        if '检查项目编码' in self.df_mg.columns:
            for idx, code in enumerate(self.df_mg['检查项目编码']):
                code_str = str(code)
                if len(code_str) != 16 and code_str != '':
                    issues.append(f"第{idx+1}行检查项目编码长度错误: {code_str} (长度: {len(code_str)})")
        
        if issues:
            print(f"⚠️  发现 {len(issues)} 个数据问题:")
            for issue in issues[:5]:  # 只显示前5个
                print(f"   - {issue}")
        else:
            print("✅ MG项目数据验证通过")
        
        return len(issues) == 0
    
    def generate_statistics(self):
        """生成MG项目统计信息"""
        print("\n📊 正在生成MG项目统计信息...")
        
        stats = {}
        
        # 基本统计
        stats['总项目数'] = len(self.df_mg)
        stats['唯一项目名称数'] = self.df_mg['检查项目名称'].nunique()
        stats['唯一编码数'] = self.df_mg['检查项目编码'].nunique()
        
        # 按部位统计
        if '三级部位' in self.df_mg.columns:
            stats['涉及部位数'] = self.df_mg['三级部位'].nunique()
            part_counts = self.df_mg['三级部位'].value_counts()
            stats['最常见部位'] = part_counts.index[0] if not part_counts.empty else '无'
        
        # 按摆位/技术统计
        if '摆位/技术' in self.df_mg.columns:
            stats['摆位技术种类'] = self.df_mg['摆位/技术'].nunique()
        
        # 编码分析
        if '人群编码' in self.df_mg.columns:
            non_zero_population = (self.df_mg['人群编码'] != '0').sum()
            stats['特定人群项目数'] = non_zero_population
        
        if '疾病编码' in self.df_mg.columns:
            non_zero_disease = (self.df_mg['疾病编码'] != '0').sum()
            stats['特定疾病项目数'] = non_zero_disease
        
        if '平急诊编码' in self.df_mg.columns:
            emergency_count = (self.df_mg['平急诊编码'] == '1').sum()
            stats['急诊项目数'] = emergency_count
        
        print("📈 MG项目统计结果:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        return stats
    
    def export_mg_projects(self, output_file=None):
        """导出MG项目到单独的Excel文件"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"../output/MG项目表_{timestamp}.xlsx"
        
        print(f"\n📤 正在导出MG项目到: {output_file}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 生成统计信息
        stats = self.generate_statistics()
        
        # 创建统计信息DataFrame
        stats_df = pd.DataFrame([
            {'统计项目': key, '数值': value} for key, value in stats.items()
        ])
        
        # 复制数据并格式化
        df_export = self.df_mg.copy()
        
        # 处理编码字段格式，避免Excel自动转换
        encoding_columns = ['一级编码', '二级编码', '三级编码', '部位编码', '医保映射编码', 
                          '医保扩展码', '检查项目编码', '人群编码', '疾病编码', '平急诊编码', '医保项目码']
        
        for col in encoding_columns:
            if col in df_export.columns:
                # 在编码前加单引号强制为文本格式
                df_export[col] = "'" + df_export[col].astype(str)
        
        # 按部位分组统计
        part_stats = []
        if '三级部位' in self.df_mg.columns:
            part_groups = self.df_mg.groupby('三级部位')
            for part_name, group in part_groups:
                part_stats.append({
                    '部位名称': part_name,
                    '项目数量': len(group),
                    '摆位技术': ', '.join(group['摆位/技术'].unique()) if '摆位/技术' in group.columns else ''
                })
        
        part_stats_df = pd.DataFrame(part_stats)
        
        # 导出到Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # MG项目主表
            df_export.to_excel(writer, sheet_name='MG项目表', index=False)
            
            # 统计信息
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
            
            # 按部位统计
            if not part_stats_df.empty:
                part_stats_df.to_excel(writer, sheet_name='部位统计', index=False)
        
        print(f"✅ MG项目导出完成: {output_file}")
        print(f"   📋 MG项目数: {len(self.df_mg)} 个")
        print(f"   📊 Excel包含: MG项目表、统计信息、部位统计 等sheet")
        
        return output_file
    
    def display_sample_data(self, num_samples=5):
        """显示MG项目示例数据"""
        print(f"\n📋 MG项目示例数据（前{num_samples}个项目）:")
        print("-" * 80)
        
        for i, (idx, row) in enumerate(self.df_mg.head(num_samples).iterrows()):
            print(f"{i+1}. {row.get('检查项目名称', 'N/A')} - {row.get('检查项目编码', 'N/A')}")
            print(f"   部位: {row.get('三级部位', 'N/A')}")
            print(f"   摆位/技术: {row.get('摆位/技术', 'N/A')}")
            print(f"   编码: 人群={row.get('人群编码', '0')} 疾病={row.get('疾病编码', '0')} 平急诊={row.get('平急诊编码', '0')}")
            print()

def main():
    """主函数 - MG项目处理程序入口"""
    print("🩻 MG项目表处理程序")
    print("=" * 60)
    
    # 数据文件路径
    excel_file = '../data/NEW_检查项目名称结构表 (11).xlsx'
    # 如果相对路径不行，使用绝对路径
    import os
    if not os.path.exists(excel_file):
        excel_file = '/Users/<USER>/Desktop/12-new/data/NEW_检查项目名称结构表 (11).xlsx'
    
    try:
        # 创建MG项目处理器
        processor = MGProjectProcessor(excel_file)
        
        # 清理和验证数据
        is_valid = processor.clean_and_validate_data()
        
        # 显示示例数据
        processor.display_sample_data()
        
        # 导出MG项目
        output_file = processor.export_mg_projects()
        
        print(f"\n🎉 MG项目处理完成!")
        print(f"   - 数据质量: {'✅ 通过' if is_valid else '⚠️ 有问题'}")
        print(f"   - 输出文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 