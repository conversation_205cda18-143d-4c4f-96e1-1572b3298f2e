#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医保编码项目生成器
根据医保编码sheet生成标准格式的检查项目清单
"""

import pandas as pd
import os
from datetime import datetime

def read_excel_sheets(file_path):
    """读取Excel文件的指定sheet"""
    try:
        # 读取医保编码sheet
        insurance_df = pd.read_excel(file_path, sheet_name='医保编码')
        print("医保编码sheet结构:")
        print(f"行数: {len(insurance_df)}")
        print(f"列数: {len(insurance_df.columns)}")
        print("列名:", list(insurance_df.columns))
        print("\n前5行数据:")
        print(insurance_df.head())

        print("\n" + "="*50 + "\n")

        # 读取模态表sheet
        modality_df = pd.read_excel(file_path, sheet_name='模态表')
        print("模态表sheet结构:")
        print(f"行数: {len(modality_df)}")
        print(f"列数: {len(modality_df.columns)}")
        print("列名:", list(modality_df.columns))
        print("\n前10行数据:")
        print(modality_df.head(10))

        return insurance_df, modality_df

    except Exception as e:
        print(f"读取文件出错: {e}")
        return None, None

def main():
    # 文件路径
    file_path = '../data/NEW_检查项目名称结构表 (11).xlsx'

    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return

    print(f"正在读取文件: {file_path}")
    insurance_df, modality_df = read_excel_sheets(file_path)

if __name__ == "__main__":
    main()