#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证增强后的统一输出文件
检查所有要求的功能是否正确实现
"""

import pandas as pd
import os
from datetime import datetime

def find_latest_output_file():
    """查找最新的输出文件"""
    output_dir = "../output"
    if not os.path.exists(output_dir):
        return None
    
    files = [f for f in os.listdir(output_dir) if f.startswith("完整检查项目清单_") and f.endswith(".xlsx")]
    if not files:
        return None
    
    # 按时间戳排序，返回最新的
    files.sort(reverse=True)
    return os.path.join(output_dir, files[0])

def verify_output_file(file_path):
    """验证输出文件"""
    print(f"🔍 正在验证文件: {os.path.basename(file_path)}")
    print("=" * 70)
    
    try:
        xls = pd.ExcelFile(file_path)
        all_sheets = xls.sheet_names
        
        # 要求的sheet清单
        required_sheets = {
            '主要项目': ['统计信息', 'CT&MR项目', 'CT项目', 'MR项目', 'CT-2', 'MR-2', 
                      'DR项目', 'DR-2', 'MG项目', 'MG-2', '医保项目', '医保-2'],
            '参考数据': ['医保编码', '模态表', 'DR项目名称清单', '扫描方式映射表', 'DR摆位-方向', 'DR摆位-体位']
        }
        
        print("📋 Sheet验证结果:")
        print("-" * 40)
        
        missing_sheets = []
        for category, sheets in required_sheets.items():
            print(f"\n{category}:")
            for sheet in sheets:
                if sheet in all_sheets:
                    df = pd.read_excel(file_path, sheet_name=sheet)
                    print(f"  ✅ {sheet:<15} ({len(df)}行)")
                else:
                    print(f"  ❌ {sheet:<15} (缺失)")
                    missing_sheets.append(sheet)
        
        print(f"\n💾 文件总sheet数: {len(all_sheets)}")
        print(f"📊 要求sheet数: {sum(len(sheets) for sheets in required_sheets.values())}")
        
        # 验证CT-2和MR-2字段顺序
        print("\n🔍 字段顺序验证:")
        print("-" * 40)
        
        expected_ct_mr_v2_order = [
            '模态', '医保映射码', '医保扩展码', '扫描方式', 
            '一级编码', '一级部位', '二级编码', '二级部位', 
            '三级编码', '部位编码', '三级部位', 
            '检查项目名称', '检查项目编码', 
            '人群编码', '疾病编码', '平/急诊编码'
        ]
        
        for sheet_name in ['CT-2', 'MR-2']:
            if sheet_name in all_sheets:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                actual_order = list(df.columns)
                
                if actual_order == expected_ct_mr_v2_order:
                    print(f"  ✅ {sheet_name} 字段顺序正确")
                else:
                    print(f"  ❌ {sheet_name} 字段顺序不正确")
                    print(f"     期望: {expected_ct_mr_v2_order[:5]}...")
                    print(f"     实际: {actual_order[:5]}...")
        
        # 验证统计信息整合
        print("\n📊 统计信息验证:")
        print("-" * 40)
        
        if '统计信息' in all_sheets:
            stats_df = pd.read_excel(file_path, sheet_name='统计信息')
            
            # 检查是否包含所有必要的统计类别
            categories = stats_df['类别'].unique()
            expected_categories = ['模块状态', '项目数量', '总体统计']
            
            print(f"  统计类别: {list(categories)}")
            
            for cat in expected_categories:
                if cat in categories:
                    count = len(stats_df[stats_df['类别'] == cat])
                    print(f"  ✅ {cat}: {count}项")
                else:
                    print(f"  ❌ {cat}: 缺失")
        
        # 验证数据排序
        print("\n🔄 排序验证:")
        print("-" * 40)
        
        # 检查CT-2和MR-2是否按医保映射码排序
        for sheet_name in ['CT-2', 'MR-2']:
            if sheet_name in all_sheets:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                if len(df) > 1:
                    is_sorted = df['医保映射码'].astype(str).is_monotonic_increasing
                    print(f"  {'✅' if is_sorted else '❌'} {sheet_name} 按医保映射码排序: {is_sorted}")
        
        # 项目数量统计
        print("\n📈 项目数量统计:")
        print("-" * 40)
        
        project_counts = {}
        for sheet in ['CT&MR项目', 'CT项目', 'MR项目', 'CT-2', 'MR-2', 
                     'DR项目', 'DR-2', 'MG项目', 'MG-2', '医保项目', '医保-2']:
            if sheet in all_sheets:
                df = pd.read_excel(file_path, sheet_name=sheet)
                project_counts[sheet] = len(df)
                print(f"  {sheet:<12}: {len(df):>4}个项目")
        
        # 验证一致性
        print("\n✅ 一致性验证:")
        print("-" * 40)
        
        if 'CT项目' in project_counts and 'CT-2' in project_counts:
            ct_match = project_counts['CT项目'] == project_counts['CT-2']
            print(f"  CT项目数量一致: {'✅' if ct_match else '❌'} ({project_counts.get('CT项目', 0)} vs {project_counts.get('CT-2', 0)})")
        
        if 'MR项目' in project_counts and 'MR-2' in project_counts:
            mr_match = project_counts['MR项目'] == project_counts['MR-2']
            print(f"  MR项目数量一致: {'✅' if mr_match else '❌'} ({project_counts.get('MR项目', 0)} vs {project_counts.get('MR-2', 0)})")
        
        if 'DR项目' in project_counts and 'DR-2' in project_counts:
            dr_match = project_counts['DR项目'] == project_counts['DR-2']
            print(f"  DR项目数量一致: {'✅' if dr_match else '❌'} ({project_counts.get('DR项目', 0)} vs {project_counts.get('DR-2', 0)})")
        
        # 总结
        print("\n🎉 验证总结:")
        print("-" * 40)
        
        total_required = sum(len(sheets) for sheets in required_sheets.values())
        total_found = len([s for sheets in required_sheets.values() for s in sheets if s in all_sheets])
        
        success_rate = (total_found / total_required) * 100 if total_required > 0 else 0
        
        print(f"Sheet完成度: {total_found}/{total_required} ({success_rate:.1f}%)")
        print(f"缺失sheet: {len(missing_sheets)}个")
        
        if missing_sheets:
            print(f"缺失的sheet: {', '.join(missing_sheets)}")
        
        if success_rate >= 90:
            print("🎉 验证通过！所有主要功能都已正确实现。")
        else:
            print("⚠️ 验证未完全通过，存在一些问题。")
        
        return success_rate >= 90
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("🔍 增强统一输出文件验证工具")
    print("=" * 70)
    print(f"📅 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 查找最新的输出文件
    latest_file = find_latest_output_file()
    
    if not latest_file:
        print("❌ 未找到输出文件，请先运行统一pipeline")
        return
    
    if not os.path.exists(latest_file):
        print(f"❌ 文件不存在: {latest_file}")
        return
    
    # 验证文件
    success = verify_output_file(latest_file)
    
    print(f"\n📁 验证文件: {latest_file}")
    print(f"📊 验证结果: {'✅ 通过' if success else '❌ 存在问题'}")

if __name__ == "__main__":
    main() 