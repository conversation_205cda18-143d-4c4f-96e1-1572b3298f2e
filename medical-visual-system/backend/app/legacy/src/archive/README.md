# 代码归档目录

本目录包含开发过程中的验证脚本和已废弃的代码文件。

## 📁 目录结构

```
src/archive/
├── verification/               # 验证和测试脚本
│   ├── verify_enhanced_output.py
│   ├── verify_output.py
│   ├── verify_unified_format.py
│   ├── compare_before_after.py
│   ├── check_extension_codes.py
│   └── view_results.py
├── deprecated/                 # 已废弃的代码
│   ├── medical_insurance_processor.py
│   ├── data_processor_v2_fixed.py
│   └── dr_sheet_processor.py
└── README.md                   # 本文件
```

## 📋 文件说明

### 验证脚本 (verification/)

开发过程中用于测试和验证的脚本：

- `verify_enhanced_output.py` - 增强输出验证
- `verify_unified_format.py` - 统一格式验证  
- `compare_before_after.py` - 前后对比验证
- `check_extension_codes.py` - 扩展码检查
- `view_results.py` - 结果查看工具

### 已废弃代码 (deprecated/)

被新版本替代的旧代码：

- `medical_insurance_processor.py` → 被 `medical_insurance_generator.py` 替代
- `data_processor_v2_fixed.py` → 旧版本数据处理器
- `dr_sheet_processor.py` → 被 `dr_project_generator.py` 替代

## ⚠️ 使用注意

- 这些文件已不再维护，仅用于参考
- 如需运行系统，请使用根目录的核心文件
- 验证脚本可用于调试，但可能需要适配新的数据格式

---

**归档日期**: 2025年7月10日  
**归档原因**: 代码整理和优化 