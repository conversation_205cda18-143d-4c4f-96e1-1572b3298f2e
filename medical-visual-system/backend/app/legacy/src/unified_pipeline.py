#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一医疗检查项目处理Pipeline
整合CT/MR、DR、MG、医保四个不同的处理流程，合并输出到一个文件中
保持每个模块的原有处理逻辑不变
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys

# 导入四个处理模块
from medical_project_generator import MedicalProjectGenerator
from dr_project_generator import DRProjectGenerator  
from mg_project_processor import MGProjectProcessor
from medical_insurance_generator import MedicalInsuranceGenerator

class UnifiedMedicalPipeline:
    """统一医疗检查项目处理Pipeline"""
    
    def __init__(self, excel_file_path):
        """初始化Pipeline"""
        self.excel_file_path = excel_file_path
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results = {}
        
    def process_ctmr_projects(self):
        """处理CT/MR项目"""
        print("🩻 正在处理CT/MR项目...")
        print("-" * 50)
        
        try:
            generator = MedicalProjectGenerator(self.excel_file_path)
            projects = generator.generate_projects()
            is_valid = generator.validate_projects(projects)
            
            # 分离CT和MR项目
            ct_projects = [p for p in projects if p['模态'] == 'CT']
            mr_projects = [p for p in projects if p['模态'] == 'MR']
            
            # 创建CT-2和MR-2格式（按医保映射码排序）
            ct_projects_v2 = self.create_v2_format(ct_projects)
            mr_projects_v2 = self.create_v2_format(mr_projects)
            
            self.results['ctmr'] = {
                'all_projects': projects,
                'ct_projects': ct_projects,
                'mr_projects': mr_projects,
                'ct_projects_v2': ct_projects_v2,
                'mr_projects_v2': mr_projects_v2,
                'is_valid': is_valid,
                'stats': {
                    'total': len(projects),
                    'ct_count': len(ct_projects),
                    'mr_count': len(mr_projects),
                    'unique_parts': len(set([p['三级部位'] for p in projects]))
                }
            }
            
            print(f"✅ CT/MR处理完成: 总计{len(projects)}个项目 (CT:{len(ct_projects)}, MR:{len(mr_projects)})")
            
        except Exception as e:
            print(f"❌ CT/MR处理失败: {e}")
            self.results['ctmr'] = {'error': str(e)}
    
    def process_dr_projects(self):
        """处理DR项目"""
        print("\n🦴 正在处理DR项目...")
        print("-" * 50)
        
        try:
            generator = DRProjectGenerator(self.excel_file_path)
            projects = generator.generate_projects()
            is_valid, duplicate_projects = generator.validate_projects(projects)
            
            # 创建DR-2视图（使用专门的DR V2格式：医保映射码在模态和一级编码之间，添加体位方向字段）
            dr_projects_v2 = self.create_dr_v2_format(projects)
            
            self.results['dr'] = {
                'projects': projects,
                'projects_v2': dr_projects_v2,
                'duplicate_projects': duplicate_projects,
                'is_valid': is_valid,
                'stats': {
                    'total': len(projects),
                    'duplicates': len(duplicate_projects) if duplicate_projects else 0,
                    'unique_parts': len(set([p['三级部位'] for p in projects])),
                    'unique_poses': len(set([p['摄影体位'] for p in projects if p['摄影体位']]))
                }
            }
            
            print(f"✅ DR处理完成: 总计{len(projects)}个项目 (重复:{len(duplicate_projects) if duplicate_projects else 0}个)")
            
        except Exception as e:
            print(f"❌ DR处理失败: {e}")
            self.results['dr'] = {'error': str(e)}
    
    def process_mg_projects(self):
        """处理MG项目"""
        print("\n🔍 正在处理MG项目...")
        print("-" * 50)
        
        try:
            processor = MGProjectProcessor(self.excel_file_path)
            is_valid = processor.clean_and_validate_data()
            stats = processor.generate_statistics()
            
            # 获取MG项目数据
            mg_data = processor.df_mg.to_dict('records')
            
            # 创建MG-2视图（医保映射码在模态和一级编码之间）
            mg_data_v2 = self.create_mg_insurance_v2_format(mg_data)
            
            self.results['mg'] = {
                'projects': mg_data,
                'projects_v2': mg_data_v2,
                'is_valid': is_valid,
                'stats': stats,
                'raw_df': processor.df_mg.copy()
            }
            
            print(f"✅ MG处理完成: 总计{len(mg_data)}个项目")
            
        except Exception as e:
            print(f"❌ MG处理失败: {e}")
            self.results['mg'] = {'error': str(e)}
    
    def process_insurance_projects(self):
        """处理医保项目"""
        print("\n💊 正在处理医保项目...")
        print("-" * 50)
        
        try:
            generator = MedicalInsuranceGenerator(self.excel_file_path)
            projects = generator.generate_projects()
            is_valid, duplicate_projects = generator.validate_projects(projects)
            
            # 按模态分组统计
            modality_stats = {}
            for project in projects:
                modality = project['模态']
                modality_stats[modality] = modality_stats.get(modality, 0) + 1
            
            # 创建医保-2视图（医保映射码在模态和一级编码之间）
            insurance_projects_v2 = self.create_mg_insurance_v2_format(projects)
            
            self.results['insurance'] = {
                'projects': projects,
                'projects_v2': insurance_projects_v2,
                'duplicate_projects': duplicate_projects,
                'is_valid': is_valid,
                'stats': {
                    'total': len(projects),
                    'duplicates': len(duplicate_projects) if duplicate_projects else 0,
                    'modality_distribution': modality_stats
                }
            }
            
            print(f"✅ 医保处理完成: 总计{len(projects)}个项目 (重复:{len(duplicate_projects) if duplicate_projects else 0}个)")
            print(f"   模态分布: {modality_stats}")
            
        except Exception as e:
            print(f"❌ 医保处理失败: {e}")
            self.results['insurance'] = {'error': str(e)}
    
    def format_encoding_fields(self, df):
        """格式化编码字段，防止Excel自动转换"""
        encoding_columns = ['一级编码', '二级编码', '三级编码', '部位编码', 
                          '人群编码', '疾病编码', '平急诊编码', '平/急诊编码',
                          '检查项目编码', '医保映射码', '医保扩展码', '摄影体位编码',
                          '医保映射编码', '医保项目码']
        
        for col in encoding_columns:
            if col in df.columns:
                df[col] = "'" + df[col].astype(str)
        
        return df
    
    def create_v2_format(self, projects_list):
        """创建V2格式的DataFrame（按医保映射码排序）"""
        if not projects_list:
            return []
        
        # 指定的字段顺序（CT-2/MR-2格式）
        v2_columns = [
            '模态', '医保映射码', '医保扩展码', '扫描方式', 
            '一级编码', '一级部位', '二级编码', '二级部位', 
            '三级编码', '部位编码', '三级部位', 
            '检查项目名称', '检查项目编码', 
            '人群编码', '疾病编码', '平/急诊编码'
        ]
        
        # 重新排列数据
        v2_data = []
        for project in projects_list:
            v2_project = {}
            for col in v2_columns:
                v2_project[col] = project.get(col, '')
            v2_data.append(v2_project)
        
        # 按医保映射码排序
        v2_data_sorted = sorted(v2_data, key=lambda x: (
            x.get('医保映射码', ''), 
            x.get('医保扩展码', ''),
            x.get('扫描方式', ''),
            x.get('一级编码', ''),
            x.get('二级编码', ''),
            x.get('三级编码', ''),
            x.get('部位编码', '')
        ))
        
        return v2_data_sorted
    
    def create_dr_v2_format(self, projects_list):
        """为DR项目创建V2格式（特殊字段顺序）"""
        if not projects_list:
            return []
        
        # DR-2特殊字段顺序：医保映射码在模态和一级编码之间，添加体位和方向字段
        dr_v2_columns = [
            '模态', '医保映射码', '医保扩展码',
            '一级编码', '一级部位', '二级编码', '二级部位', 
            '三级编码', '部位编码', '三级部位', 
            '摄影体位', '摄影体位编码', '体位', '体位编码', '方向', '方向编码',
            '检查项目名称', '检查项目编码', 
            '人群编码', '疾病编码', '平/急诊编码'
        ]
        
        # 重新排列数据并添加缺失字段
        v2_data = []
        for project in projects_list:
            v2_project = {}
            for col in dr_v2_columns:
                if col == '医保扩展码':
                    # DR项目的医保扩展码使用部位编码+摄影体位编码
                    v2_project[col] = project.get('部位编码', '') + project.get('摄影体位编码', '')
                else:
                    v2_project[col] = project.get(col, '')
            v2_data.append(v2_project)
        
        # 按指定优先级排序
        v2_data_sorted = sorted(v2_data, key=lambda x: (
            x.get('医保映射码', ''), 
            x.get('医保扩展码', ''),
            x.get('一级编码', ''),
            x.get('二级编码', ''),
            x.get('三级编码', ''),
            x.get('体位编码', ''),
            x.get('方向编码', ''),
            x.get('摄影体位编码', '')
        ))
        
        return v2_data_sorted
    
    def create_mg_insurance_v2_format(self, projects_list):
        """为MG/医保项目创建V2格式（医保映射码在模态和一级编码之间）"""
        if not projects_list:
            return []
        
        # 获取第一个项目的所有字段来确定顺序
        if not projects_list:
            return []
        
        first_project = projects_list[0]
        all_columns = list(first_project.keys())
        
        # 重新排列字段：医保映射码、医保扩展码在模态和一级编码之间
        v2_columns = []
        
        # 添加模态
        if '模态' in all_columns:
            v2_columns.append('模态')
        
        # 添加医保映射码和医保扩展码
        if '医保映射码' in all_columns:
            v2_columns.append('医保映射码')
        elif '医保映射编码' in all_columns:
            v2_columns.append('医保映射编码')
        
        if '医保扩展码' in all_columns:
            v2_columns.append('医保扩展码')
        
        # 添加其余字段（除了已添加的）
        for col in all_columns:
            if col not in v2_columns:
                v2_columns.append(col)
        
        # 重新排列数据
        v2_data = []
        for project in projects_list:
            v2_project = {}
            for col in v2_columns:
                v2_project[col] = project.get(col, '')
            v2_data.append(v2_project)
        
        # 按医保映射码排序
        v2_data_sorted = sorted(v2_data, key=lambda x: (
            x.get('医保映射码', x.get('医保映射编码', '')), 
            x.get('医保扩展码', ''),
            x.get('一级编码', ''),
            x.get('二级编码', ''),
            x.get('三级编码', ''),
            x.get('部位编码', '')
        ))
        
        return v2_data_sorted
    
    def create_insurance_sorted_format(self, projects_list):
        """创建按医保映射码排序的第二视图（保持原有功能）"""
        if not projects_list:
            return []
        
        # 按医保映射码排序
        projects_sorted = sorted(projects_list, key=lambda x: (
            x.get('医保映射码', x.get('医保映射编码', '')), 
            x.get('医保扩展码', ''),
            x.get('一级编码', ''),
            x.get('二级编码', ''),
            x.get('三级编码', ''),
            x.get('部位编码', '')
        ))
        
        return projects_sorted
    
    def create_unified_statistics(self):
        """创建统一的统计信息"""
        print("\n📊 正在生成统一统计信息...")
        
        total_projects = 0
        all_stats = []
        
        # 总体统计
        processing_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        successful_modules = 0
        
        # 汇总各模块统计
        for module_name, module_data in self.results.items():
            if 'error' in module_data:
                all_stats.append({
                    '类别': '模块状态',
                    '项目': f"{module_name.upper()}模块",
                    '数值': "❌ 错误",
                    '备注': module_data['error']
                })
                continue
            
            if module_name == 'ctmr':
                count = module_data['stats']['total']
                detail = f"CT:{module_data['stats']['ct_count']}, MR:{module_data['stats']['mr_count']}"
                all_stats.extend([
                    {'类别': '模块状态', '项目': 'CT&MR模块', '数值': '✅ 成功', '备注': detail},
                    {'类别': '项目数量', '项目': 'CT&MR项目总数', '数值': count, '备注': ''},
                    {'类别': '项目数量', '项目': 'CT项目数', '数值': module_data['stats']['ct_count'], '备注': ''},
                    {'类别': '项目数量', '项目': 'MR项目数', '数值': module_data['stats']['mr_count'], '备注': ''},
                ])
            elif module_name == 'dr':
                count = module_data['stats']['total']
                detail = f"重复:{module_data['stats']['duplicates']}个"
                all_stats.extend([
                    {'类别': '模块状态', '项目': 'DR模块', '数值': '✅ 成功', '备注': detail},
                    {'类别': '项目数量', '项目': 'DR项目数', '数值': count, '备注': ''},
                ])
            elif module_name == 'mg':
                count = module_data['stats']['总项目数']
                detail = f"部位数:{module_data['stats'].get('涉及部位数', 0)}"
                all_stats.extend([
                    {'类别': '模块状态', '项目': 'MG模块', '数值': '✅ 成功', '备注': detail},
                    {'类别': '项目数量', '项目': 'MG项目数', '数值': count, '备注': ''},
                ])
            elif module_name == 'insurance':
                count = module_data['stats']['total']
                detail = f"重复:{module_data['stats']['duplicates']}个"
                all_stats.extend([
                    {'类别': '模块状态', '项目': '医保模块', '数值': '✅ 成功', '备注': detail},
                    {'类别': '项目数量', '项目': '医保项目数', '数值': count, '备注': ''},
                ])
            
            total_projects += count
            successful_modules += 1
        
        # 添加总体统计
        all_stats.extend([
            {'类别': '总体统计', '项目': '处理时间', '数值': processing_time, '备注': ''},
            {'类别': '总体统计', '项目': '总项目数', '数值': total_projects, '备注': ''},
            {'类别': '总体统计', '项目': '成功模块数', '数值': f"{successful_modules}/4", '备注': ''},
            {'类别': '总体统计', '项目': '数据源文件', '数值': os.path.basename(self.excel_file_path), '备注': ''},
        ])
        
        return pd.DataFrame(all_stats)
    
    def create_project_documentation(self):
        """创建详细的项目说明文档"""
        
        # 字段含义和规范说明
        field_definitions = [
            {'类别': '字段定义', '项目': '模态', '说明': 'CT/MR/DR/MG等影像检查类型，用于区分不同的医学影像设备', '规范': '字符型，固定值'},
            {'类别': '字段定义', '项目': '一级编码', '说明': '检查部位的一级分类编码', '规范': '1位字符，1-9'},
            {'类别': '字段定义', '项目': '一级部位', '说明': '检查部位的一级分类名称', '规范': '中文名称，如"头部"、"胸部"'},
            {'类别': '字段定义', '项目': '二级编码', '说明': '检查部位的二级分类编码', '规范': '2位字符，01-99'},
            {'类别': '字段定义', '项目': '二级部位', '说明': '检查部位的二级分类名称', '规范': '中文名称，更具体的部位描述'},
            {'类别': '字段定义', '项目': '三级编码', '说明': '检查部位的三级分类编码（最详细）', '规范': '2位字符，01-99'},
            {'类别': '字段定义', '项目': '三级部位', '说明': '检查部位的三级分类名称（最详细）', '规范': '中文名称，最具体的解剖部位'},
            {'类别': '字段定义', '项目': '部位编码', '说明': '完整的部位编码（一级+二级+三级）', '规范': '5位字符，格式：1XXYY'},
            {'类别': '字段定义', '项目': '医保映射码', '说明': '与医保系统对应的映射编码', '规范': '6位字符，CT:2XXXXX, MR:3XXXXX, DR:110000'},
            {'类别': '字段定义', '项目': '医保扩展码', '说明': '医保映射的扩展编码，用于细分项目', '规范': '2位编码，CT/MR表示项目的扩展，DR表中为摄影体位编码'},
            {'类别': '字段定义', '项目': '扫描方式', '说明': 'CT/MR特有，描述扫描技术和对比剂使用', '规范': '中文描述，如"平扫"、"增强"'},
            {'类别': '字段定义', '项目': '摄影体位', '说明': 'DR特有，描述患者检查时的体位摆放', '规范': '中文描述，如"后前正位"、"左侧位"'},
            {'类别': '字段定义', '项目': '摄影体位编码', '说明': 'DR摄影体位的数字编码', '规范': '2位字符，对应摄影体位的数字代码'},
            {'类别': '字段定义', '项目': '检查项目名称', '说明': '完整的检查项目名称', '规范': '按模态+部位+技术组合生成'},
            {'类别': '字段定义', '项目': '检查项目编码', '说明': '唯一的检查项目编码', '规范': '16位字符，按特定规则组合生成'},
            {'类别': '字段定义', '项目': '人群编码', '说明': '特定人群标识', '规范': '1位字符，1:胎儿 2:新生儿 3:儿童 4:孕妇'},
            {'类别': '字段定义', '项目': '疾病编码', '说明': '特定疾病标识', '规范': '1位字符，1:外伤 2:认知 3:癫痫 4:PICC 5:卒中 6:胸痛'},
            {'类别': '字段定义', '项目': '平/急诊编码', '说明': '就诊类型标识', '规范': '1位字符，0:平诊 1:急诊'},
        ]
        
        # 名称组合规则
        naming_rules = [
            {'类别': '名称规则', '项目': 'CT项目', '说明': 'CT + 三级部位 + (扫描方式)', '规范': '如：CT头颅(平扫)、CT胸部(增强)'},
            {'类别': '名称规则', '项目': 'MR项目', '说明': 'MR + 三级部位 + (扫描方式)', '规范': '如：MR头颅(平扫)、MR脊柱(增强)'},
            {'类别': '名称规则', '项目': 'DR项目', '说明': 'DR + 三级部位 + 摄影体位', '规范': '如：DR胸部后前正位、DR颈椎左侧位'},
            {'类别': '名称规则', '项目': 'MG项目', '说明': '基于医保项目名称', '规范': '如：双侧乳腺钼靶摄影、单侧乳腺钼靶摄影'},
            {'类别': '名称规则', '项目': '医保项目', '说明': '基于医保编码表中的项目名称', '规范': '直接使用医保系统标准名称'},
        ]
        
        # 编码组合规则
        coding_rules = [
            {'类别': '编码规则', '项目': 'CT编码', '说明': '医保映射码(6位) + 部位编码(5位) + 医保扩展码(2位) + 人群码 + 疾病码' + '平急诊码', '规范': '总长16位'},
            {'类别': '编码规则', '项目': 'MR编码', '说明': '医保映射码(6位) + 部位编码(5位) + 医保扩展码(2位) + 人群码 + 疾病码' + '平急诊码', '规范': '总长16位'},
            {'类别': '编码规则', '项目': 'DR编码', '说明': '110000 + 部位编码(5位) + 摄影体位编码(2位) + 人群码 + 疾病码' + '平/急诊码', '规范': '总长16位'},
            {'类别': '编码规则', '项目': 'MG编码', '说明': '基于医保项目码生成', '规范': '遵循医保编码标准'},
            {'类别': '编码规则', '项目': '医保编码', '说明': '直接使用医保系统标准编码', '规范': '按医保规范执行'},
        ]
        
        # 医保映射关系
        insurance_mapping = [
            {'类别': '医保映射', '项目': 'CT映射', '说明': '2开头的医保映射码对应CT检查', '规范': '20-24开头，精确匹配优先'},
            {'类别': '医保映射', '项目': 'MR映射', '说明': '3开头的医保映射码对应MR检查', '规范': '30-35开头，支持字母后缀'},
            {'类别': '医保映射', '项目': 'DR映射', '说明': '11开头的医保映射码对应DR检查', '规范': '110000固定值'},
            {'类别': '医保映射', '项目': 'MG映射', '说明': '13开头的医保映射码对应MG检查', '规范': '130000系列'},
            {'类别': '医保映射', '项目': '其他映射', '说明': '12:IO, 14:RF, 40:OT等其他影像检查', '规范': '按模态表精确映射'},
            {'类别': '医保映射', '项目': '映射优先级', '说明': '优先使用精确映射，无匹配时使用首位默认映射', '规范': '双层映射机制保证100%覆盖'},
        ]
        
        # 摄影体位关系说明
        position_relations = [
            {'类别': '摄影体位关系', '项目': '摄影体位组成', '说明': '摄影体位 = 体位 + 方向的组合描述', '规范': '如："后前正位"由"俯卧位"+"后前位"组成'},
            {'类别': '摄影体位关系', '项目': '体位类型', '说明': '俯卧位、仰卧位、侧卧位、立位等', '规范': '描述患者身体的基本姿势'},
            {'类别': '摄影体位关系', '项目': '方向类型', '说明': '后前位、前后位、左侧位、右侧位等', '规范': '描述X线投照的方向'},
            {'类别': '摄影体位关系', '项目': '编码对应', '说明': '每种体位和方向都有对应的2位数字编码', '规范': '便于系统处理和数据分析'},
            {'类别': '摄影体位关系', '项目': '摄影体位编码生成', '说明': '摄影体位编码基于体位编码和方向编码生成', '规范': '确保摄影体位描述的唯一性'},
        ]
        
        # 模态与部位关系
        modality_relations = [
            {'类别': '模态关系', '项目': 'CT适用部位', '说明': '全身各部位，特别适用于骨骼、胸腹部检查', '规范': '覆盖头部、胸部、腹部、骨盆等'},
            {'类别': '模态关系', '项目': 'MR适用部位', '说明': '软组织、神经系统、关节检查', '规范': '头颅、脊柱、关节、盆腔等软组织'},
            {'类别': '模态关系', '项目': 'DR适用部位', '说明': '骨骼系统、胸部常规检查', '规范': '胸部、四肢、脊柱等骨骼结构'},
            {'类别': '模态关系', '项目': 'MG适用部位', '说明': '乳腺专项检查', '规范': '左乳腺、右乳腺、双侧乳腺'},
            {'类别': '模态关系', '项目': '部位编码统一', '说明': '所有模态共享同一套三级部位编码体系', '规范': '确保不同检查的部位编码一致性'},
            {'类别': '模态关系', '项目': '技术差异', '说明': '不同模态有各自的技术参数和检查方式', '规范': '扫描方式(CT/MR)vs摄影体位(DR)vs技术(MG)'},
        ]
        
        # 项目分析
        project_analysis = [
            {'类别': '项目分析', '项目': '总体规模', '说明': f'生成{sum([self.results.get(m, {}).get("stats", {}).get("total", 0) for m in ["ctmr", "dr", "mg", "insurance"]])}个检查项目', '规范': '覆盖四大影像检查类型'},
            {'类别': '项目分析', '项目': 'CT项目数量', '说明': f'{self.results.get("ctmr", {}).get("stats", {}).get("ct_count", 0)}个CT检查项目', '规范': '涵盖全身各部位的CT检查'},
            {'类别': '项目分析', '项目': 'MR项目数量', '说明': f'{self.results.get("ctmr", {}).get("stats", {}).get("mr_count", 0)}个MR检查项目', '规范': '重点关注软组织和神经系统'},
            {'类别': '项目分析', '项目': 'DR项目数量', '说明': f'{self.results.get("dr", {}).get("stats", {}).get("total", 0)}个DR检查项目', '规范': '包含多种摄影体位的骨骼检查'},
            {'类别': '项目分析', '项目': 'MG项目数量', '说明': f'{self.results.get("mg", {}).get("stats", {}).get("总项目数", 0)}个MG检查项目', '规范': '乳腺专项筛查和诊断'},
            {'类别': '项目分析', '项目': '医保项目数量', '说明': f'{self.results.get("insurance", {}).get("stats", {}).get("total", 0)}个医保项目', '规范': '与医保系统完全对接'},
            {'类别': '项目分析', '项目': '编码唯一性', '说明': '所有项目编码保证唯一性，无重复冲突', '规范': '通过多级验证确保数据质量'},
            {'类别': '项目分析', '项目': '标准化程度', '说明': '统一的编码规范和命名规则', '规范': '符合医疗信息化标准要求'},
            {'类别': '项目分析', '项目': '扩展能力', '说明': '支持新增部位、新增摄影体位、新增扫描方式', '规范': '架构设计具有良好的可扩展性'},
            {'类别': '项目分析', '项目': '质量控制', '说明': '多层次数据验证和质量检查机制', '规范': '确保输出数据的准确性和完整性'},
        ]
        
        # 合并所有说明
        all_documentation = (field_definitions + naming_rules + coding_rules + 
                           insurance_mapping + position_relations + 
                           modality_relations + project_analysis)
        
        return pd.DataFrame(all_documentation)
    
    def load_reference_data(self):
        """加载原始数据表作为参考"""
        try:
            reference_sheets = {
                '医保编码': '医保编码',
                '模态表': '模态表',
                'DR项目名称清单': 'DR项目名称清单',
                '扫描方式映射表': '扫描方式医保映射编码',
                'DR摄影体位-方向': 'DR摆位_方向',
                'DR摄影体位-体位': 'DR摆位_体位'
            }
            
            reference_data = {}
            for target_name, source_name in reference_sheets.items():
                try:
                    df = pd.read_excel(self.excel_file_path, sheet_name=source_name)
                    reference_data[target_name] = df
                    print(f"✅ 已加载参考数据: {target_name} ({len(df)}行)")
                except Exception as e:
                    print(f"⚠️ 无法加载参考数据 {target_name}: {e}")
                    
            return reference_data
            
        except Exception as e:
            print(f"❌ 加载参考数据失败: {e}")
            return {}
    
    def export_unified_results(self, output_file=None):
        """导出统一的结果文件"""
        if not output_file:
            output_file = f"../output/完整检查项目清单_{self.timestamp}.xlsx"
        
        print(f"\n📁 正在导出统一结果到: {output_file}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 创建统一统计信息
        unified_stats = self.create_unified_statistics()
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 1. 写入统一统计信息
            unified_stats.to_excel(writer, sheet_name='统计信息', index=False)
            
            # 1.5. 写入详细项目说明文档
            project_docs = self.create_project_documentation()
            if not project_docs.empty:
                project_docs.to_excel(writer, sheet_name='项目说明', index=False)
            
            # 2. 写入CT/MR项目（移除合并表格）
            if 'ctmr' in self.results and 'error' not in self.results['ctmr']:
                ctmr_data = self.results['ctmr']
                
                # CT项目
                if ctmr_data['ct_projects']:
                    df_ct = pd.DataFrame(ctmr_data['ct_projects'])
                    df_ct = self.format_encoding_fields(df_ct)
                    df_ct.to_excel(writer, sheet_name='CT项目', index=False)
                
                # MR项目
                if ctmr_data['mr_projects']:
                    df_mr = pd.DataFrame(ctmr_data['mr_projects'])
                    df_mr = self.format_encoding_fields(df_mr)
                    df_mr.to_excel(writer, sheet_name='MR项目', index=False)
                
                # CT-2项目（V2格式）
                if 'ct_projects_v2' in ctmr_data and ctmr_data['ct_projects_v2']:
                    df_ct_v2 = pd.DataFrame(ctmr_data['ct_projects_v2'])
                    df_ct_v2 = self.format_encoding_fields(df_ct_v2)
                    df_ct_v2.to_excel(writer, sheet_name='CT-2', index=False)
                
                # MR-2项目（V2格式）
                if 'mr_projects_v2' in ctmr_data and ctmr_data['mr_projects_v2']:
                    df_mr_v2 = pd.DataFrame(ctmr_data['mr_projects_v2'])
                    df_mr_v2 = self.format_encoding_fields(df_mr_v2)
                    df_mr_v2.to_excel(writer, sheet_name='MR-2', index=False)
            
            # 3. 写入DR项目
            if 'dr' in self.results and 'error' not in self.results['dr']:
                dr_data = self.results['dr']
                
                # DR项目主表
                df_dr = pd.DataFrame(dr_data['projects'])
                df_dr = self.format_encoding_fields(df_dr)
                df_dr.to_excel(writer, sheet_name='DR项目', index=False)
                
                # DR-2项目（按医保映射码排序）
                if 'projects_v2' in dr_data and dr_data['projects_v2']:
                    df_dr_v2 = pd.DataFrame(dr_data['projects_v2'])
                    df_dr_v2 = self.format_encoding_fields(df_dr_v2)
                    df_dr_v2.to_excel(writer, sheet_name='DR-2', index=False)
                
                # DR重复项目（如果有）
                if dr_data.get('duplicate_projects'):
                    df_dr_dup = pd.DataFrame(dr_data['duplicate_projects'])
                    df_dr_dup = self.format_encoding_fields(df_dr_dup)
                    df_dr_dup.to_excel(writer, sheet_name='DR重复项目', index=False)
            
            # 4. 写入MG项目
            if 'mg' in self.results and 'error' not in self.results['mg']:
                mg_data = self.results['mg']
                
                # MG项目表
                df_mg = mg_data['raw_df'].copy()
                df_mg = self.format_encoding_fields(df_mg)
                df_mg.to_excel(writer, sheet_name='MG项目', index=False)
                
                # MG-2项目（按医保映射码排序）
                if 'projects_v2' in mg_data and mg_data['projects_v2']:
                    df_mg_v2 = pd.DataFrame(mg_data['projects_v2'])
                    df_mg_v2 = self.format_encoding_fields(df_mg_v2)
                    df_mg_v2.to_excel(writer, sheet_name='MG-2', index=False)
            
            # 5. 写入医保项目
            if 'insurance' in self.results and 'error' not in self.results['insurance']:
                ins_data = self.results['insurance']
                
                # 医保项目主表
                df_ins = pd.DataFrame(ins_data['projects'])
                df_ins = self.format_encoding_fields(df_ins)
                df_ins.to_excel(writer, sheet_name='医保项目', index=False)
                
                # 医保-2项目（按医保映射码排序）
                if 'projects_v2' in ins_data and ins_data['projects_v2']:
                    df_ins_v2 = pd.DataFrame(ins_data['projects_v2'])
                    df_ins_v2 = self.format_encoding_fields(df_ins_v2)
                    df_ins_v2.to_excel(writer, sheet_name='医保-2', index=False)
                
                # 医保重复项目（如果有）
                if ins_data.get('duplicate_projects'):
                    df_ins_dup = pd.DataFrame(ins_data['duplicate_projects'])
                    df_ins_dup = self.format_encoding_fields(df_ins_dup)
                    df_ins_dup.to_excel(writer, sheet_name='医保重复项目', index=False)
            
            # 6. 添加参考数据sheets
            reference_data = self.load_reference_data()
            for sheet_name, df in reference_data.items():
                if not df.empty:
                    try:
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                        print(f"✅ 已添加参考数据sheet: {sheet_name}")
                    except Exception as e:
                        print(f"⚠️ 添加参考数据sheet失败 {sheet_name}: {e}")
        
        print(f"✅ 统一结果导出完成: {output_file}")
        self.print_export_summary()
        
        return output_file
    
    def print_export_summary(self):
        """打印导出摘要"""
        print(f"\n📋 统一导出摘要:")
        print("=" * 60)
        
        total_successful = 0
        total_projects = 0
        
        for module_name, module_data in self.results.items():
            if 'error' in module_data:
                print(f"❌ {module_name.upper()}: 处理失败 - {module_data['error']}")
                continue
            
            if module_name == 'ctmr':
                count = module_data['stats']['total']
                detail = f"(CT:{module_data['stats']['ct_count']}, MR:{module_data['stats']['mr_count']})"
            elif module_name == 'dr':
                count = module_data['stats']['total']
                detail = f"(重复:{module_data['stats']['duplicates']}个)"
            elif module_name == 'mg':
                count = module_data['stats']['总项目数']
                detail = ""
            elif module_name == 'insurance':
                count = module_data['stats']['total']
                detail = f"(重复:{module_data['stats']['duplicates']}个)"
            
            print(f"✅ {module_name.upper()}: {count}个项目 {detail}")
            total_successful += 1
            total_projects += count
        
        print("-" * 60)
        print(f"🎉 总计: {total_projects}个检查项目，{total_successful}/4个模块处理成功")
    
    def run_full_pipeline(self, output_file=None):
        """运行完整的Pipeline"""
        print("🏥 统一医疗检查项目处理Pipeline")
        print("=" * 60)
        print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📂 数据源: {self.excel_file_path}")
        print()
        
        # 执行四个处理流程
        self.process_ctmr_projects()
        self.process_dr_projects()
        self.process_mg_projects()
        self.process_insurance_projects()
        
        # 导出统一结果
        output_file = self.export_unified_results(output_file)
        
        print(f"\n🎉 Pipeline执行完成!")
        print(f"📄 输出文件: {output_file}")
        
        return output_file

def main():
    """主函数"""
    print("🚀 启动统一医疗检查项目处理Pipeline")
    print("=" * 70)
    
    try:
        # 数据文件路径
        excel_file = "../data/NEW_检查项目名称结构表 (11).xlsx"
        
        # 检查文件是否存在
        if not os.path.exists(excel_file):
            print(f"❌ 数据文件不存在: {excel_file}")
            return
        
        # 创建并运行Pipeline
        pipeline = UnifiedMedicalPipeline(excel_file)
        output_file = pipeline.run_full_pipeline()
        
        print(f"\n✨ 所有处理流程已完成，统一输出文件已生成!")
        print(f"📁 查看结果: {output_file}")
        
    except Exception as e:
        print(f"❌ Pipeline执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 