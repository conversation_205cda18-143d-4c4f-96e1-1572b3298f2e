#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DR检查项目清单生成器
基于 NEW_检查项目名称结构表 (11).xlsx 中的 DR项目名称清单 数据源

根据用户要求生成DR检查项目：
- 名称格式：DR + 三级部位 + "-" + 摆位
- 编码格式："110000" + "部位编码"（5位）+ "医保扩展码"
- 医保扩展码 = "摆位编码" +  "疾病编码" + "人群编码" + "平/急诊编码 "
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

class DRProjectGenerator:
    """DR检查项目生成器"""
    
    def __init__(self, excel_file_path):
        """初始化生成器"""
        self.excel_file_path = excel_file_path
        self.df_dr = None
        self.df_population = None
        self.df_disease = None
        self.load_data()
        
    def load_data(self):
        """加载数据源"""
        print("🔄 正在加载数据源...")
        try:
            # 加载DR项目名称清单
            self.df_dr = pd.read_excel(self.excel_file_path, sheet_name='DR项目名称清单')
            print(f"✅ DR项目名称清单: {self.df_dr.shape[0]} 行 x {self.df_dr.shape[1]} 列")
            
            # 加载人群表
            self.df_population = pd.read_excel(self.excel_file_path, sheet_name='人群表')
            print(f"✅ 人群表: {self.df_population.shape[0]} 行 x {self.df_population.shape[1]} 列")
            
            # 加载疾病表
            self.df_disease = pd.read_excel(self.excel_file_path, sheet_name='疾病表')
            print(f"✅ 疾病表: {self.df_disease.shape[0]} 行 x {self.df_disease.shape[1]} 列")
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise
    
    def create_mapping_dicts(self):
        """创建人群和疾病的映射字典"""
        print("\n📚 正在创建映射字典...")
        
        # 创建人群映射字典
        population_dict = {}
        for _, row in self.df_population.iterrows():
            population_dict[row['人群名称']] = str(row['编码'])
        
        # 创建疾病映射字典  
        disease_dict = {}
        for _, row in self.df_disease.iterrows():
            disease_dict[row['疾病名称']] = str(row['疾病编码'])
        
        print(f"✅ 人群映射: {population_dict}")
        print(f"✅ 疾病映射: {disease_dict}")
        
        return population_dict, disease_dict
    
    def get_population_code(self, population_name, population_dict):
        """获取人群编码"""
        if pd.isna(population_name):
            return '0'  # 默认编码
        
        population_name = str(population_name).strip()
        return population_dict.get(population_name, '0')
    
    def get_disease_code(self, disease_name, disease_dict):
        """获取疾病编码"""
        if pd.isna(disease_name):
            return '0'  # 默认编码
        
        disease_name = str(disease_name).strip()
        return disease_dict.get(disease_name, '0')
    
    def get_emergency_code(self, emergency_type):
        """获取平急诊编码"""
        if pd.isna(emergency_type):
            return '0'  # 默认编码：平诊
        
        emergency_type = str(emergency_type).strip()
        # 根据实际数据定义编码规则
        if emergency_type == '急诊':
            return '1'
        elif emergency_type == '平诊':
            return '0'
        else:
            return '0'  # 默认编码
    
    def generate_project_name(self, part_name, pose_name):
        """生成项目名称：DR + 三级部位 + "-" + 摆位"""
        part_name = str(part_name) if not pd.isna(part_name) else ""
        pose_name = str(pose_name) if not pd.isna(pose_name) else ""
        
        return f"DR{part_name}-{pose_name}"
    
    def generate_project_code(self, part_code, pose_code, population_code, disease_code, emergency_code):
        """生成项目编码：110000 + 部位编码(5位) + 医保扩展码"""
        # 确保部位编码为5位
        part_code = str(part_code).zfill(5)
        
        # 确保摆位编码为2位
        pose_code = str(pose_code) if not pd.isna(pose_code) else "00"
        if len(pose_code) == 1:
            pose_code = pose_code.zfill(2)
        
        # 医保扩展码 = 摆位编码(2位) + 人群编码(1位) + 疾病编码(1位) + 平急诊编码(1位)
        extension_code = pose_code + population_code + disease_code + emergency_code
        
        # 最终编码：110000 + 部位编码(5位) + 医保扩展码(5位) = 16位
        return "110000" + part_code + extension_code
    
    def generate_projects(self):
        """生成DR检查项目"""
        print(f"\n🏭 正在生成DR检查项目清单...")
        
        population_dict, disease_dict = self.create_mapping_dicts()
        
        projects = []
        
        for idx, row in self.df_dr.iterrows():
            # 获取编码
            population_code = self.get_population_code(row['人群'], population_dict)
            disease_code = self.get_disease_code(row['疾病'], disease_dict)
            emergency_code = self.get_emergency_code(row['平/急诊'])
            
            # 生成项目名称
            project_name = self.generate_project_name(row['三级部位'], row['摆位'])
            
            # 生成项目编码
            project_code = self.generate_project_code(
                row['部位编码'], 
                row['摆位编码'], 
                population_code, 
                disease_code, 
                emergency_code
            )
            
            # 创建项目记录 - DR项目专用字段
            摆位码 = str(row['摆位编码']).zfill(2) if not pd.isna(row['摆位编码']) else "00"
            体位编码 = str(row['体位编码']).zfill(2) if not pd.isna(row['体位编码']) else "00"
            方向编码 = str(row['方向编码']).zfill(2) if not pd.isna(row['方向编码']) else "00"
            
            # 按要求的字段顺序排列，体位相关字段在摆位和人群编码之间
            from collections import OrderedDict
            project = OrderedDict([
                ('模态', 'DR'),
                ('一级编码', str(row['一级编码'])),
                ('一级部位', str(row['一级部位']) if not pd.isna(row['一级部位']) else ""),
                ('二级编码', str(row['二级编码']).zfill(2)),
                ('二级部位', str(row['二级部位']) if not pd.isna(row['二级部位']) else ""),
                ('三级编码', str(row['三级编码']).zfill(2)),
                ('部位编码', str(row['部位编码']).zfill(5)),
                ('三级部位', str(row['三级部位']) if not pd.isna(row['三级部位']) else ""),
                ('摆位', str(row['摆位']) if not pd.isna(row['摆位']) else ""),
                ('摆位码', 摆位码),
                ('体位', str(row['体位']) if not pd.isna(row['体位']) else ""),
                ('体位编码', 体位编码),
                ('方向', str(row['方向']) if not pd.isna(row['方向']) else ""),
                ('方向编码', 方向编码),
                ('检查项目名称', project_name),
                ('检查项目编码', project_code),
                ('医保映射码', '110000'),
                ('人群编码', population_code),
                ('疾病编码', disease_code),
                ('平急诊编码', emergency_code),
                # 保留扩展字段
                ('人群', str(row['人群']) if not pd.isna(row['人群']) else ""),
                ('疾病', str(row['疾病']) if not pd.isna(row['疾病']) else ""),
                ('平/急诊', str(row['平/急诊']) if not pd.isna(row['平/急诊']) else "")
            ])
            
            projects.append(project)
        
        # 按照字段优先级排序：一级编码→二级编码→三级编码→体位编码→方向编码→摆位码
        projects_sorted = sorted(projects, key=lambda x: (
            x.get('一级编码', ''),
            x.get('二级编码', ''), 
            x.get('三级编码', ''),
            x.get('体位编码', ''),
            x.get('方向编码', ''),
            x.get('摆位码', '')
        ))
        
        print(f"✅ 项目生成完成，共生成 {len(projects_sorted)} 个DR检查项目")
        return projects_sorted
    
    def validate_projects(self, projects):
        """验证项目数据质量"""
        print(f"\n🔍 正在验证项目数据质量...")
        
        issues = []
        duplicate_projects = []
        
        # 检查项目编码长度
        for project in projects:
            code_length = len(project['检查项目编码'])
            if code_length != 16:
                issues.append(f"项目编码长度错误: {project['检查项目名称']} - {project['检查项目编码']} (长度:{code_length})")
        
        # 检查项目编码唯一性并收集重复项目
        codes = [p['检查项目编码'] for p in projects]
        duplicate_codes = [code for code in set(codes) if codes.count(code) > 1]
        
        if duplicate_codes:
            issues.append(f"发现重复的项目编码: {len(duplicate_codes)} 个重复编码")
            # 收集所有重复编码的项目
            for project in projects:
                if project['检查项目编码'] in duplicate_codes:
                    duplicate_projects.append(project)
        
        # 检查编码格式
        code_format_issues = 0
        for project in projects:
            code = project['检查项目编码']
            if not code.startswith('110000'):
                code_format_issues += 1
        
        if code_format_issues > 0:
            issues.append(f"编码格式错误的项目数量: {code_format_issues}")
        
        # 输出验证结果
        if issues:
            print("⚠️  发现以下问题:")
            for issue in issues[:10]:  # 最多显示10个问题
                print(f"   - {issue}")
            if duplicate_projects:
                print(f"   - 重复项目详情将导出到单独sheet中，共 {len(duplicate_projects)} 个项目")
        else:
            print("✅ 数据质量验证通过")
        
        return len(issues) == 0, duplicate_projects
    
    def export_results(self, projects, duplicate_projects=None, output_file=None):
        """导出结果到Excel文件"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"../output/DR检查项目清单_{timestamp}.xlsx"
        
        print(f"\n📁 正在导出结果到: {output_file}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 创建DataFrame
        df_projects = pd.DataFrame(projects)
        
        # 根据编码格式修正说明，为避免Excel自动转换为数字，在编码字段前加单引号
        def format_encoding_fields(df):
            """格式化编码字段，防止Excel自动转换"""
            encoding_columns = ['一级编码', '二级编码', '三级编码', '部位编码', 
                              '人群编码', '疾病编码', '平急诊编码', '检查项目编码', '医保映射码', '摆位码']
            
            for col in encoding_columns:
                if col in df.columns:
                    df[col] = "'" + df[col].astype(str)
            
            return df
        
        # 格式化编码字段
        df_projects = format_encoding_fields(df_projects)
        
        # 创建统计信息
        stats_data = {
            '统计项': ['总项目数', '项目编码长度验证', '编码格式验证', '生成时间'],
            '数值': [
                len(projects),
                '通过' if all(len(p['检查项目编码']) == 16 for p in projects) else '失败',
                '通过' if all(p['检查项目编码'].startswith('110000') for p in projects) else '失败',
                datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ]
        }
        df_stats = pd.DataFrame(stats_data)
        df_stats = format_encoding_fields(df_stats)
        
        # 人群统计
        population_counts = df_projects['人群'].value_counts().reset_index()
        population_counts.columns = ['人群', '项目数量']
        df_population_stats = format_encoding_fields(population_counts)
        
        # 疾病统计
        disease_counts = df_projects['疾病'].value_counts().reset_index()
        disease_counts.columns = ['疾病', '项目数量']
        df_disease_stats = format_encoding_fields(disease_counts)
        
        # 部位统计
        part_counts = df_projects['三级部位'].value_counts().reset_index()
        part_counts.columns = ['三级部位', '项目数量']
        df_part_stats = format_encoding_fields(part_counts)
        
        # 处理重复项目数据
        df_duplicates = None
        if duplicate_projects and len(duplicate_projects) > 0:
            df_duplicates = pd.DataFrame(duplicate_projects)
            df_duplicates = format_encoding_fields(df_duplicates)
            # 按编码排序，便于查看重复项目
            df_duplicates = df_duplicates.sort_values('检查项目编码')
        
        # 写入Excel文件，包含多个sheet
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df_projects.to_excel(writer, sheet_name='DR检查项目清单', index=False)
            df_stats.to_excel(writer, sheet_name='统计信息', index=False)
            df_population_stats.to_excel(writer, sheet_name='人群统计', index=False)
            df_disease_stats.to_excel(writer, sheet_name='疾病统计', index=False)
            df_part_stats.to_excel(writer, sheet_name='部位统计', index=False)
            
            # 如果有重复项目，添加重复项目sheet
            if df_duplicates is not None:
                df_duplicates.to_excel(writer, sheet_name='重复编码项目', index=False)
        
        print(f"✅ 导出完成！文件包含以下sheet:")
        print(f"   - DR检查项目清单: {len(projects)} 个项目")
        print(f"   - 统计信息: 生成概览")
        print(f"   - 人群统计: {len(population_counts)} 个人群")
        print(f"   - 疾病统计: {len(disease_counts)} 个疾病")
        print(f"   - 部位统计: {len(part_counts)} 个部位")
        if df_duplicates is not None:
            print(f"   - 重复编码项目: {len(df_duplicates)} 个重复项目")
        
        return output_file

def main():
    """主函数"""
    print("🏥 DR检查项目清单生成器")
    print("=" * 50)
    
    try:
        # 初始化生成器
        excel_file = "../data/NEW_检查项目名称结构表 (11).xlsx"
        generator = DRProjectGenerator(excel_file)
        
        # 生成项目
        projects = generator.generate_projects()
        
        # 验证数据质量
        is_valid, duplicate_projects = generator.validate_projects(projects)
        
        # 导出结果
        output_file = generator.export_results(projects, duplicate_projects)
        
        print(f"\n🎉 DR检查项目清单生成完成！")
        print(f"📄 输出文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 