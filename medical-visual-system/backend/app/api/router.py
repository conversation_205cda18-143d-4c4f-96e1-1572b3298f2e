#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目可视化数据处理系统 - API路由配置
创建时间: 2025-01-11
版本: v1.0
"""

from fastapi import APIRouter
from app.api.endpoints import (
    system,
    data_dict,
    projects,
    rules,
    upload
)

# 创建主API路由器
api_router = APIRouter()

# 注册子路由模块
api_router.include_router(
    system.router,
    prefix="/system",
    tags=["系统管理"]
)

api_router.include_router(
    data_dict.router,
    prefix="/data-dict",
    tags=["数据字典"]
)

api_router.include_router(
    projects.router,
    prefix="/projects",
    tags=["项目管理"]
)

api_router.include_router(
    rules.router,
    prefix="/rules",
    tags=["规则配置"]
)

api_router.include_router(
    upload.router,
    prefix="/upload",
    tags=["文件上传"]
) 