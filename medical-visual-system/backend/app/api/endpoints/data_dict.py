#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目可视化数据处理系统 - 数据字典API
创建时间: 2025-01-11
版本: v1.0
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Dict, Any, Optional
from app.core.database import get_db

router = APIRouter()

@router.get("/modalities", summary="获取模态字典")
async def get_modalities(
    active_only: bool = Query(True, description="是否只返回启用的模态"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取所有模态字典数据
    """
    try:
        # 构建查询条件
        where_clause = "WHERE is_active = 1" if active_only else ""
        
        query = f"""
        SELECT 
            id, mapping_code_prefix, modality_name, modality_desc,
            default_mapping, sort_order, is_active, created_at
        FROM modality_dict 
        {where_clause}
        ORDER BY sort_order, modality_name
        """
        
        result = db.execute(text(query))
        modalities = []
        
        for row in result:
            modalities.append({
                "id": row[0],
                "mapping_code_prefix": row[1],
                "modality_name": row[2],
                "modality_desc": row[3],
                "default_mapping": bool(row[4]),
                "sort_order": row[5],
                "is_active": bool(row[6]),
                "created_at": str(row[7])
            })
        
        return {
            "success": True,
            "data": modalities,
            "total": len(modalities),
            "message": f"获取到 {len(modalities)} 个模态记录"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模态字典失败: {str(e)}")

@router.get("/populations", summary="获取人群字典")
async def get_populations(
    active_only: bool = Query(True, description="是否只返回启用的人群"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取所有人群字典数据
    """
    try:
        where_clause = "WHERE is_active = 1" if active_only else ""
        
        query = f"""
        SELECT 
            id, population_code, population_name, description,
            sort_order, is_active, created_at
        FROM population_dict 
        {where_clause}
        ORDER BY sort_order, population_code
        """
        
        result = db.execute(text(query))
        populations = []
        
        for row in result:
            populations.append({
                "id": row[0],
                "population_code": row[1],
                "population_name": row[2],
                "description": row[3],
                "sort_order": row[4],
                "is_active": bool(row[5]),
                "created_at": str(row[6])
            })
        
        return {
            "success": True,
            "data": populations,
            "total": len(populations),
            "message": f"获取到 {len(populations)} 个人群记录"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取人群字典失败: {str(e)}")

@router.get("/diseases", summary="获取疾病字典")
async def get_diseases(
    active_only: bool = Query(True, description="是否只返回启用的疾病"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取所有疾病字典数据
    """
    try:
        where_clause = "WHERE is_active = 1" if active_only else ""
        
        query = f"""
        SELECT 
            id, disease_code, disease_name, description,
            sort_order, is_active, created_at
        FROM disease_dict 
        {where_clause}
        ORDER BY sort_order, disease_code
        """
        
        result = db.execute(text(query))
        diseases = []
        
        for row in result:
            diseases.append({
                "id": row[0],
                "disease_code": row[1],
                "disease_name": row[2],
                "description": row[3],
                "sort_order": row[4],
                "is_active": bool(row[5]),
                "created_at": str(row[6])
            })
        
        return {
            "success": True,
            "data": diseases,
            "total": len(diseases),
            "message": f"获取到 {len(diseases)} 个疾病记录"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取疾病字典失败: {str(e)}")

@router.get("/scan-mappings", summary="获取扫描方式映射")
async def get_scan_mappings(
    modality: Optional[str] = Query(None, description="按模态筛选"),
    active_only: bool = Query(True, description="是否只返回启用的映射"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取扫描方式映射数据
    """
    try:
        conditions = []
        if active_only:
            conditions.append("is_active = 1")
        if modality:
            conditions.append(f"modality = '{modality}'")
        
        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
        
        query = f"""
        SELECT 
            id, scan_method, scan_method_short, insurance_mapping_code,
            insurance_extension_code, modality, applicable_parts,
            contrast_agent, sort_order, is_active, created_at
        FROM scan_mapping 
        {where_clause}
        ORDER BY modality, sort_order, insurance_mapping_code
        """
        
        result = db.execute(text(query))
        scan_mappings = []
        
        for row in result:
            scan_mappings.append({
                "id": row[0],
                "scan_method": row[1],
                "scan_method_short": row[2],
                "insurance_mapping_code": row[3],
                "insurance_extension_code": row[4],
                "modality": row[5],
                "applicable_parts": row[6],
                "contrast_agent": bool(row[7]) if row[7] is not None else False,
                "sort_order": row[8],
                "is_active": bool(row[9]),
                "created_at": str(row[10])
            })
        
        return {
            "success": True,
            "data": scan_mappings,
            "total": len(scan_mappings),
            "message": f"获取到 {len(scan_mappings)} 个扫描映射记录"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取扫描方式映射失败: {str(e)}")

@router.get("/body-parts", summary="获取部位数据")
async def get_body_parts(
    modality: Optional[str] = Query(None, description="按模态筛选部位"),
    level1: Optional[str] = Query(None, description="按一级部位筛选"),
    search: Optional[str] = Query(None, description="搜索部位名称、编码"),
    active_only: bool = Query(True, description="是否只返回启用的部位"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取部位数据，支持分页和按模态筛选
    """
    try:
        conditions = []
        if active_only:
            conditions.append("is_active = 1")
        if level1:
            conditions.append(f"level1_code = '{level1}'")
        if search:
            # 搜索部位名称、编码
            search_condition = f"(level1_name LIKE '%{search}%' OR level2_name LIKE '%{search}%' OR level3_name LIKE '%{search}%' OR level1_code LIKE '%{search}%' OR level2_code LIKE '%{search}%' OR level3_code LIKE '%{search}%' OR part_code LIKE '%{search}%')"
            conditions.append(search_condition)
        
        # 模态筛选
        if modality:
            modality_upper = modality.upper()
            if modality_upper in ['CT', 'MR', 'DR', 'RF', 'MG', 'OT']:
                conditions.append(f"is_applicable_{modality_upper.lower()} = 1")
        
        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
        
        # 分页计算
        offset = (page - 1) * page_size
        
        # 查询总数
        count_query = f"SELECT COUNT(*) FROM body_parts {where_clause}"
        total_result = db.execute(text(count_query))
        total = total_result.scalar()
        
        # 查询数据
        query = f"""
        SELECT 
            id, level1_code, level1_name, level2_code, level2_name,
            level3_code, level3_name, part_code, part_code_text,
            is_applicable_ct, is_applicable_mr, is_applicable_dr,
            is_applicable_rf, is_applicable_mg, is_applicable_ot,
            is_active, created_at
        FROM body_parts 
        {where_clause}
        ORDER BY level1_code, level2_code, level3_code
        LIMIT {page_size} OFFSET {offset}
        """
        
        result = db.execute(text(query))
        body_parts = []
        
        for row in result:
            # 计算适用的模态列表
            applicable_modalities = []
            if row[9]: applicable_modalities.append("CT")
            if row[10]: applicable_modalities.append("MR")
            if row[11]: applicable_modalities.append("DR")
            if row[12]: applicable_modalities.append("RF")
            if row[13]: applicable_modalities.append("MG")
            if row[14]: applicable_modalities.append("OT")
            
            body_parts.append({
                "id": row[0],
                "level1_code": row[1],
                "level1_name": row[2],
                "level2_code": row[3],
                "level2_name": row[4],
                "level3_code": row[5],
                "level3_name": row[6],
                "part_code": row[7],
                "part_code_text": row[8],
                "modality_applicability": {
                    "ct": bool(row[9]),
                    "mr": bool(row[10]),
                    "dr": bool(row[11]),
                    "rf": bool(row[12]),
                    "mg": bool(row[13]),
                    "ot": bool(row[14])
                },
                "applicable_modalities": applicable_modalities,
                "is_active": bool(row[15]),
                "created_at": str(row[16])
            })
        
        return {
            "success": True,
            "data": body_parts,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "total_pages": (total + page_size - 1) // page_size
            },
            "message": f"获取到 {len(body_parts)} 个部位记录"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取部位数据失败: {str(e)}")

@router.get("/body-parts/modality-stats", summary="获取部位模态适用性统计")
async def get_body_parts_modality_stats(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取部位表的模态适用性统计信息
    """
    try:
        modalities = ['ct', 'mr', 'dr', 'rf', 'mg', 'ot']
        stats = {}
        
        for modality in modalities:
            query = f"SELECT COUNT(*) FROM body_parts WHERE is_applicable_{modality} = 1 AND is_active = 1"
            result = db.execute(text(query))
            count = result.scalar()
            stats[modality.upper()] = count
        
        # 总部位数
        total_query = "SELECT COUNT(*) FROM body_parts WHERE is_active = 1"
        total_result = db.execute(text(total_query))
        total_parts = total_result.scalar()
        
        return {
            "success": True,
            "data": {
                "total_parts": total_parts,
                "modality_stats": stats,
                "percentage": {
                    modality: round((count / total_parts * 100), 2) if total_parts > 0 else 0
                    for modality, count in stats.items()
                }
            },
            "message": "模态适用性统计获取成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模态统计失败: {str(e)}")

@router.get("/summary", summary="获取数据字典汇总信息")
async def get_data_dict_summary(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取所有数据字典的汇总信息
    """
    try:
        tables = [
            ("body_parts", "部位表"),
            ("modality_dict", "模态字典"),
            ("population_dict", "人群字典"),
            ("disease_dict", "疾病字典"),
            ("scan_mapping", "扫描方式映射")
        ]
        
        summary = {}
        for table_name, table_desc in tables:
            # 活跃记录数
            active_query = f"SELECT COUNT(*) FROM {table_name} WHERE is_active = 1"
            active_result = db.execute(text(active_query))
            active_count = active_result.scalar()
            
            # 总记录数
            total_query = f"SELECT COUNT(*) FROM {table_name}"
            total_result = db.execute(text(total_query))
            total_count = total_result.scalar()
            
            summary[table_name] = {
                "table_desc": table_desc,
                "active_count": active_count,
                "total_count": total_count,
                "inactive_count": total_count - active_count
            }
        
        return {
            "success": True,
            "data": summary,
            "message": "数据字典汇总信息获取成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取汇总信息失败: {str(e)}") 