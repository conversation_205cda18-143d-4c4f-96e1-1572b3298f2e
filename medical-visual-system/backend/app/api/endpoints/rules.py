from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db

router = APIRouter()

@router.get("/encoding")
def get_encoding_rules(db: Session = Depends(get_db)):
    """获取编码规则"""
    return {"message": "编码规则API - 待实现"}

@router.get("/naming")
def get_naming_rules(db: Session = Depends(get_db)):
    """获取命名规则"""
    return {"message": "命名规则API - 待实现"} 