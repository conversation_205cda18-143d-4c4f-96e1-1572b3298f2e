from fastapi import APIRouter, Depends, UploadFile, File
from sqlalchemy.orm import Session
from app.core.database import get_db

router = APIRouter()

@router.post("/excel")
async def upload_excel(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传Excel文件"""
    return {"message": "Excel上传API - 待实现", "filename": file.filename}

@router.get("/history")
def get_upload_history(db: Session = Depends(get_db)):
    """获取上传历史"""
    return {"message": "上传历史API - 待实现"} 