from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db

router = APIRouter()

@router.get("/")
def get_projects(db: Session = Depends(get_db)):
    """获取项目列表"""
    return {"message": "项目管理API - 待实现"}

@router.get("/generate")
def generate_projects(db: Session = Depends(get_db)):
    """生成项目"""
    return {"message": "项目生成API - 待实现"}

@router.get("/batches")
def get_batches(db: Session = Depends(get_db)):
    """获取生成批次"""
    return {"message": "批次管理API - 待实现"} 