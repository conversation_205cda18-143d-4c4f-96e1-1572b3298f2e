#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目可视化数据处理系统 - 系统管理API
创建时间: 2025-01-11
版本: v1.0
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any
import time
import psutil
import platform
from datetime import datetime

from app.core.database import get_db, check_database_health, get_database_stats
from app.core.config import settings

router = APIRouter()

@router.get("/status", summary="获取系统状态")
async def get_system_status(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取系统运行状态
    """
    return {
        "status": "running",
        "timestamp": time.time(),
        "datetime": datetime.now().isoformat(),
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "database": check_database_health()
    }

@router.get("/info", summary="获取系统信息")
async def get_system_info() -> Dict[str, Any]:
    """
    获取详细的系统信息
    """
    # 系统信息
    system_info = {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "architecture": platform.architecture()[0],
        "processor": platform.processor(),
        "hostname": platform.node()
    }
    
    # 内存信息
    memory = psutil.virtual_memory()
    memory_info = {
        "total": memory.total,
        "available": memory.available,
        "percent": memory.percent,
        "used": memory.used,
        "free": memory.free
    }
    
    # CPU信息
    cpu_info = {
        "count": psutil.cpu_count(),
        "percent": psutil.cpu_percent(interval=1),
        "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
    }
    
    # 磁盘信息
    disk = psutil.disk_usage('/')
    disk_info = {
        "total": disk.total,
        "used": disk.used,
        "free": disk.free,
        "percent": (disk.used / disk.total) * 100
    }
    
    return {
        "application": {
            "name": settings.PROJECT_NAME,
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
            "debug": settings.DEBUG
        },
        "system": system_info,
        "resources": {
            "memory": memory_info,
            "cpu": cpu_info,
            "disk": disk_info
        },
        "timestamp": datetime.now().isoformat()
    }

@router.get("/database", summary="获取数据库信息")
async def get_database_info(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取数据库详细信息
    """
    return {
        "health": check_database_health(),
        "statistics": get_database_stats(),
        "configuration": {
            "url": str(settings.DATABASE_URL).replace(
                str(settings.DATABASE_URL).split('@')[0].split(':')[-1] + '@', 
                "***@"
            ) if '@' in str(settings.DATABASE_URL) else str(settings.DATABASE_URL),
            "pool_size": settings.DB_POOL_SIZE,
            "max_overflow": settings.DB_MAX_OVERFLOW,
            "echo": settings.DB_ECHO
        }
    }

@router.get("/config", summary="获取系统配置")
async def get_system_config() -> Dict[str, Any]:
    """
    获取系统配置信息（脱敏后）
    """
    return {
        "application": {
            "name": settings.PROJECT_NAME,
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
            "debug": settings.DEBUG,
            "host": settings.HOST,
            "port": settings.PORT
        },
        "features": {
            "max_upload_size": settings.MAX_UPLOAD_SIZE,
            "allowed_extensions": settings.ALLOWED_EXTENSIONS,
            "excel_max_rows": settings.EXCEL_MAX_ROWS,
            "cache_enabled": settings.CACHE_ENABLED,
            "metrics_enabled": settings.ENABLE_METRICS
        },
        "limits": {
            "max_workers": settings.MAX_WORKERS,
            "task_timeout": settings.TASK_TIMEOUT,
            "batch_process_size": settings.BATCH_PROCESS_SIZE
        },
        "cors": {
            "origins": settings.CORS_ORIGINS,
            "allowed_hosts": settings.ALLOWED_HOSTS
        }
    }

@router.post("/cache/clear", summary="清理系统缓存")
async def clear_cache() -> Dict[str, Any]:
    """
    清理系统缓存
    """
    # 这里可以添加具体的缓存清理逻辑
    return {
        "success": True,
        "message": "缓存清理完成",
        "timestamp": datetime.now().isoformat()
    }

@router.get("/logs", summary="获取系统日志")
async def get_system_logs(
    lines: int = 100,
    level: str = "INFO"
) -> Dict[str, Any]:
    """
    获取系统日志
    """
    try:
        import os
        log_file = os.path.join(settings.LOG_DIR, settings.LOG_FILE)
        
        if not os.path.exists(log_file):
            return {
                "logs": [],
                "message": "日志文件不存在",
                "total_lines": 0
            }
        
        # 读取最后N行日志
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            
        # 过滤日志级别
        filtered_lines = []
        for line in all_lines[-lines:]:
            if level.upper() in line or level == "ALL":
                filtered_lines.append(line.strip())
        
        return {
            "logs": filtered_lines,
            "total_lines": len(filtered_lines),
            "level_filter": level,
            "lines_requested": lines,
            "log_file": log_file
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取日志文件失败: {str(e)}")

@router.get("/metrics", summary="获取系统指标")
async def get_system_metrics() -> Dict[str, Any]:
    """
    获取系统性能指标
    """
    if not settings.ENABLE_METRICS:
        raise HTTPException(status_code=404, detail="系统指标收集未启用")
    
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # 内存使用率
    memory = psutil.virtual_memory()
    
    # 磁盘使用率
    disk = psutil.disk_usage('/')
    
    # 网络IO（如果可用）
    network = psutil.net_io_counters()
    
    return {
        "timestamp": datetime.now().isoformat(),
        "cpu": {
            "percent": cpu_percent,
            "count": psutil.cpu_count()
        },
        "memory": {
            "total": memory.total,
            "used": memory.used,
            "percent": memory.percent
        },
        "disk": {
            "total": disk.total,
            "used": disk.used,
            "percent": (disk.used / disk.total) * 100
        },
        "network": {
            "bytes_sent": network.bytes_sent,
            "bytes_recv": network.bytes_recv,
            "packets_sent": network.packets_sent,
            "packets_recv": network.packets_recv
        } if network else None
    } 