# ================================================================
# 医疗检查项目可视化数据处理系统 - Python依赖
# ================================================================
# 版本: v1.0
# 创建时间: 2025-01-11
# Python版本要求: >=3.8
# ================================================================

# ================================================================
# 核心Web框架
# ================================================================
fastapi==0.104.1                # 现代Python Web框架
uvicorn[standard]==0.24.0        # ASGI服务器
starlette==0.27.0               # ASGI框架核心
pydantic==2.5.0                 # 数据验证和设置管理
pydantic-settings==2.1.0        # Pydantic设置管理

# ================================================================
# 数据库相关
# ================================================================
sqlalchemy==2.0.23             # SQL工具包和ORM
alembic==1.13.0                 # 数据库迁移工具
psycopg2-binary==2.9.9          # PostgreSQL适配器
# sqlite3                       # SQLite数据库（Python内置，不需要安装）

# ================================================================
# Excel和数据处理
# ================================================================
pandas==2.1.4                   # 数据分析和处理
openpyxl==3.1.2                 # Excel读写支持(.xlsx)
xlrd==2.0.1                     # Excel读取支持(.xls)
xlwt==1.3.0                     # Excel写入支持(.xls)

# ================================================================
# 文件处理
# ================================================================
python-multipart==0.0.6         # 文件上传支持
aiofiles==23.2.1                # 异步文件操作

# ================================================================
# 系统监控和工具
# ================================================================
psutil==5.9.6                   # 系统和进程工具
python-json-logger==2.0.7       # 结构化日志

# ================================================================
# 安全和认证
# ================================================================
python-jose[cryptography]==3.3.0 # JWT令牌处理
passlib[bcrypt]==1.7.4          # 密码哈希
bcrypt==4.1.2                   # 密码加密

# ================================================================
# HTTP客户端和工具
# ================================================================
httpx==0.25.2                   # 异步HTTP客户端
requests==2.31.0                # HTTP库

# ================================================================
# 开发和测试工具
# ================================================================
pytest==7.4.3                   # 测试框架
pytest-asyncio==0.21.1          # 异步测试支持
pytest-cov==4.1.0               # 测试覆盖率
black==23.11.0                  # 代码格式化
flake8==6.1.0                   # 代码检查
mypy==1.7.1                     # 静态类型检查

# ================================================================
# 配置和环境
# ================================================================
python-dotenv==1.0.0            # 环境变量管理
click==8.1.7                    # 命令行工具

# ================================================================
# 异步和并发
# ================================================================
# asyncio                        # 异步编程（Python内置，不需要安装）
# concurrent.futures             # 并发处理（Python内置，不需要安装）

# ================================================================
# 时间和日期处理
# ================================================================
python-dateutil==2.8.2          # 日期工具扩展

# ================================================================
# 数据序列化
# ================================================================
orjson==3.9.10                  # 快速JSON序列化

# ================================================================
# 可选依赖（根据需要安装）
# ================================================================
# redis==5.0.1                  # Redis缓存（如果使用Redis）
# celery==5.3.4                 # 任务队列（如果使用Celery）
# gunicorn==21.2.0               # WSGI服务器（生产环境）

# ================================================================
# 开发环境特定依赖
# ================================================================
# jupyter==1.0.0                # Jupyter笔记本（可选）
# ipython==8.17.2               # 增强的Python交互环境（可选） 