#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗检查项目可视化系统 - 数据库初始化脚本
用途: 快速创建数据库、表结构和基础数据
适用: 开发环境快速搭建、测试环境重置
"""

import os
import sys
import sqlite3
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根路径到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self, db_path: str = None, db_type: str = "sqlite"):
        """
        初始化数据库初始化器
        
        Args:
            db_path: 数据库文件路径（SQLite）或连接字符串（PostgreSQL）
            db_type: 数据库类型 ("sqlite" 或 "postgresql")
        """
        self.db_type = db_type
        self.db_path = db_path or self._get_default_db_path()
        self.schema_file = Path(__file__).parent / "schema.sql"
        
    def _get_default_db_path(self):
        """获取默认数据库路径"""
        if self.db_type == "sqlite":
            return str(Path(__file__).parent.parent / "medical_visual_system.db")
        else:
            return "postgresql://user:password@localhost/medical_visual_system"
    
    def create_database(self):
        """创建数据库"""
        print("🔄 开始初始化数据库...")
        print(f"📍 数据库类型: {self.db_type}")
        print(f"📍 数据库路径: {self.db_path}")
        
        if self.db_type == "sqlite":
            self._create_sqlite_database()
        else:
            self._create_postgresql_database()
    
    def _create_sqlite_database(self):
        """创建 SQLite 数据库"""
        try:
            # 确保数据库目录存在
            db_dir = Path(self.db_path).parent
            db_dir.mkdir(parents=True, exist_ok=True)
            
            # 如果数据库文件已存在，先备份
            if os.path.exists(self.db_path):
                backup_path = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.rename(self.db_path, backup_path)
                print(f"📦 已备份原数据库到: {backup_path}")
            
            # 创建新数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 启用外键约束
            cursor.execute("PRAGMA foreign_keys = ON;")
            
            # 读取并执行 schema.sql
            print("📋 正在执行数据库架构脚本...")
            with open(self.schema_file, 'r', encoding='utf-8') as f:
                schema_script = f.read()
            
            # 分割并执行SQL语句
            statements = self._split_sql_statements(schema_script)
            for i, statement in enumerate(statements):
                if statement.strip():
                    try:
                        cursor.execute(statement)
                        print(f"✅ 执行语句 {i+1}/{len(statements)}")
                    except sqlite3.Error as e:
                        print(f"⚠️ 语句 {i+1} 执行警告: {e}")
            
            conn.commit()
            conn.close()
            
            print(f"✅ SQLite 数据库创建成功: {self.db_path}")
            
        except Exception as e:
            print(f"❌ SQLite 数据库创建失败: {e}")
            raise
    
    def _create_postgresql_database(self):
        """创建 PostgreSQL 数据库"""
        try:
            import psycopg2
            from psycopg2 import sql
            
            print("🐘 正在连接 PostgreSQL...")
            
            # 这里需要根据实际的连接参数进行配置
            # 示例连接参数
            conn_params = {
                'host': 'localhost',
                'port': 5432,
                'user': 'postgres',
                'password': 'password',
                'database': 'postgres'  # 先连接到默认数据库
            }
            
            # 创建数据库
            conn = psycopg2.connect(**conn_params)
            conn.autocommit = True
            cursor = conn.cursor()
            
            # 创建目标数据库
            database_name = 'medical_visual_system'
            cursor.execute(
                sql.SQL("DROP DATABASE IF EXISTS {}").format(
                    sql.Identifier(database_name)
                )
            )
            cursor.execute(
                sql.SQL("CREATE DATABASE {}").format(
                    sql.Identifier(database_name)
                )
            )
            
            conn.close()
            
            # 连接到新数据库并创建表结构
            conn_params['database'] = database_name
            conn = psycopg2.connect(**conn_params)
            cursor = conn.cursor()
            
            # 读取并执行 schema.sql (需要PostgreSQL版本)
            print("📋 正在执行数据库架构脚本...")
            with open(self.schema_file, 'r', encoding='utf-8') as f:
                schema_script = f.read()
            
            # 转换为PostgreSQL兼容的语法
            schema_script = self._convert_to_postgresql(schema_script)
            
            cursor.execute(schema_script)
            conn.commit()
            conn.close()
            
            print(f"✅ PostgreSQL 数据库创建成功: {database_name}")
            
        except ImportError:
            print("❌ 请安装 psycopg2: pip install psycopg2-binary")
            raise
        except Exception as e:
            print(f"❌ PostgreSQL 数据库创建失败: {e}")
            raise
    
    def _split_sql_statements(self, script: str) -> list:
        """分割SQL脚本为独立的语句"""
        # 简单的SQL语句分割（可以优化为更复杂的解析器）
        statements = []
        current_statement = ""
        in_comment = False
        
        for line in script.split('\n'):
            line = line.strip()
            
            # 跳过注释行
            if line.startswith('--') or line.startswith('/*') or line.startswith('*'):
                continue
            
            # 跳过空行
            if not line:
                continue
            
            current_statement += line + " "
            
            # 检查语句结束
            if line.endswith(';'):
                statements.append(current_statement.strip())
                current_statement = ""
        
        return [stmt for stmt in statements if stmt and not stmt.startswith('--')]
    
    def _convert_to_postgresql(self, script: str) -> str:
        """将SQLite脚本转换为PostgreSQL兼容格式"""
        # 基本的语法转换
        script = script.replace('AUTOINCREMENT', 'SERIAL')
        script = script.replace('INTEGER PRIMARY KEY AUTOINCREMENT', 'SERIAL PRIMARY KEY')
        script = script.replace('CURRENT_TIMESTAMP', 'CURRENT_TIMESTAMP')
        script = script.replace('BOOLEAN DEFAULT TRUE', 'BOOLEAN DEFAULT TRUE')
        script = script.replace('BOOLEAN DEFAULT FALSE', 'BOOLEAN DEFAULT FALSE')
        
        # 移除SQLite特有的触发器
        lines = script.split('\n')
        filtered_lines = []
        skip_trigger = False
        
        for line in lines:
            if 'CREATE TRIGGER' in line:
                skip_trigger = True
                continue
            elif skip_trigger and line.strip().endswith(';'):
                skip_trigger = False
                continue
            elif not skip_trigger:
                filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)
    
    def verify_database(self):
        """验证数据库创建是否成功"""
        print("🔍 正在验证数据库...")
        
        try:
            if self.db_type == "sqlite":
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 检查表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                    ORDER BY name
                """)
                tables = cursor.fetchall()
                
                print(f"📋 发现 {len(tables)} 个表:")
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"   - {table_name}: {count} 条记录")
                
                conn.close()
                
            print("✅ 数据库验证成功!")
            
        except Exception as e:
            print(f"❌ 数据库验证失败: {e}")
            raise
    
    def show_connection_info(self):
        """显示数据库连接信息"""
        print("\n" + "="*60)
        print("📊 数据库连接信息")
        print("="*60)
        print(f"数据库类型: {self.db_type}")
        print(f"数据库路径: {self.db_path}")
        
        if self.db_type == "sqlite":
            abs_path = os.path.abspath(self.db_path)
            print(f"绝对路径: {abs_path}")
            print(f"文件大小: {os.path.getsize(abs_path) / 1024:.1f} KB")
        
        print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='医疗检查项目可视化系统 - 数据库初始化')
    parser.add_argument('--db-type', choices=['sqlite', 'postgresql'], 
                       default='sqlite', help='数据库类型')
    parser.add_argument('--db-path', help='数据库路径')
    parser.add_argument('--verify-only', action='store_true', 
                       help='仅验证现有数据库')
    parser.add_argument('--force', action='store_true', 
                       help='强制重新创建数据库（会删除现有数据）')
    
    args = parser.parse_args()
    
    print("🏥 医疗检查项目可视化系统 - 数据库初始化工具")
    print("="*60)
    
    # 创建初始化器
    initializer = DatabaseInitializer(
        db_path=args.db_path,
        db_type=args.db_type
    )
    
    try:
        if args.verify_only:
            # 仅验证数据库
            initializer.verify_database()
        else:
            # 创建数据库
            if not args.force and os.path.exists(initializer.db_path):
                response = input(f"⚠️ 数据库文件已存在: {initializer.db_path}\n是否继续？这将备份原文件。(y/N): ")
                if response.lower() != 'y':
                    print("❌ 操作已取消")
                    return
            
            # 执行初始化
            initializer.create_database()
            initializer.verify_database()
            initializer.show_connection_info()
            
            print("\n🎉 数据库初始化完成!")
            print("💡 提示:")
            print("   - 可以使用 --verify-only 参数验证数据库")
            print("   - 可以使用不同的 --db-path 创建多个数据库实例")
            
    except KeyboardInterrupt:
        print("\n❌ 操作被用户取消")
    except Exception as e:
        print(f"\n❌ 初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 