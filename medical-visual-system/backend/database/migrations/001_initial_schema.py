"""
医疗检查项目可视化系统 - 初始数据库迁移
版本: 001_initial_schema
创建时间: 2025-01-11
说明: 创建所有核心数据表和基础数据
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table, column
from sqlalchemy import Integer, String, Text, Boolean, DateTime
from datetime import datetime

# 修订版本信息
revision = '001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """创建所有数据表"""
    
    print("🔄 开始执行数据库迁移: 001_initial_schema")
    
    # ================================================================
    # 1. 创建基础数据表
    # ================================================================
    
    # 1.1 部位结构表
    print("📋 创建 body_parts 表...")
    op.create_table(
        'body_parts',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('level1_code', sa.String(1), nullable=False),
        sa.Column('level1_name', sa.String(50), nullable=False),
        sa.Column('level2_code', sa.String(2), nullable=False),
        sa.Column('level2_name', sa.String(50), nullable=False),
        sa.Column('level3_code', sa.String(2), nullable=False),
        sa.Column('level3_name', sa.String(100), nullable=False),
        sa.Column('part_code', sa.String(5), nullable=False, unique=True),
        sa.Column('part_code_text', sa.String(10)),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow),
        
        # 约束
        sa.UniqueConstraint('level1_code', 'level2_code', 'level3_code'),
    )
    
    # 创建索引
    op.create_index('idx_part_code', 'body_parts', ['part_code'])
    op.create_index('idx_level1', 'body_parts', ['level1_code'])
    op.create_index('idx_level2', 'body_parts', ['level2_code'])
    op.create_index('idx_level3', 'body_parts', ['level3_code'])
    
    # 1.2 DR项目清单表
    print("📋 创建 dr_projects 表...")
    op.create_table(
        'dr_projects',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('level1_code', sa.String(1), nullable=False),
        sa.Column('level1_name', sa.String(50), nullable=False),
        sa.Column('level2_code', sa.String(2), nullable=False),
        sa.Column('level2_name', sa.String(50), nullable=False),
        sa.Column('level3_code', sa.String(2), nullable=False),
        sa.Column('level3_name', sa.String(100), nullable=False),
        sa.Column('part_code', sa.String(5), nullable=False),
        
        # DR特有字段
        sa.Column('position', sa.String(100)),
        sa.Column('position_code', sa.String(2)),
        sa.Column('body_position', sa.String(50)),
        sa.Column('body_position_code', sa.String(2)),
        sa.Column('direction', sa.String(50)),
        sa.Column('direction_code', sa.String(2)),
        
        # 分类字段
        sa.Column('population', sa.String(20)),
        sa.Column('disease', sa.String(30)),
        sa.Column('emergency_type', sa.String(10)),
        
        # 元数据
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow),
    )
    
    # 创建索引
    op.create_index('idx_dr_part_code', 'dr_projects', ['part_code'])
    op.create_index('idx_dr_position', 'dr_projects', ['position'])
    op.create_index('idx_dr_population', 'dr_projects', ['population'])
    op.create_index('idx_dr_disease', 'dr_projects', ['disease'])
    
    # 1.3 医保编码表
    print("📋 创建 insurance_codes 表...")
    op.create_table(
        'insurance_codes',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('sequence_no', sa.String(10)),
        sa.Column('category', sa.String(100)),
        sa.Column('insurance_project_code', sa.String(30)),
        sa.Column('insurance_project_name', sa.Text()),
        sa.Column('mutual_recognition_name', sa.Text()),
        sa.Column('insurance_mapping_code', sa.String(6), nullable=False),
        sa.Column('insurance_extension_code', sa.String(2)),
        
        # 元数据
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow),
        
        # 约束
        sa.UniqueConstraint('insurance_mapping_code', 'insurance_extension_code'),
    )
    
    # 创建索引
    op.create_index('idx_insurance_mapping', 'insurance_codes', ['insurance_mapping_code'])
    op.create_index('idx_insurance_project_code', 'insurance_codes', ['insurance_project_code'])
    
    # 1.4 MG项目表
    print("📋 创建 mg_projects 表...")
    op.create_table(
        'mg_projects',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('level1_code', sa.String(1), nullable=False),
        sa.Column('level1_name', sa.String(50), nullable=False),
        sa.Column('level2_code', sa.String(2), nullable=False),
        sa.Column('level2_name', sa.String(50), nullable=False),
        sa.Column('level3_code', sa.String(2), nullable=False),
        sa.Column('level3_name', sa.String(100), nullable=False),
        sa.Column('part_code', sa.String(5), nullable=False),
        
        # MG特有字段
        sa.Column('position_tech', sa.String(100)),
        sa.Column('insurance_mapping_code', sa.String(6)),
        sa.Column('insurance_extension_code', sa.String(2)),
        sa.Column('project_name', sa.Text()),
        sa.Column('project_code', sa.String(16)),
        sa.Column('population_code', sa.String(1)),
        sa.Column('disease_code', sa.String(1)),
        sa.Column('emergency_code', sa.String(1)),
        sa.Column('insurance_project_code', sa.String(30)),
        
        # 元数据
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow),
    )
    
    # 创建索引
    op.create_index('idx_mg_part_code', 'mg_projects', ['part_code'])
    op.create_index('idx_mg_project_code', 'mg_projects', ['project_code'])
    op.create_index('idx_mg_insurance_mapping', 'mg_projects', ['insurance_mapping_code'])
    
    # ================================================================
    # 2. 创建数据字典表
    # ================================================================
    
    # 2.1 模态字典表
    print("📋 创建 modality_dict 表...")
    op.create_table(
        'modality_dict',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('mapping_code_prefix', sa.String(2), nullable=False, unique=True),
        sa.Column('modality_name', sa.String(10), nullable=False),
        sa.Column('modality_desc', sa.String(100)),
        sa.Column('default_mapping', sa.Boolean(), default=False),
        sa.Column('sort_order', sa.Integer(), default=0),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow),
        
        # 约束
        sa.UniqueConstraint('modality_name', 'mapping_code_prefix'),
    )
    
    # 创建索引
    op.create_index('idx_modality_name', 'modality_dict', ['modality_name'])
    
    # 2.2 人群字典表
    print("📋 创建 population_dict 表...")
    op.create_table(
        'population_dict',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('population_code', sa.String(1), nullable=False, unique=True),
        sa.Column('population_name', sa.String(30), nullable=False),
        sa.Column('description', sa.Text()),
        sa.Column('sort_order', sa.Integer(), default=0),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow),
    )
    
    # 创建索引
    op.create_index('idx_population_name', 'population_dict', ['population_name'])
    
    # 2.3 疾病字典表
    print("📋 创建 disease_dict 表...")
    op.create_table(
        'disease_dict',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('disease_code', sa.String(1), nullable=False, unique=True),
        sa.Column('disease_name', sa.String(30), nullable=False),
        sa.Column('description', sa.Text()),
        sa.Column('sort_order', sa.Integer(), default=0),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow),
    )
    
    # 创建索引
    op.create_index('idx_disease_name', 'disease_dict', ['disease_name'])
    
    # 2.4 扫描方式映射表
    print("📋 创建 scan_mapping 表...")
    op.create_table(
        'scan_mapping',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('scan_method', sa.String(150), nullable=False),
        sa.Column('scan_method_short', sa.String(50)),
        sa.Column('insurance_mapping_code', sa.String(6), nullable=False),
        sa.Column('insurance_extension_code', sa.String(2), nullable=False),
        sa.Column('modality', sa.String(10), nullable=False),
        sa.Column('applicable_parts', sa.Text()),
        sa.Column('contrast_agent', sa.Boolean(), default=False),
        sa.Column('sort_order', sa.Integer(), default=0),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow),
        
        # 约束
        sa.UniqueConstraint('scan_method', 'modality'),
    )
    
    # 创建索引
    op.create_index('idx_scan_modality', 'scan_mapping', ['modality'])
    op.create_index('idx_scan_mapping_code', 'scan_mapping', ['insurance_mapping_code', 'insurance_extension_code'])
    
    # ================================================================
    # 3. 创建项目生成记录表
    # ================================================================
    
    # 3.1 生成项目表
    print("📋 创建 generated_projects 表...")
    op.create_table(
        'generated_projects',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        
        # 基础信息
        sa.Column('modality', sa.String(10), nullable=False),
        sa.Column('level1_code', sa.String(1), nullable=False),
        sa.Column('level1_name', sa.String(50), nullable=False),
        sa.Column('level2_code', sa.String(2), nullable=False),
        sa.Column('level2_name', sa.String(50), nullable=False),
        sa.Column('level3_code', sa.String(2), nullable=False),
        sa.Column('level3_name', sa.String(100), nullable=False),
        sa.Column('part_code', sa.String(5), nullable=False),
        
        # 特定字段
        sa.Column('scan_method', sa.String(150)),
        sa.Column('position', sa.String(100)),
        sa.Column('position_code', sa.String(2)),
        sa.Column('body_position', sa.String(50)),
        sa.Column('body_position_code', sa.String(2)),
        sa.Column('direction', sa.String(50)),
        sa.Column('direction_code', sa.String(2)),
        
        # 编码信息
        sa.Column('insurance_mapping_code', sa.String(6), nullable=False),
        sa.Column('insurance_extension_code', sa.String(2)),
        sa.Column('project_name', sa.Text(), nullable=False),
        sa.Column('project_code', sa.String(16), nullable=False, unique=True),
        sa.Column('population_code', sa.String(1), default='0'),
        sa.Column('disease_code', sa.String(1), default='0'),
        sa.Column('emergency_code', sa.String(1), default='0'),
        
        # 生成信息
        sa.Column('generation_batch_id', sa.String(50), nullable=False),
        sa.Column('source_table', sa.String(50)),
        sa.Column('generation_rules', sa.Text()),
        
        # 元数据
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
    )
    
    # 创建索引
    op.create_index('idx_gen_modality', 'generated_projects', ['modality'])
    op.create_index('idx_gen_batch', 'generated_projects', ['generation_batch_id'])
    op.create_index('idx_gen_insurance_mapping', 'generated_projects', ['insurance_mapping_code'])
    op.create_index('idx_gen_part_code', 'generated_projects', ['part_code'])
    op.create_index('idx_gen_created', 'generated_projects', ['created_at'])
    
    # 3.2 生成批次记录表
    print("📋 创建 generation_batches 表...")
    op.create_table(
        'generation_batches',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('batch_id', sa.String(50), nullable=False, unique=True),
        sa.Column('batch_name', sa.String(150)),
        sa.Column('batch_desc', sa.Text()),
        
        # 统计信息
        sa.Column('total_projects', sa.Integer(), default=0),
        sa.Column('ct_count', sa.Integer(), default=0),
        sa.Column('mr_count', sa.Integer(), default=0),
        sa.Column('dr_count', sa.Integer(), default=0),
        sa.Column('mg_count', sa.Integer(), default=0),
        sa.Column('insurance_count', sa.Integer(), default=0),
        
        # 状态信息
        sa.Column('status', sa.String(20), default='pending'),
        sa.Column('progress_percent', sa.Integer(), default=0),
        sa.Column('error_count', sa.Integer(), default=0),
        sa.Column('error_details', sa.Text()),
        
        # 配置信息
        sa.Column('generation_config', sa.Text()),
        sa.Column('data_source_info', sa.Text()),
        
        # 时间信息
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('started_at', sa.DateTime()),
        sa.Column('completed_at', sa.DateTime()),
    )
    
    # 创建索引
    op.create_index('idx_batch_status', 'generation_batches', ['status'])
    op.create_index('idx_batch_created', 'generation_batches', ['created_at'])
    op.create_index('idx_batch_completed', 'generation_batches', ['completed_at'])
    
    print("✅ 数据库表创建完成!")


def insert_basic_data():
    """插入基础数据"""
    
    print("🔄 开始插入基础数据...")
    
    # 获取表引用
    modality_dict = table('modality_dict',
        column('mapping_code_prefix', String),
        column('modality_name', String),
        column('modality_desc', String),
        column('default_mapping', Boolean),
        column('sort_order', Integer)
    )
    
    population_dict = table('population_dict',
        column('population_code', String),
        column('population_name', String),
        column('description', String),
        column('sort_order', Integer)
    )
    
    disease_dict = table('disease_dict',
        column('disease_code', String),
        column('disease_name', String),
        column('description', String),
        column('sort_order', Integer)
    )
    
    # 插入模态字典数据
    print("📝 插入模态字典数据...")
    op.bulk_insert(modality_dict, [
        {'mapping_code_prefix': '2a', 'modality_name': 'CT', 'modality_desc': 'X线计算机体层摄影', 'default_mapping': True, 'sort_order': 1},
        {'mapping_code_prefix': '2b', 'modality_name': 'CT', 'modality_desc': 'X线计算机体层摄影(特殊)', 'default_mapping': False, 'sort_order': 2},
        {'mapping_code_prefix': '3a', 'modality_name': 'MR', 'modality_desc': '磁共振成像', 'default_mapping': True, 'sort_order': 3},
        {'mapping_code_prefix': '3b', 'modality_name': 'MR', 'modality_desc': '磁共振成像(增强)', 'default_mapping': False, 'sort_order': 4},
        {'mapping_code_prefix': '3c', 'modality_name': 'MR', 'modality_desc': '磁共振成像(特殊)', 'default_mapping': False, 'sort_order': 5},
        {'mapping_code_prefix': '11', 'modality_name': 'DR', 'modality_desc': 'X线数字化摄影', 'default_mapping': True, 'sort_order': 6},
        {'mapping_code_prefix': '13', 'modality_name': 'MG', 'modality_desc': '乳腺钼靶摄影', 'default_mapping': True, 'sort_order': 7},
    ])
    
    # 插入人群字典数据
    print("📝 插入人群字典数据...")
    op.bulk_insert(population_dict, [
        {'population_code': '0', 'population_name': '一般人群', 'description': '普通患者群体', 'sort_order': 0},
        {'population_code': '1', 'population_name': '胎儿', 'description': '胎儿检查专用', 'sort_order': 1},
        {'population_code': '2', 'population_name': '新生儿', 'description': '新生儿检查专用', 'sort_order': 2},
        {'population_code': '3', 'population_name': '儿童', 'description': '儿童检查专用', 'sort_order': 3},
        {'population_code': '4', 'population_name': '孕妇', 'description': '孕妇检查专用', 'sort_order': 4},
    ])
    
    # 插入疾病字典数据
    print("📝 插入疾病字典数据...")
    op.bulk_insert(disease_dict, [
        {'disease_code': '0', 'disease_name': '一般疾病', 'description': '常规检查项目', 'sort_order': 0},
        {'disease_code': '1', 'disease_name': '外伤', 'description': '外伤相关检查', 'sort_order': 1},
        {'disease_code': '2', 'disease_name': '认知', 'description': '认知功能检查', 'sort_order': 2},
        {'disease_code': '3', 'disease_name': '癫痫', 'description': '癫痫相关检查', 'sort_order': 3},
        {'disease_code': '4', 'disease_name': 'PICC', 'description': 'PICC相关检查', 'sort_order': 4},
        {'disease_code': '5', 'disease_name': '卒中', 'description': '脑卒中相关检查', 'sort_order': 5},
        {'disease_code': '6', 'disease_name': '胸痛', 'description': '胸痛相关检查', 'sort_order': 6},
    ])
    
    print("✅ 基础数据插入完成!")


def downgrade():
    """删除所有数据表（回滚操作）"""
    
    print("🔄 开始回滚数据库迁移...")
    
    # 按相反顺序删除表（考虑外键依赖）
    tables_to_drop = [
        'generation_batches',
        'generated_projects', 
        'scan_mapping',
        'disease_dict',
        'population_dict',
        'modality_dict',
        'mg_projects',
        'insurance_codes',
        'dr_projects',
        'body_parts'
    ]
    
    for table_name in tables_to_drop:
        print(f"🗑️ 删除表: {table_name}")
        op.drop_table(table_name)
    
    print("✅ 数据库回滚完成!")


# 在迁移执行时自动插入基础数据
def upgrade_with_data():
    """执行迁移并插入基础数据"""
    upgrade()
    insert_basic_data() 