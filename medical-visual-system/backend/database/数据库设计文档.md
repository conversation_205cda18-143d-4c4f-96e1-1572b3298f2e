# 医疗检查项目可视化数据处理系统 - 数据库设计文档

## 📋 文档信息

| 项目 | 内容 |
|------|------|
| **系统名称** | 医疗检查项目可视化数据处理系统 |
| **数据库版本** | v1.0 |
| **文档版本** | v1.0 |
| **创建时间** | 2025-01-11 |
| **数据库类型** | SQLite (开发) / PostgreSQL (生产) |
| **总表数量** | 10个核心表 |

---

## 🎯 设计目标

### 核心目标
1. **数据完整性**：确保医疗项目数据的准确性和完整性
2. **高性能查询**：支持大数据量的快速查询和统计
3. **业务规则**：严格遵循医疗编码规范和业务逻辑
4. **扩展性**：支持新模态和新规则的快速添加
5. **可追溯性**：完整记录项目生成历史和数据变更

### 技术特性
- ✅ **标准化编码**：16位标准医疗项目编码
- ✅ **多模态支持**：CT、MR、DR、MG、医保项目
- ✅ **数据字典管理**：灵活的字典配置系统
- ✅ **批次管理**：完整的生成批次跟踪
- ✅ **索引优化**：针对查询场景的索引设计

---

## 📊 数据库架构概览

### 表分类结构
```
医疗检查项目数据库 (10张表)
├── 基础数据表 (4张)
│   ├── body_parts          - 部位结构表
│   ├── dr_projects         - DR项目清单表  
│   ├── insurance_codes     - 医保编码表
│   └── mg_projects         - MG项目表
├── 数据字典表 (4张)
│   ├── modality_dict       - 模态字典表
│   ├── population_dict     - 人群字典表
│   ├── disease_dict        - 疾病字典表
│   └── scan_mapping        - 扫描方式映射表
└── 项目生成表 (2张)
    ├── generated_projects  - 生成项目表
    └── generation_batches  - 生成批次记录表
```

### 数据流关系
```mermaid
graph TB
    A[基础数据] --> D[项目生成引擎]
    B[数据字典] --> D
    D --> E[生成项目表]
    D --> F[生成批次表]
    
    subgraph "基础数据表"
        A1[body_parts]
        A2[dr_projects]
        A3[insurance_codes]
        A4[mg_projects]
    end
    
    subgraph "数据字典表"
        B1[modality_dict]
        B2[population_dict]
        B3[disease_dict]
        B4[scan_mapping]
    end
    
    A --> A1
    A --> A2
    A --> A3
    A --> A4
    
    B --> B1
    B --> B2
    B --> B3
    B --> B4
```

---

## 📋 详细表结构设计

### 1. 基础数据表

#### 1.1 部位结构表 (body_parts)

**表用途**：存储三级医疗部位分类结构，包含各模态适用性标记，是项目生成的核心基础数据源

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| `id` | INTEGER | PRIMARY KEY | 自增主键 | 1 |
| `level1_code` | VARCHAR(1) | NOT NULL | 一级编码 | "1" |
| `level1_name` | VARCHAR(50) | NOT NULL | 一级部位名称 | "头部" |
| `level2_code` | VARCHAR(2) | NOT NULL | 二级编码 | "01" |
| `level2_name` | VARCHAR(50) | NOT NULL | 二级部位名称 | "颅脑" |
| `level3_code` | VARCHAR(2) | NOT NULL | 三级编码 | "01" |
| `level3_name` | VARCHAR(100) | NOT NULL | 三级部位名称 | "颅脑平扫" |
| `part_code` | VARCHAR(5) | UNIQUE | 完整部位编码 (1+2+2位) | "10101" |
| `part_code_text` | VARCHAR(10) | NULLABLE | 文本格式编码（兼容） | "'10101" |
| **🏥 模态适用性字段（核心价值）** | | | | |
| `is_applicable_ct` | BOOLEAN | DEFAULT FALSE | **是否适用CT检查** | true |
| `is_applicable_mr` | BOOLEAN | DEFAULT FALSE | **是否适用MR检查** | true |
| `is_applicable_dr` | BOOLEAN | DEFAULT FALSE | **是否适用DR检查** | false |
| `is_applicable_rf` | BOOLEAN | DEFAULT FALSE | **是否适用RF透视检查** | false |
| `is_applicable_mg` | BOOLEAN | DEFAULT FALSE | **是否适用MG乳腺钼靶检查** | false |
| `is_applicable_ot` | BOOLEAN | DEFAULT FALSE | **是否适用其他模态检查** | false |
| **通用元数据字段** | | | | |
| `is_active` | BOOLEAN | DEFAULT TRUE | 是否启用 | true |
| `created_at` | TIMESTAMP | DEFAULT NOW | 创建时间 | 2025-01-11 10:00:00 |
| `updated_at` | TIMESTAMP | DEFAULT NOW | 更新时间 | 2025-01-11 10:00:00 |

**业务规则**：
- **编码规则**：部位编码 = 一级编码(1位) + 二级编码(2位) + 三级编码(2位)
- **唯一性约束**：(level1_code, level2_code, level3_code) 组合唯一
- **🎯 模态适用性核心规则**：
  - **CT适用性**：is_applicable_ct = TRUE 表示该部位可进行CT检查
  - **MR适用性**：is_applicable_mr = TRUE 表示该部位可进行MR检查
  - **DR适用性**：is_applicable_dr = TRUE 表示该部位可进行DR检查
  - **RF适用性**：is_applicable_rf = TRUE 表示该部位可进行RF透视检查
  - **MG适用性**：is_applicable_mg = TRUE 表示该部位可进行乳腺钼靶检查
  - **OT适用性**：is_applicable_ot = TRUE 表示该部位可进行其他模态检查
- **数据来源**：Excel文件"三级部位结构"sheet + 模态适用性配置
- **使用场景**：所有模态项目生成的基础数据，根据适用性字段筛选生成对应模态项目

**索引策略**：
```sql
-- 主要查询索引
CREATE INDEX idx_part_code ON body_parts(part_code);
CREATE INDEX idx_level1 ON body_parts(level1_code);
CREATE INDEX idx_level2 ON body_parts(level2_code);
CREATE INDEX idx_level3 ON body_parts(level3_code);

-- 🏥 模态适用性查询索引（核心业务索引）
CREATE INDEX idx_modality_ct ON body_parts(is_applicable_ct);
CREATE INDEX idx_modality_mr ON body_parts(is_applicable_mr);
CREATE INDEX idx_modality_dr ON body_parts(is_applicable_dr);
CREATE INDEX idx_modality_rf ON body_parts(is_applicable_rf);
CREATE INDEX idx_modality_mg ON body_parts(is_applicable_mg);
CREATE INDEX idx_modality_ot ON body_parts(is_applicable_ot);

-- 复合查询索引
CREATE INDEX idx_level_combo ON body_parts(level1_code, level2_code, level3_code);
CREATE INDEX idx_ct_active ON body_parts(is_applicable_ct, is_active);
CREATE INDEX idx_mr_active ON body_parts(is_applicable_mr, is_active);
CREATE INDEX idx_dr_active ON body_parts(is_applicable_dr, is_active);
```

---

#### 1.2 DR项目清单表 (dr_projects)

**表用途**：存储DR(数字化X线摄影)检查项目的详细信息，包含特有的摆位和体位数据

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| `id` | INTEGER | PRIMARY KEY | 自增主键 | 1 |
| `level1_code` | VARCHAR(1) | NOT NULL | 一级编码 | "1" |
| `level1_name` | VARCHAR(50) | NOT NULL | 一级部位 | "头部" |
| `level2_code` | VARCHAR(2) | NOT NULL | 二级编码 | "01" |
| `level2_name` | VARCHAR(50) | NOT NULL | 二级部位 | "颅脑" |
| `level3_code` | VARCHAR(2) | NOT NULL | 三级编码 | "01" |
| `level3_name` | VARCHAR(100) | NOT NULL | 三级部位 | "头颅" |
| `part_code` | VARCHAR(5) | NOT NULL | 部位编码 | "10101" |
| **DR特有字段** | | | | |
| `position` | VARCHAR(100) | NULLABLE | 摆位/摄影体位 | "后前正位" |
| `position_code` | VARCHAR(2) | NULLABLE | 摆位编码 | "01" |
| `body_position` | VARCHAR(50) | NULLABLE | 体位 | "站立位" |
| `body_position_code` | VARCHAR(2) | NULLABLE | 体位编码 | "01" |
| `direction` | VARCHAR(50) | NULLABLE | 方向 | "正位" |
| `direction_code` | VARCHAR(2) | NULLABLE | 方向编码 | "01" |
| **分类字段** | | | | |
| `population` | VARCHAR(20) | NULLABLE | 人群分类 | "一般人群" |
| `disease` | VARCHAR(30) | NULLABLE | 疾病分类 | "外伤" |
| `emergency_type` | VARCHAR(10) | NULLABLE | 平/急诊 | "平诊" |
| **元数据字段** | | | | |
| `is_active` | BOOLEAN | DEFAULT TRUE | 是否启用 | true |
| `created_at` | TIMESTAMP | DEFAULT NOW | 创建时间 | 2025-01-11 10:00:00 |
| `updated_at` | TIMESTAMP | DEFAULT NOW | 更新时间 | 2025-01-11 10:00:00 |

**业务规则**：
- **DR编码规则**：检查项目编码 = "110000" + 部位编码(5位) + 摆位编码(2位) + 人群码 + 疾病码 + 平急诊码
- **项目名称规则**：项目名称 = "DR" + 三级部位 + 摆位
- **数据来源**：Excel文件"DR项目名称清单"sheet
- **特殊处理**：DR项目包含详细的摆位、体位、方向信息

**索引策略**：
```sql
CREATE INDEX idx_dr_part_code ON dr_projects(part_code);
CREATE INDEX idx_dr_position ON dr_projects(position);
CREATE INDEX idx_dr_population ON dr_projects(population);
CREATE INDEX idx_dr_disease ON dr_projects(disease);
```

---

#### 1.3 医保编码表 (insurance_codes)

**表用途**：存储医保系统的标准项目编码和名称，用于医保项目生成

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| `id` | INTEGER | PRIMARY KEY | 自增主键 | 1 |
| `sequence_no` | VARCHAR(10) | NULLABLE | 序号 | "001" |
| `category` | VARCHAR(100) | NULLABLE | 归集口径 | "影像诊断" |
| `insurance_project_code` | VARCHAR(30) | NULLABLE | 医保项目码 | "220100001" |
| `insurance_project_name` | TEXT | NULLABLE | 医保项目名称 | "头颅CT平扫" |
| `mutual_recognition_name` | TEXT | NULLABLE | 互认项目名称 | "头颅CT平扫检查" |
| `insurance_mapping_code` | VARCHAR(6) | NOT NULL | 医保映射码 | "220100" |
| `insurance_extension_code` | VARCHAR(2) | NULLABLE | 医保扩展码 | "01" |
| **元数据字段** | | | | |
| `is_active` | BOOLEAN | DEFAULT TRUE | 是否启用 | true |
| `created_at` | TIMESTAMP | DEFAULT NOW | 创建时间 | 2025-01-11 10:00:00 |
| `updated_at` | TIMESTAMP | DEFAULT NOW | 更新时间 | 2025-01-11 10:00:00 |

**业务规则**：
- **医保编码规则**：使用标准医保项目编码体系
- **模态映射**：医保映射码前缀决定模态类型（2开头=CT，3开头=MR等）
- **数据来源**：Excel文件"医保编码"sheet
- **唯一性约束**：(insurance_mapping_code, insurance_extension_code) 组合唯一

**索引策略**：
```sql
CREATE INDEX idx_insurance_mapping ON insurance_codes(insurance_mapping_code);
CREATE INDEX idx_insurance_project_code ON insurance_codes(insurance_project_code);
```

---

#### 1.4 MG项目表 (mg_projects)

**表用途**：存储MG(乳腺钼靶)检查项目数据，包含特殊的乳腺检查项目信息

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| `id` | INTEGER | PRIMARY KEY | 自增主键 | 1 |
| `level1_code` | VARCHAR(1) | NOT NULL | 一级编码 | "5" |
| `level1_name` | VARCHAR(50) | NOT NULL | 一级部位 | "乳腺" |
| `level2_code` | VARCHAR(2) | NOT NULL | 二级编码 | "01" |
| `level2_name` | VARCHAR(50) | NOT NULL | 二级部位 | "乳腺" |
| `level3_code` | VARCHAR(2) | NOT NULL | 三级编码 | "01" |
| `level3_name` | VARCHAR(100) | NOT NULL | 三级部位 | "双侧乳腺" |
| `part_code` | VARCHAR(5) | NOT NULL | 部位编码 | "50101" |
| **MG特有字段** | | | | |
| `position_tech` | VARCHAR(100) | NULLABLE | 摆位/技术 | "双侧CC+MLO位" |
| `insurance_mapping_code` | VARCHAR(6) | NULLABLE | 医保映射码 | "130100" |
| `insurance_extension_code` | VARCHAR(2) | NULLABLE | 医保扩展码 | "01" |
| `project_name` | TEXT | NULLABLE | 项目名称 | "双侧乳腺钼靶摄影" |
| `project_code` | VARCHAR(16) | NULLABLE | 项目编码 | "1301005010100000" |
| `population_code` | VARCHAR(1) | NULLABLE | 人群编码 | "0" |
| `disease_code` | VARCHAR(1) | NULLABLE | 疾病编码 | "0" |
| `emergency_code` | VARCHAR(1) | NULLABLE | 平急诊编码 | "0" |
| `insurance_project_code` | VARCHAR(30) | NULLABLE | 关联医保项目码 | "130100001" |
| **元数据字段** | | | | |
| `is_active` | BOOLEAN | DEFAULT TRUE | 是否启用 | true |
| `created_at` | TIMESTAMP | DEFAULT NOW | 创建时间 | 2025-01-11 10:00:00 |
| `updated_at` | TIMESTAMP | DEFAULT NOW | 更新时间 | 2025-01-11 10:00:00 |

**业务规则**：
- **MG特殊性**：乳腺钼靶检查的专用项目表
- **数据来源**：Excel文件"MG项目表"sheet
- **项目编码**：16位标准编码格式

**索引策略**：
```sql
CREATE INDEX idx_mg_part_code ON mg_projects(part_code);
CREATE INDEX idx_mg_project_code ON mg_projects(project_code);
CREATE INDEX idx_mg_insurance_mapping ON mg_projects(insurance_mapping_code);
```

---

### 2. 数据字典表

#### 2.1 模态字典表 (modality_dict)

**表用途**：定义医学影像模态类型和医保映射码的对应关系

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| `id` | INTEGER | PRIMARY KEY | 自增主键 | 1 |
| `mapping_code_prefix` | VARCHAR(2) | UNIQUE | 映射码前缀 | "2a" |
| `modality_name` | VARCHAR(10) | NOT NULL | 模态名称 | "CT" |
| `modality_desc` | VARCHAR(100) | NULLABLE | 模态描述 | "X线计算机体层摄影" |
| `default_mapping` | BOOLEAN | DEFAULT FALSE | 是否为默认映射 | true |
| `sort_order` | INTEGER | DEFAULT 0 | 排序顺序 | 1 |
| `is_active` | BOOLEAN | DEFAULT TRUE | 是否启用 | true |
| `created_at` | TIMESTAMP | DEFAULT NOW | 创建时间 | 2025-01-11 10:00:00 |
| `updated_at` | TIMESTAMP | DEFAULT NOW | 更新时间 | 2025-01-11 10:00:00 |

**预置数据**：
| 映射码前缀 | 模态名称 | 描述 | 默认映射 |
|------------|----------|------|----------|
| 2a | CT | X线计算机体层摄影 | ✅ |
| 2b | CT | X线计算机体层摄影(特殊) | ❌ |
| 3a | MR | 磁共振成像 | ✅ |
| 3b | MR | 磁共振成像(增强) | ❌ |
| 3c | MR | 磁共振成像(特殊) | ❌ |
| 11 | DR | X线数字化摄影 | ✅ |
| 13 | MG | 乳腺钼靶摄影 | ✅ |

**业务规则**：
- **双层映射机制**：精确映射码前缀 + 默认映射机制
- **扩展性**：支持新模态的快速添加
- **映射逻辑**：医保映射码前缀决定项目模态类型

---

#### 2.2 人群字典表 (population_dict)

**表用途**：定义特定人群分类，用于项目编码的人群标识

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| `id` | INTEGER | PRIMARY KEY | 自增主键 | 1 |
| `population_code` | VARCHAR(1) | UNIQUE | 人群编码 | "1" |
| `population_name` | VARCHAR(30) | NOT NULL | 人群名称 | "胎儿" |
| `description` | TEXT | NULLABLE | 描述说明 | "胎儿检查专用" |
| `sort_order` | INTEGER | DEFAULT 0 | 排序顺序 | 1 |
| `is_active` | BOOLEAN | DEFAULT TRUE | 是否启用 | true |
| `created_at` | TIMESTAMP | DEFAULT NOW | 创建时间 | 2025-01-11 10:00:00 |
| `updated_at` | TIMESTAMP | DEFAULT NOW | 更新时间 | 2025-01-11 10:00:00 |

**预置数据**：
| 人群编码 | 人群名称 | 描述 |
|----------|----------|------|
| 0 | 一般人群 | 普通患者群体 |
| 1 | 胎儿 | 胎儿检查专用 |
| 2 | 新生儿 | 新生儿检查专用 |
| 3 | 儿童 | 儿童检查专用 |
| 4 | 孕妇 | 孕妇检查专用 |

---

#### 2.3 疾病字典表 (disease_dict)

**表用途**：定义特定疾病分类，用于项目编码的疾病标识

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| `id` | INTEGER | PRIMARY KEY | 自增主键 | 1 |
| `disease_code` | VARCHAR(1) | UNIQUE | 疾病编码 | "1" |
| `disease_name` | VARCHAR(30) | NOT NULL | 疾病名称 | "外伤" |
| `description` | TEXT | NULLABLE | 描述说明 | "外伤相关检查" |
| `sort_order` | INTEGER | DEFAULT 0 | 排序顺序 | 1 |
| `is_active` | BOOLEAN | DEFAULT TRUE | 是否启用 | true |
| `created_at` | TIMESTAMP | DEFAULT NOW | 创建时间 | 2025-01-11 10:00:00 |
| `updated_at` | TIMESTAMP | DEFAULT NOW | 更新时间 | 2025-01-11 10:00:00 |

**预置数据**：
| 疾病编码 | 疾病名称 | 描述 |
|----------|----------|------|
| 0 | 一般疾病 | 常规检查项目 |
| 1 | 外伤 | 外伤相关检查 |
| 2 | 认知 | 认知功能检查 |
| 3 | 癫痫 | 癫痫相关检查 |
| 4 | PICC | PICC相关检查 |
| 5 | 卒中 | 脑卒中相关检查 |
| 6 | 胸痛 | 胸痛相关检查 |

---

#### 2.4 扫描方式映射表 (scan_mapping)

**表用途**：定义CT/MR扫描方式与医保编码的映射关系，是项目生成的核心配置

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| `id` | INTEGER | PRIMARY KEY | 自增主键 | 1 |
| `scan_method` | VARCHAR(150) | NOT NULL | 扫描方式描述 | "头颅CT平扫" |
| `scan_method_short` | VARCHAR(50) | NULLABLE | 扫描方式简称 | "头颅CT" |
| `insurance_mapping_code` | VARCHAR(6) | NOT NULL | 医保映射码 | "220100" |
| `insurance_extension_code` | VARCHAR(2) | NOT NULL | 医保扩展码 | "01" |
| `modality` | VARCHAR(10) | NOT NULL | 适用模态 | "CT" |
| `applicable_parts` | TEXT | NULLABLE | 适用部位(JSON) | ["头部","颅脑"] |
| `contrast_agent` | BOOLEAN | DEFAULT FALSE | 是否使用对比剂 | false |
| `sort_order` | INTEGER | DEFAULT 0 | 排序顺序 | 1 |
| `is_active` | BOOLEAN | DEFAULT TRUE | 是否启用 | true |
| `created_at` | TIMESTAMP | DEFAULT NOW | 创建时间 | 2025-01-11 10:00:00 |
| `updated_at` | TIMESTAMP | DEFAULT NOW | 更新时间 | 2025-01-11 10:00:00 |

**业务规则**：
- **映射关系**：扫描方式 → 医保编码的一对一映射
- **模态限制**：每个扫描方式只能属于一个模态
- **部位适用性**：JSON格式存储适用的部位列表
- **对比剂标识**：区分平扫和增强扫描
- **数据来源**：Excel文件"扫描方式医保映射编码"sheet

**索引策略**：
```sql
CREATE INDEX idx_scan_modality ON scan_mapping(modality);
CREATE INDEX idx_scan_mapping_code ON scan_mapping(insurance_mapping_code, insurance_extension_code);
CREATE UNIQUE INDEX idx_scan_method_modality ON scan_mapping(scan_method, modality);
```

---

### 3. 项目生成记录表

#### 3.1 生成项目表 (generated_projects)

**表用途**：存储系统生成的所有医疗检查项目，是最终的项目数据输出

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| `id` | INTEGER | PRIMARY KEY | 自增主键 | 1 |
| **基础信息** | | | | |
| `modality` | VARCHAR(10) | NOT NULL | 模态 | "CT" |
| `level1_code` | VARCHAR(1) | NOT NULL | 一级编码 | "1" |
| `level1_name` | VARCHAR(50) | NOT NULL | 一级部位 | "头部" |
| `level2_code` | VARCHAR(2) | NOT NULL | 二级编码 | "01" |
| `level2_name` | VARCHAR(50) | NOT NULL | 二级部位 | "颅脑" |
| `level3_code` | VARCHAR(2) | NOT NULL | 三级编码 | "01" |
| `level3_name` | VARCHAR(100) | NOT NULL | 三级部位 | "颅脑" |
| `part_code` | VARCHAR(5) | NOT NULL | 部位编码 | "10101" |
| **特定字段（根据模态不同）** | | | | |
| `scan_method` | VARCHAR(150) | NULLABLE | 扫描方式(CT/MR) | "头颅CT平扫" |
| `position` | VARCHAR(100) | NULLABLE | 摆位(DR) | "后前正位" |
| `position_code` | VARCHAR(2) | NULLABLE | 摆位编码(DR) | "01" |
| `body_position` | VARCHAR(50) | NULLABLE | 体位(DR) | "站立位" |
| `body_position_code` | VARCHAR(2) | NULLABLE | 体位编码(DR) | "01" |
| `direction` | VARCHAR(50) | NULLABLE | 方向(DR) | "正位" |
| `direction_code` | VARCHAR(2) | NULLABLE | 方向编码(DR) | "01" |
| **编码信息** | | | | |
| `insurance_mapping_code` | VARCHAR(6) | NOT NULL | 医保映射码 | "220100" |
| `insurance_extension_code` | VARCHAR(2) | NULLABLE | 医保扩展码 | "01" |
| `project_name` | TEXT | NOT NULL | 生成的项目名称 | "CT头颅(平扫)" |
| `project_code` | VARCHAR(16) | UNIQUE | 生成的16位编码 | "2201001010100000" |
| `population_code` | VARCHAR(1) | DEFAULT '0' | 人群编码 | "0" |
| `disease_code` | VARCHAR(1) | DEFAULT '0' | 疾病编码 | "0" |
| `emergency_code` | VARCHAR(1) | DEFAULT '0' | 平急诊编码 | "0" |
| **生成信息** | | | | |
| `generation_batch_id` | VARCHAR(50) | NOT NULL | 生成批次ID | "BATCH_20250111_100000" |
| `source_table` | VARCHAR(50) | NULLABLE | 来源数据表 | "body_parts" |
| `generation_rules` | TEXT | NULLABLE | 生成规则(JSON) | {"rule":"standard"} |
| **元数据** | | | | |
| `created_at` | TIMESTAMP | DEFAULT NOW | 创建时间 | 2025-01-11 10:00:00 |

**业务规则**：
- **16位编码规则**：医保映射码(6位) + 部位编码(5位) + 医保扩展码(2位) + 人群码(1位) + 疾病码(1位) + 平急诊码(1位)
- **项目名称规则**：根据模态类型生成不同格式的名称
- **唯一性约束**：project_code必须全局唯一
- **批次关联**：每个项目必须关联到生成批次

**索引策略**：
```sql
CREATE UNIQUE INDEX idx_project_code ON generated_projects(project_code);
CREATE INDEX idx_gen_modality ON generated_projects(modality);
CREATE INDEX idx_gen_batch ON generated_projects(generation_batch_id);
CREATE INDEX idx_gen_insurance_mapping ON generated_projects(insurance_mapping_code);
CREATE INDEX idx_gen_part_code ON generated_projects(part_code);
CREATE INDEX idx_gen_created ON generated_projects(created_at);
```

---

#### 3.2 生成批次记录表 (generation_batches)

**表用途**：记录每次项目生成的批次信息，提供完整的生成历史追踪

| 字段名 | 数据类型 | 约束 | 说明 | 示例值 |
|--------|----------|------|------|--------|
| `id` | INTEGER | PRIMARY KEY | 自增主键 | 1 |
| `batch_id` | VARCHAR(50) | UNIQUE | 批次ID | "BATCH_20250111_100000" |
| `batch_name` | VARCHAR(150) | NULLABLE | 批次名称 | "2025年1月标准项目生成" |
| `batch_desc` | TEXT | NULLABLE | 批次描述 | "包含所有模态的标准项目生成" |
| **统计信息** | | | | |
| `total_projects` | INTEGER | DEFAULT 0 | 总项目数 | 1500 |
| `ct_count` | INTEGER | DEFAULT 0 | CT项目数 | 400 |
| `mr_count` | INTEGER | DEFAULT 0 | MR项目数 | 350 |
| `dr_count` | INTEGER | DEFAULT 0 | DR项目数 | 500 |
| `mg_count` | INTEGER | DEFAULT 0 | MG项目数 | 50 |
| `insurance_count` | INTEGER | DEFAULT 0 | 医保项目数 | 200 |
| **状态信息** | | | | |
| `status` | VARCHAR(20) | DEFAULT 'pending' | 状态 | "completed" |
| `progress_percent` | INTEGER | DEFAULT 0 | 完成百分比 | 100 |
| `error_count` | INTEGER | DEFAULT 0 | 错误数量 | 0 |
| `error_details` | TEXT | NULLABLE | 错误详情(JSON) | null |
| **配置信息** | | | | |
| `generation_config` | TEXT | NULLABLE | 生成配置(JSON) | {"modes":["CT","MR"]} |
| `data_source_info` | TEXT | NULLABLE | 数据源信息(JSON) | {"file":"data.xlsx"} |
| **时间信息** | | | | |
| `created_at` | TIMESTAMP | DEFAULT NOW | 创建时间 | 2025-01-11 10:00:00 |
| `started_at` | TIMESTAMP | NULLABLE | 开始时间 | 2025-01-11 10:00:10 |
| `completed_at` | TIMESTAMP | NULLABLE | 完成时间 | 2025-01-11 10:05:30 |

**状态枚举**：
- `pending` - 等待开始
- `processing` - 正在处理
- `completed` - 完成
- `failed` - 失败
- `cancelled` - 已取消

**业务规则**：
- **批次ID格式**：BATCH_YYYYMMDD_HHMMSS
- **统计自动更新**：项目统计数据自动计算
- **状态跟踪**：完整的生成流程状态跟踪
- **错误记录**：详细的错误信息JSON存储

**索引策略**：
```sql
CREATE INDEX idx_batch_status ON generation_batches(status);
CREATE INDEX idx_batch_created ON generation_batches(created_at);
CREATE INDEX idx_batch_completed ON generation_batches(completed_at);
```

---

## 🔍 业务视图定义

### 3.1 项目概览视图 (v_project_overview)

**用途**：提供项目生成的概览统计

```sql
CREATE VIEW v_project_overview AS
SELECT 
    modality,
    level1_name,
    level2_name,
    level3_name,
    COUNT(*) as project_count,
    MAX(created_at) as last_generated
FROM generated_projects 
GROUP BY modality, level1_name, level2_name, level3_name
ORDER BY modality, level1_name, level2_name, level3_name;
```

### 3.2 批次统计视图 (v_batch_statistics)

**用途**：按日期统计生成批次信息

```sql
CREATE VIEW v_batch_statistics AS
SELECT 
    DATE(created_at) as generation_date,
    COUNT(*) as batch_count,
    SUM(total_projects) as total_projects,
    SUM(ct_count) as total_ct,
    SUM(mr_count) as total_mr,
    SUM(dr_count) as total_dr,
    SUM(mg_count) as total_mg,
    SUM(insurance_count) as total_insurance,
    AVG(progress_percent) as avg_progress
FROM generation_batches
GROUP BY DATE(created_at)
ORDER BY generation_date DESC;
```

### 3.3 数据质量检查视图 (v_data_quality_check)

**用途**：监控各表的数据质量状况

```sql
CREATE VIEW v_data_quality_check AS
SELECT 
    'body_parts' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_records,
    COUNT(CASE WHEN level1_code IS NULL OR level1_code = '' THEN 1 END) as missing_level1,
    COUNT(CASE WHEN part_code IS NULL OR part_code = '' THEN 1 END) as missing_part_code
FROM body_parts
UNION ALL
-- 其他表的质量检查...
```

---

## ⚡ 性能优化策略

### 索引设计原则

1. **主键索引**：所有表的自增主键自动创建聚集索引
2. **唯一索引**：业务唯一字段创建唯一索引
3. **查询索引**：基于查询频率创建适当索引
4. **复合索引**：多字段组合查询创建复合索引

### 查询优化建议

1. **分页查询**：大数据量查询使用LIMIT和OFFSET
2. **条件下推**：在WHERE子句中使用索引字段
3. **避免SELECT \***：明确指定需要的字段
4. **JOIN优化**：适当使用表连接代替子查询

### 数据库配置优化

```sql
-- SQLite优化配置
PRAGMA cache_size = 10000;        -- 增加缓存
PRAGMA temp_store = memory;       -- 临时表存储在内存
PRAGMA journal_mode = WAL;        -- 使用WAL日志模式
PRAGMA synchronous = NORMAL;      -- 平衡性能和安全
```

---

## 🔒 数据安全策略

### 数据备份策略

1. **自动备份**：数据库初始化脚本自动创建备份
2. **增量备份**：定期增量备份数据变更
3. **异地备份**：重要数据的异地备份存储

### 数据完整性保护

1. **外键约束**：关键关联关系设置外键约束
2. **检查约束**：重要字段设置值域检查
3. **触发器**：自动更新时间戳和数据验证
4. **事务控制**：批量操作使用事务保护

### 访问控制

1. **数据库用户**：不同环境使用不同权限用户
2. **连接加密**：生产环境启用SSL连接
3. **审计日志**：记录重要数据操作日志

---

## 📈 扩展性设计

### 水平扩展

1. **分表策略**：大表按时间或模态分表
2. **读写分离**：主库写入，从库查询
3. **分布式部署**：多实例负载均衡

### 垂直扩展

1. **新模态支持**：通过modality_dict扩展新模态
2. **新字段添加**：使用ALTER TABLE添加新字段
3. **业务规则扩展**：通过配置表扩展业务规则

### 兼容性保证

1. **版本管理**：使用Alembic管理数据库版本
2. **向后兼容**：新版本保持对旧数据的兼容
3. **平滑升级**：数据迁移脚本支持平滑升级

---

## 📞 维护指南

### 日常维护

1. **数据清理**：定期清理过期的生成批次数据
2. **索引维护**：定期重建或优化索引
3. **统计更新**：更新数据库统计信息
4. **空间监控**：监控数据库存储空间使用

### 故障处理

1. **数据恢复**：从备份恢复数据的标准流程
2. **性能调优**：慢查询分析和优化方法
3. **数据修复**：数据不一致的检查和修复
4. **日志分析**：错误日志的分析和处理

### 监控告警

1. **性能监控**：数据库性能指标监控
2. **空间告警**：磁盘空间不足告警
3. **错误监控**：数据库错误和异常监控
4. **备份监控**：备份任务成功率监控

---

*文档最后更新：2025-01-11*  
*版本：v1.0*  
*下一步：开始FastAPI后端架构搭建* 