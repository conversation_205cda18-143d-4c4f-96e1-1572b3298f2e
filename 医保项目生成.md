
生成无部位的检查项目名称和编码

输入文件： 
/12-new/data/NEW_检查项目名称结构表 (11).xlsx'




### 原始编码结构（16位）
```
[医保映射码][部位编码][医保扩展码][人群编码][疾病编码][平急诊编码]
    6位        6位        2位       1位      1位       1位
```



sheet:医保编码
sheet:模态表
 sheet:医保编码，对 无部位的项目，使用三级部位 “其他” 来构建 项目清单


需要将这个文件的项目生成 检查项目，按标准格式

其中 医保映射码 6位中，前两位对应的 模态，


查看 sheet : 模态表， 
首位 = 1 的有不同的模态名称， 
首位 = 2，3  分别对应 CT/MR ，不论第二位是什么； 

其中部位码 采用 90200（其他）
扩展码00，三位扩展码000，

项目名称 采用sheet表中字段：  医保项目名称

1. 模态
2. 一级编码、一级部位
3. 二级编码、二级部位  
4. 三级编码、部位编码、三级部位
6. 医保映射码、医保扩展码（2位 = 00）
7. 检查项目名称、检查项目编码
8. 人群编码、疾病编码、平急诊编码 =00
