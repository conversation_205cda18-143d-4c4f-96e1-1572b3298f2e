# 编码格式修正说明

## 问题描述

用户指出原始数据中的编码格式为：
- **一级编码**: 1位字符
- **二级编码**: 2位字符  
- **三级编码**: 2位字符
- **全部为字符类型**

但之前的程序在处理时出现了格式错误，需要确保写入Excel文件的正确性。

## 修正过程

### 1. 数据读取修正
```python
# 在load_data()函数中强制指定编码列为字符串类型
dtype_parts = {
    '一级编码': str,
    '二级编码': str, 
    '三级编码': str,
    '部位编码': str
}

self.df_parts = pd.read_excel(self.excel_file_path, sheet_name='三级部位结构', dtype=dtype_parts)
```

### 2. 编码格式处理修正
```python
# 确保编码保持原始字符格式
level1_code = str(part_row['一级编码']).replace('.0', '')  # 一级编码1位
level2_code = str(part_row['二级编码']).replace('.0', '').zfill(2)  # 二级编码2位
level3_code = str(part_row['三级编码']).replace('.0', '').zfill(2)  # 三级编码2位
```

### 3. Excel导出修正
```python
# 为避免Excel自动转换为数字，在所有编码字段前加单引号强制为文本格式
text_format_columns = ['一级编码', '二级编码', '三级编码', '部位编码', '检查项目编码', '医保映射码', '医保扩展码']
for col in text_format_columns:
    if col in df_projects.columns:
        df_projects[col] = "'" + df_projects[col]
```

## 修正结果验证

### Excel文件中的显示格式
- 一级编码: `'1` (显示带单引号，实际值为 `1`)
- 二级编码: `'01`, `'02` (显示带单引号，实际值为 `01`, `02`)
- 三级编码: `'01`, `'02`, `'10` 等 (显示带单引号，实际值为2位字符)

### 编码规范验证
✅ **一级编码全部为1位**: True  
✅ **二级编码全部为2位**: True  
✅ **三级编码全部为2位**: True  
✅ **数据类型**: 全部为 `object` (字符串)

## 技术说明

### Excel自动转换问题
Excel在读取数据时会自动推断数据类型：
- `01` 会被转换为数值 `1`
- `1.0` 会被转换为数值 `1`

### 解决方案
1. **读取时强制类型**: 使用 `dtype` 参数指定字符串类型
2. **处理浮点格式**: 使用 `.replace('.0', '')` 去除可能的浮点后缀
3. **补零处理**: 使用 `.zfill(2)` 确保二级、三级编码为2位
4. **防止Excel转换**: 在导出时添加单引号前缀 `'` 强制Excel识别为文本

### 最终输出格式
生成的Excel文件中：
- 编码字段带单引号前缀（如 `'01`）
- 实际编码值符合位数要求（一级1位，二级三级2位）
- 数据类型为字符串，避免数值转换

## 使用说明

修正后的程序 `src/验证_项目生成规则.py` 现在能够：
1. 正确读取原始编码格式
2. 保持字符类型不被转换
3. 确保Excel文件中编码格式的正确性
4. 生成符合规范的16位项目编码

最终生成的Excel文件完全符合原始数据的编码格式要求。 