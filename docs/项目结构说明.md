# 医疗检查项目数据处理系统 - 项目结构说明

## 📁 项目目录结构

```
医疗检查项目处理系统/
├── src/                                    # 源代码目录
│   ├── medical_exam_processor.py          # 🆕 统一Streamlit应用（主入口）
│   ├── streamlit_simple.py               # CT/MR处理模块
│   ├── streamlit_dr.py                   # DR处理专用应用
│   ├── dr_complete_matching.py           # DR核心处理逻辑
│   ├── requirements.txt                  # Python依赖包
│   ├── README.md                         # 技术文档
│   └── 使用说明.md                       # 使用指南
├── data/                                  # 数据文件目录
│   ├── NEW_检查项目名称结构表 (9).xlsx    # CT/MR数据源（正确版本）
│   └── DR项目结构-0706.xlsx              # DR数据源
├── output/                               # 输出结果目录
│   ├── DR完整匹配结果_*.xlsx             # DR处理结果
│   └── 检查项目清单_*.xlsx               # CT/MR处理结果
├── 使用指南.md                           # 用户使用指南
├── README.md                             # 项目说明
└── 项目结构说明.md                       # 本文件
```

## 🚀 核心应用

### 1. 统一处理应用（推荐使用）
**文件**: `src/medical_exam_processor.py`  
**功能**: 整合CT/MR/DR三种模态的数据处理  
**启动命令**: 
```bash
cd src
streamlit run medical_exam_processor.py --server.port 8501
```

**主要功能**:
- 📂 数据加载与概览
- 🔬 CT/MR项目处理
- 📷 DR项目处理
- 📚 部位字典管理
- 📊 统一数据分析

### 2. CT/MR专用应用
**文件**: `src/streamlit_simple.py`  
**功能**: 专门处理CT/MR数据  
**启动命令**: 
```bash
cd src
streamlit run streamlit_simple.py --server.port 8502
```

### 3. DR专用应用
**文件**: `src/streamlit_dr.py`  
**功能**: 专门处理DR数据  
**启动命令**: 
```bash
cd src
streamlit run streamlit_dr.py --server.port 8503
```

## 📊 数据文件说明

### CT/MR数据文件
**文件**: `data/NEW_检查项目名称结构表 (9).xlsx`  
**包含Sheet**:
- 三级部位结构 (501行×35列)
- CT扫描方式 (68行×4列)
- MR扫描方式 (63行×5列)
- 体位 (20行×2列)
- DR摆位_方向 (18行×2列)

**关键字段**:
- CT/MR/DR标记列：标识部位适用的检查模态
- 扫描方式列：CT-平扫、MR-增强等具体扫描方式
- 部位编码：6位标准编码

### DR数据文件
**文件**: `data/DR项目结构-0706.xlsx`  
**包含Sheet**:
- DR数据 (360行)
- 三级部位 (128行)
- 体位 (20行)
- 方向 (18行)

## 🔧 核心处理模块

### 1. CT/MR处理模块
**文件**: `src/streamlit_simple.py`  
**类**: `SimpleMedicalProcessor`  

**主要功能**:
- 数据加载与验证
- 三级部位字典生成
- CT/MR扫描方式字典生成
- 检查项目清单生成（11列格式）
- 无部位项目生成

**输出格式**:
```
模态 → 一级编码 → 一级部位 → 二级编码 → 二级部位 → 三级编码 → 三级部位 → 
扫描方式 → 扫描方式编码 → 项目编码 → 项目名称
```

### 2. DR处理模块
**文件**: `src/dr_complete_matching.py`  

**主要功能**:
- DR数据智能匹配
- 摆位编码生成（体位+方向）
- 项目名称格式化
- 重复内容清理

**输出格式**:
```
一级编码 → 一级部位 → 二级编码 → 二级部位 → 三级编码 → 三级部位 → 部位编码 → 
项目名称 → DR检查项目编码 → 摆位 → 摆位编码 → 体位 → 体位编码 → 方向 → 方向编码
```

## 📈 处理能力统计

### CT/MR处理能力
- **适用部位**: CT 163个，MR 196个，DR 128个
- **扫描方式**: CT 7种，MR 16种
- **生成项目**: CT ~386个，MR ~609个
- **总项目数**: ~995个

### DR处理能力
- **DR数据**: 360行原始数据
- **部位覆盖**: 128个三级部位
- **摆位组合**: 体位×方向的完整组合
- **生成项目**: ~347个
- **匹配率**: 99.7%

## 🎯 使用建议

### 推荐使用方式
1. **统一应用**: 使用 `medical_exam_processor.py` 进行所有处理
2. **数据准备**: 确保数据文件在正确位置
3. **分步处理**: 按功能模块逐步处理数据
4. **结果验证**: 使用内置的数据分析功能验证结果

### 专业用户
- **CT/MR专家**: 可使用 `streamlit_simple.py` 进行深度处理
- **DR专家**: 可使用 `streamlit_dr.py` 进行专门处理
- **系统集成**: 可直接调用核心处理函数

## 🔄 数据流程

```
数据加载 → 数据验证 → 项目生成 → 格式化输出 → 质量控制 → 结果下载
```

### 详细流程
1. **数据加载**: 从Excel文件加载多个sheet
2. **数据验证**: 检查数据完整性和格式
3. **字典生成**: 生成部位和扫描方式字典
4. **项目匹配**: 根据适用性标记生成项目
5. **编码生成**: 生成标准的项目编码
6. **格式化**: 按标准格式输出结果
7. **质量控制**: 验证生成结果的准确性
8. **结果输出**: 提供Excel格式下载

## 📋 输出文件说明

### CT/MR输出文件
- **CT项目清单**: 11列格式，包含扫描方式信息
- **MR项目清单**: 11列格式，包含扫描方式信息
- **部位字典**: 包含CT/MR/DR适用性标记

### DR输出文件
- **DR项目清单**: 15列格式，包含完整摆位信息
- **匹配统计**: 详细的匹配和处理统计

### 统一输出文件
- **完整结果**: 包含所有模态的项目清单
- **综合字典**: 统一的部位字典
- **处理报告**: 详细的处理统计和质量报告

## 🛠️ 技术特性

### 核心优势
1. **模块化设计**: 各功能模块独立，易于维护
2. **智能处理**: 自动数据清理和格式化
3. **质量保证**: 多层次验证机制
4. **用户友好**: 直观的Web界面
5. **扩展性强**: 支持新功能和数据源

### 技术栈
- **前端**: Streamlit Web框架
- **后端**: Python + Pandas数据处理
- **存储**: Excel文件格式
- **部署**: 本地运行，支持多端口

## 📞 支持信息

### 快速启动
```bash
# 启动统一应用
cd src && streamlit run medical_exam_processor.py --server.port 8501

# 访问地址
http://localhost:8501
```

### 常见问题
1. **数据文件路径**: 确保数据文件在 `data/` 目录下
2. **端口冲突**: 可修改端口号避免冲突
3. **内存不足**: 大数据量时可能需要增加内存
4. **编码问题**: 确保Excel文件编码正确

### 技术支持
- 详细使用说明请参考 `使用指南.md`
- 技术文档请参考 `src/README.md`
- 问题反馈请查看处理日志
