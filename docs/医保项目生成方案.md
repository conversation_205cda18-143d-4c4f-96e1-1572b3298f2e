# 医保项目生成方案

## 📋 项目概述

本方案用于根据医保编码数据生成无部位的检查项目名称和编码，实现标准化的医保项目清单输出。

## 🎯 需求分析

### 输入数据源
- **文件**: `NEW_检查项目名称结构表 (11).xlsx`
- **主要Sheet**:
  - `医保编码` - 包含医保项目信息
  - `模态表` - 包含模态映射关系

### 输出要求
- 生成无部位的检查项目清单
- 使用"其他"部位作为统一部位编码
- 按指定列顺序输出结果

## 🔧 技术方案

### 1. 编码结构设计

#### 部位编码结构（5位）
```
[一级编码1位][二级编码2位][三级编码2位]
```
- **一级编码**: `9` (其他类别)
- **二级编码**: `02` (其他子类)  
- **三级编码**: `00` (其他具体部位)
- **完整部位编码**: `90200`

#### 检查项目编码结构（16位）
```
[医保映射码6位][部位编码5位][医保扩展码2位][人群编码1位][疾病编码1位][平急诊编码1位]
```

**示例**:
- 医保映射码: `110000` (6位)
- 部位编码: `90200` (5位)
- 医保扩展码: `00` (2位)
- 人群编码: `0` (1位)
- 疾病编码: `0` (1位)
- 平急诊编码: `0` (1位)
- **完整编码**: `1100009020000000` (16位)

### 2. 模态映射规则

根据医保映射码前两位确定模态类型：

#### 首位 = 1 (特殊设备)
- `11` → DR (X线摄影)
- `12` → IO (介入)
- `13` → MG (乳腺钼靶)
- `14` → RF (透视造影)

#### 首位 = 2 (CT)
- `2X` → CT (不论第二位数字)

#### 首位 = 3 (MR)
- `3X` → MR (不论第二位数字)

### 3. 数据处理流程

#### 步骤1: 数据加载
1. 读取医保编码sheet (117行数据)
2. 读取模态表sheet (6行映射关系)
3. 创建模态映射字典

#### 步骤2: 数据清理
1. 处理医保扩展码中的"xx"值，转换为"00"
2. 处理空值和异常数据
3. 确保数据类型正确

#### 步骤3: 项目生成
1. 遍历每个医保编码记录
2. 根据映射码确定模态类型
3. 生成16位检查项目编码
4. 构建完整项目记录

#### 步骤4: 质量验证
1. 检查编码长度（必须16位）
2. 检查重复编码
3. 检查空值字段
4. 生成质量报告

#### 步骤5: 结果输出
1. 按模态分组统计
2. 生成多sheet Excel文件
3. 确保编码字段为文本格式

## 📊 输出格式规范

### 列顺序定义
按以下顺序排列输出列：

1. **模态** - 根据医保映射码确定
2. **一级编码** - 固定为"9"
3. **一级部位** - 固定为"其他"
4. **二级编码** - 固定为"02"
5. **二级部位** - 固定为"其他"
6. **三级编码** - 固定为"00"
7. **部位编码** - 固定为"90200"
8. **三级部位** - 固定为"其他"
9. **医保项目名称** - 来源于医保编码sheet
10. **医保扩展码** - 来源于医保编码sheet，处理"xx"为"00"
11. **互认项目名称** - 来源于医保编码sheet
12. **检查项目名称** - 使用互认项目名称
13. **检查项目编码** - 16位生成编码
14. **人群编码** - 固定为"0"
15. **疾病编码** - 固定为"0"
16. **平急诊编码** - 固定为"0"

### Excel输出结构
- **医保项目清单** - 完整项目列表
- **CT项目** - CT模态项目
- **MR项目** - MR模态项目
- **DR项目** - DR模态项目
- **RF项目** - RF模态项目
- **MG项目** - MG模态项目
- **IO项目** - IO模态项目
- **统计信息** - 各模态数量统计

## 🔍 数据验证规则

### 编码验证
- 检查项目编码必须为16位
- 部位编码必须为5位："90200"
- 医保扩展码必须为2位数字

### 完整性验证
- 所有必填字段不能为空
- 检查项目名称不能为空
- 医保映射码不能为空

### 唯一性验证
- 检查项目编码不能重复
- 记录完整性检查

## 🚀 实施步骤

### 阶段1: 环境准备
1. 安装Python依赖包（pandas, openpyxl）
2. 准备数据文件
3. 创建输出目录

### 阶段2: 代码开发
1. 实现数据加载模块
2. 实现模态映射模块
3. 实现编码生成模块
4. 实现数据验证模块
5. 实现结果输出模块

### 阶段3: 测试验证
1. 单元测试各个模块
2. 集成测试完整流程
3. 数据质量验证
4. 输出格式验证

### 阶段4: 部署运行
1. 执行项目生成
2. 验证输出结果
3. 生成质量报告
4. 交付最终文件

## 📝 注意事项

### 数据处理
- 医保扩展码"xx"统一转换为"00"
- 空值字段使用默认值填充
- 确保数值字段在Excel中显示为文本格式

### 编码规范
- 严格按照16位编码格式生成
- 部位编码固定使用"90200"（其他部位）
- 人群、疾病、平急诊编码均为"0"

### 输出质量
- 确保所有编码字段在Excel中为文本格式
- 按模态正确分组输出
- 提供详细的统计信息

## 🎯 预期成果

### 数据产出
- 117个医保检查项目记录
- 按7种模态分类输出
- 16位标准编码格式

### 质量指标
- 编码格式正确率：100%
- 数据完整性：100%
- 模态识别准确率：97%+

### 文件交付
- Excel格式项目清单
- 质量验证报告
- 技术实施文档
