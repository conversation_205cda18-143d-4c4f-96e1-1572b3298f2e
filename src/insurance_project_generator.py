#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医保编码项目生成器
根据医保编码sheet生成无部位检查项目清单

功能：
1. 读取医保编码和模态表数据
2. 根据医保映射码前两位确定模态
3. 生成标准格式的检查项目清单
4. 使用"其他"部位（编码90200）作为无部位项目的部位信息
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

class InsuranceProjectGenerator:
    """医保项目生成器"""

    def __init__(self, excel_file_path):
        """初始化生成器"""
        self.excel_file_path = excel_file_path
        self.df_insurance = None
        self.df_modality = None
        self.modality_mapping = {}
        self.load_data()

    def load_data(self):
        """加载数据源"""
        print("🔄 正在加载医保数据源...")
        try:
            # 加载医保编码sheet
            self.df_insurance = pd.read_excel(self.excel_file_path, sheet_name='医保编码')
            print(f"✅ 医保编码表: {self.df_insurance.shape[0]} 行 x {self.df_insurance.shape[1]} 列")

            # 加载模态表
            self.df_modality = pd.read_excel(self.excel_file_path, sheet_name='模态表')
            print(f"✅ 模态表: {self.df_modality.shape[0]} 行 x {self.df_modality.shape[1]} 列")

            # 创建模态映射字典
            self.create_modality_mapping()

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise

    def create_modality_mapping(self):
        """创建模态映射字典"""
        print("\n📚 正在创建模态映射字典...")

        for _, row in self.df_modality.iterrows():
            first_digit = str(row['医保第一位'])
            second_digit = str(row['第二位']) if not pd.isna(row['第二位']) else '0'
            modality = row['模态']

            # 根据规则：首位=1的有不同模态名称，首位=2,3分别对应CT/MR
            if first_digit == '1':
                # 对于首位=1，使用具体的模态名称
                key = first_digit + second_digit
                self.modality_mapping[key] = modality
            elif first_digit == '2':
                # 首位=2对应CT，不论第二位是什么
                self.modality_mapping['2'] = 'CT'
            elif first_digit == '3':
                # 首位=3对应MR，不论第二位是什么
                self.modality_mapping['3'] = 'MR'

        print(f"✅ 模态映射字典创建完成: {self.modality_mapping}")

    def determine_modality(self, insurance_code):
        """根据医保映射码确定模态"""
        if pd.isna(insurance_code) or len(str(insurance_code)) < 2:
            return "未知"

        code_str = str(insurance_code)
        first_digit = code_str[0]

        # 根据规则确定模态
        if first_digit == '1':
            # 对于首位=1，需要看前两位
            if len(code_str) >= 2:
                first_two = code_str[:2]
                return self.modality_mapping.get(first_two, "未知")
            else:
                return "未知"
        elif first_digit == '2':
            return 'CT'
        elif first_digit == '3':
            return 'MR'
        else:
            return "未知"

    def generate_project_code(self, insurance_code, extension_code='00'):
        """生成检查项目编码（16位）"""
        # 格式：[医保映射码6位][部位编码6位][医保扩展码2位][人群编码1位][疾病编码1位][平急诊编码1位]
        # 部位编码使用90200（其他）+ 00补齐为6位
        part_code = "902000"  # 其他部位编码，补齐为6位
        population_code = "0"  # 人群编码
        disease_code = "0"     # 疾病编码
        emergency_code = "0"   # 平急诊编码

        # 处理医保映射码
        if pd.isna(insurance_code):
            insurance_code_str = "000000"
        else:
            insurance_code_str = str(insurance_code).replace('nan', '').strip()
            # 确保为6位，不足补0，超出截取
            insurance_code_str = insurance_code_str.ljust(6, '0')[:6]

        # 处理扩展码
        if pd.isna(extension_code) or str(extension_code).lower() in ['xx', 'nan', '']:
            extension_code_str = "00"
        else:
            extension_code_str = str(extension_code).replace('xx', '00').replace('nan', '00')
            # 确保为2位
            extension_code_str = extension_code_str.zfill(2)[:2]

        return insurance_code_str + part_code + extension_code_str + population_code + disease_code + emergency_code

    def format_extension_code(self, extension_code):
        """格式化扩展码为2位"""
        if pd.isna(extension_code) or str(extension_code).lower() in ['xx', 'nan', '']:
            return "00"

        code_str = str(extension_code).replace('xx', '00').replace('nan', '00').strip()

        # 如果是单个数字0，返回00
        if code_str == "0":
            return "00"

        # 如果是单个数字，补0
        if code_str.isdigit() and len(code_str) == 1:
            return code_str + "0"

        # 确保为2位
        return code_str.zfill(2)[:2]

    def generate_projects(self):
        """生成医保检查项目清单"""
        print(f"\n🏭 正在生成医保检查项目清单...")

        projects = []

        for idx, row in self.df_insurance.iterrows():
            # 获取基本信息
            insurance_code = row['医保映射码']
            insurance_name = row['医保项目名称'] if not pd.isna(row['医保项目名称']) else f"医保项目_{idx+1}"
            extension_code = row['医保扩展码'] if not pd.isna(row['医保扩展码']) else '00'

            # 确定模态
            modality = self.determine_modality(insurance_code)

            # 生成项目编码
            project_code = self.generate_project_code(insurance_code, extension_code)

            # 构建项目记录
            project = {
                '模态': modality,
                '一级编码': '09',  # 其他类别
                '一级部位': '其他',
                '二级编码': '02',  # 其他子类
                '二级部位': '其他',
                '三级编码': '00',  # 其他具体部位
                '部位编码': '902000',  # 6位部位编码
                '三级部位': '其他',
                '医保映射码': str(insurance_code),
                '医保扩展码': self.format_extension_code(extension_code),
                '检查项目名称': insurance_name,
                '检查项目编码': project_code,
                '人群编码': '0',
                '疾病编码': '0',
                '平急诊编码': '0'
            }

            projects.append(project)

        print(f"✅ 医保项目生成完成，共生成 {len(projects)} 个项目")
        return projects

    def validate_projects(self, projects):
        """验证项目数据质量"""
        print(f"\n🔍 正在验证项目数据质量...")

        issues = []

        # 检查重复编码
        codes = [p['检查项目编码'] for p in projects]
        duplicate_codes = [code for code in set(codes) if codes.count(code) > 1]
        if duplicate_codes:
            issues.append(f"发现重复编码: {duplicate_codes[:5]}")

        # 检查空值
        for i, project in enumerate(projects):
            for key, value in project.items():
                if pd.isna(value) or str(value).strip() == '':
                    issues.append(f"第{i+1}行 {key} 字段为空")
                    break

        # 检查编码长度
        for i, project in enumerate(projects):
            if len(project['检查项目编码']) != 17:
                issues.append(f"第{i+1}行编码长度不正确: {project['检查项目编码']} (长度:{len(project['检查项目编码'])})")

        if issues:
            print(f"❌ 发现 {len(issues)} 个质量问题:")
            for issue in issues[:10]:  # 只显示前10个
                print(f"   - {issue}")
        else:
            print("✅ 项目数据质量验证通过")

        return len(issues) == 0

    def export_results(self, projects, output_file=None):
        """导出结果到Excel"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"../output/医保检查项目清单_{timestamp}.xlsx"

        print(f"\n📤 正在导出结果到: {output_file}")

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 创建DataFrame
        df_all = pd.DataFrame(projects)

        # 按模态分组统计
        modality_stats = df_all['模态'].value_counts()

        # 创建统计信息
        stats_data = []
        for modality, count in modality_stats.items():
            stats_data.append({
                '模态': modality,
                '项目数量': count,
                '占比': f"{count/len(projects)*100:.1f}%"
            })

        df_stats = pd.DataFrame(stats_data)

        # 确保编码相关字段为文本格式，特别处理可能被Excel转换为数字的字段
        text_columns = ['一级编码', '二级编码', '三级编码', '部位编码', '医保映射码', '医保扩展码',
                       '检查项目编码', '人群编码', '疾病编码', '平急诊编码']

        for col in text_columns:
            if col in df_all.columns:
                # 对于可能被Excel误解为数字的字段，确保格式正确
                if col == '医保扩展码':
                    df_all[col] = df_all[col].apply(lambda x: f"{int(x):02d}" if str(x).isdigit() else str(x))
                else:
                    df_all[col] = df_all[col].astype(str)

        # 导出到Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 全部项目
            df_all.to_excel(writer, sheet_name='医保项目清单', index=False)

            # 按模态分别导出
            for modality in modality_stats.index:
                if modality != '未知':
                    df_modality = df_all[df_all['模态'] == modality].copy()
                    # 确保编码字段为文本格式
                    for col in text_columns:
                        if col in df_modality.columns:
                            if col == '医保扩展码':
                                df_modality[col] = df_modality[col].apply(lambda x: f"{int(x):02d}" if str(x).isdigit() else str(x))
                            else:
                                df_modality[col] = df_modality[col].astype(str)
                    sheet_name = f'{modality}项目'
                    df_modality.to_excel(writer, sheet_name=sheet_name, index=False)

            # 统计信息
            df_stats.to_excel(writer, sheet_name='统计信息', index=False)

        print(f"✅ 结果导出完成: {output_file}")
        print(f"   📋 总项目数: {len(projects)} 个")
        for modality, count in modality_stats.items():
            print(f"   📊 {modality}项目: {count} 个")

        return output_file

def main():
    """主函数"""
    print("🏥 医保编码项目生成器")
    print("=" * 50)

    try:
        # 初始化生成器
        excel_file = "../data/NEW_检查项目名称结构表 (11).xlsx"
        generator = InsuranceProjectGenerator(excel_file)

        # 生成项目
        projects = generator.generate_projects()

        # 验证数据质量
        is_valid = generator.validate_projects(projects)

        # 导出结果
        output_file = generator.export_results(projects)

        print(f"\n🎉 医保检查项目清单生成完成！")
        print(f"📄 输出文件: {output_file}")

        if not is_valid:
            print("⚠️  请检查数据质量问题")

    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
