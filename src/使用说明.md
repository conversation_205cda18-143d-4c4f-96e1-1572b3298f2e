# DR项目结构数据处理 - 使用说明

## 核心文件

### 主要处理脚本
- **`dr_complete_matching.py`** - 核心处理脚本，完成所有DR数据处理

### 验证脚本（可选）
- `verify_detailed_result.py` - 验证处理结果
- `verify_cleaning_effect.py` - 验证清理效果  
- `verify_bracket_fixes.py` - 验证括号修正
- `verify_project_names.py` - 验证项目名称格式

## 快速使用

### 1. 运行主处理脚本
```bash
python dr_complete_matching.py
```

### 2. 输入要求
确保Excel文件包含以下sheet：
- `DR` - DR项目数据
- `三级部位` - 三级部位数据（129个部位）
- `体位` - 体位编码数据
- `方向` - 方向编码数据

### 3. 输出结果
生成Excel文件包含15列：
1. 一级编码（2位）
2. 一级部位
3. 二级编码（2位）
4. 二级部位  
5. 三级编码（2位）
6. 三级部位
7. 部位编码（6位）
8. 项目名称（格式：DR+部位+"-"+摆位）
9. DR检查项目编码（10位：DR+6位部位编码+2位摆位编码）
10. 摆位
11. 摆位编码（2位：体位编码+方向编码）
12. 体位
13. 体位编码（1位）
14. 方向
15. 方向编码（1位）

## 主要功能

### 数据处理
- ✅ 智能匹配DR数据与三级部位数据（匹配率99.7%）
- ✅ 包含全部129个三级部位
- ✅ 自动生成摆位编码和DR检查项目编码

### 数据清理
- ✅ 去除摆位中的"DR"字符
- ✅ 去除部位名称中的空格
- ✅ 中文括号转换为英文括号
- ✅ 智能去除重复括号内容

### 特殊处理
- ✅ "侧"字符自动去除
- ✅ "眼框"统一为"眼眶"
- ✅ "股骨髁间窝"映射到"股骨"

## 输出示例

**项目名称格式**：
- `DR头颅-后前正位`
- `DR胸部-正位`
- `DR左手-正位`
- `DR左胫腓骨(近膝)-侧位`

**编码格式**：
- 部位编码：`010103`（6位）
- 摆位编码：`11`（2位）
- DR检查项目编码：`DR01010311`（10位）

## 注意事项

1. 输入文件路径：`data/DR项目结构-0706.xlsx`
2. 输出文件保存在：`output/` 目录
3. 所有编码都是字符串格式
4. 包含统计信息和部位覆盖情况分析
