#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的编码格式
"""

import pandas as pd
import glob
import os

def verify_codes():
    """验证编码格式"""
    
    # 查找最新的医保项目文件
    pattern = "../output/医保检查项目清单_*.xlsx"
    files = glob.glob(pattern)
    
    if not files:
        print("❌ 未找到医保项目清单文件")
        return
    
    # 获取最新文件
    latest_file = max(files, key=os.path.getctime)
    print(f"📄 验证文件: {latest_file}")
    
    try:
        # 读取数据
        df = pd.read_excel(latest_file, sheet_name='医保项目清单')
        
        print(f"\n🔍 编码格式验证:")
        
        # 检查检查项目编码长度
        code_lengths = df['检查项目编码'].astype(str).str.len()
        print(f"   检查项目编码长度分布: {code_lengths.value_counts().to_dict()}")
        
        # 检查部位编码长度
        part_code_lengths = df['部位编码'].astype(str).str.len()
        print(f"   部位编码长度分布: {part_code_lengths.value_counts().to_dict()}")
        
        # 显示编码示例
        print(f"\n📋 编码示例分析:")
        for i, row in df.head(5).iterrows():
            code = str(row['检查项目编码'])
            part_code = str(row['部位编码'])
            print(f"   第{i+1}行:")
            print(f"     检查项目编码: {code} (长度: {len(code)})")
            print(f"     部位编码: {part_code} (长度: {len(part_code)})")
            print(f"     模态: {row['模态']}")
            
            # 分解编码
            if len(code) == 16:
                insurance_part = code[:6]
                location_part = code[6:11]
                extension_part = code[11:13]
                other_part = code[13:]
                print(f"     编码分解: {insurance_part}(医保6位) + {location_part}(部位5位) + {extension_part}(扩展2位) + {other_part}(其他3位)")
            print()
        
        # 验证编码格式正确性
        correct_codes = (code_lengths == 16).sum()
        total_codes = len(df)
        print(f"📊 编码格式统计:")
        print(f"   总项目数: {total_codes}")
        print(f"   16位编码数量: {correct_codes}")
        print(f"   格式正确率: {correct_codes/total_codes*100:.1f}%")
        
        if correct_codes == total_codes:
            print("✅ 所有编码格式正确！")
        else:
            print("❌ 存在编码格式错误")
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_codes()
