# DR项目结构数据处理代码

本目录包含完整的DR项目结构数据匹配和处理代码。

## 文件说明

### 1. 主要处理脚本

#### `dr_complete_matching.py` - 核心处理脚本
**功能**：完整的DR项目结构数据匹配处理
**主要特性**：
- 从Excel文件读取DR数据、三级部位数据、体位数据、方向数据
- 智能匹配DR数据与三级部位数据
- 生成完整的129个三级部位列表
- 处理摆位编码（体位编码+方向编码）
- 生成DR检查项目编码（DR+6位部位编码+2位摆位编码）
- 清理项目名称格式（DR+部位+"-"+摆位）
- 去除摆位中的"DR"字符和部位中的空格
- 中文括号转换为英文括号
- 智能去除重复括号内容

**输出格式**：15列完整数据
- 一级编码、一级部位、二级编码、二级部位、三级编码、三级部位、部位编码
- 项目名称、DR检查项目编码、摆位、摆位编码、体位、体位编码、方向、方向编码

**使用方法**：
```bash
python dr_complete_matching.py
```

### 2. 验证脚本

#### `verify_detailed_result.py` - 详细结果验证
**功能**：验证处理结果的完整性和正确性
- 检查列顺序是否正确
- 验证编码格式和长度
- 检查摆位编码一致性
- 验证DR检查项目编码格式

#### `verify_cleaning_effect.py` - 清理效果验证
**功能**：验证数据清理效果
- 检查摆位中"DR"字符清理效果
- 验证部位名称空格清理效果
- 统计清理成功率

#### `verify_bracket_fixes.py` - 括号修正验证
**功能**：验证括号处理和重复内容去除效果
- 检查中文括号转英文括号效果
- 验证重复括号内容处理
- 特定案例检查（如左胫腓骨案例）

#### `verify_project_names.py` - 项目名称格式验证
**功能**：验证项目名称格式正确性
- 检查项目名称格式（DR+部位+"-"+摆位）
- 统计格式正确率
- 显示特殊情况处理效果

## 数据处理流程

1. **数据加载**：从Excel文件读取所有相关数据
2. **数据清理**：去除"侧"字符、处理眼眶/眼框统一、股骨髁间窝映射
3. **摆位编码处理**：从体位和方向sheet获取编码映射
4. **完整匹配**：基于三级部位表生成完整的129个部位列表
5. **项目名称生成**：按照标准格式生成项目名称
6. **编码生成**：生成DR检查项目编码
7. **格式优化**：括号转换、重复内容去除

## 主要功能特性

### 数据匹配
- 智能部位名称清理和匹配
- 支持特殊情况处理（眼眶/眼框、股骨髁间窝等）
- 高匹配率（99.7%）

### 编码生成
- 标准化编码格式
- 摆位编码 = 体位编码 + 方向编码
- DR检查项目编码 = DR + 6位部位编码 + 2位摆位编码

### 格式优化
- 统一项目名称格式：DR+部位+"-"+摆位
- 中文括号转英文括号
- 智能去除重复括号内容
- 清理多余空格和"DR"字符

### 数据完整性
- 包含所有129个三级部位
- 15列完整输出
- 保持数据一致性和完整性

## 输出结果

最终生成Excel文件包含：
- **完整匹配结果** sheet：347行完整数据
- **统计信息** sheet：处理统计数据
- **部位覆盖情况** sheet：部位覆盖状态

## 技术要求

- Python 3.x
- pandas
- openpyxl
- numpy

## 使用示例

```bash
# 运行主处理脚本
python dr_complete_matching.py

# 验证结果
python verify_detailed_result.py
python verify_cleaning_effect.py
python verify_bracket_fixes.py
python verify_project_names.py
```

## 更新历史

- 初始版本：基础匹配功能
- v2：增加摆位编码处理
- v3：优化项目名称格式
- v4：添加清理功能（去除DR、空格）
- v5：括号转换和重复内容处理

## 注意事项

1. 确保输入Excel文件包含所需的sheet：DR、三级部位、体位、方向
2. 输出文件会保存在output目录下
3. 所有验证脚本需要在主处理脚本运行后执行
4. 编码格式严格按照要求：2位一级编码、2位二级编码、2位三级编码、6位部位编码、2位摆位编码
