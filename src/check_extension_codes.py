#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查医保扩展码的实际值
"""

import pandas as pd
import glob
import os

def check_extension_codes():
    """检查医保扩展码的实际值"""
    
    # 查找最新的医保项目文件
    pattern = "../output/医保检查项目清单_*.xlsx"
    files = glob.glob(pattern)
    
    if not files:
        print("❌ 未找到医保项目清单文件")
        return
    
    # 获取最新文件
    latest_file = max(files, key=os.path.getctime)
    print(f"📄 检查文件: {latest_file}")
    
    try:
        # 读取数据
        df = pd.read_excel(latest_file, sheet_name='医保项目清单')
        
        print(f"\n🔍 医保扩展码检查:")
        print(f"   数据类型: {df['医保扩展码'].dtype}")
        print(f"   唯一值: {df['医保扩展码'].unique()}")
        
        print(f"\n📋 前10行的扩展码详情:")
        for i, row in df.head(10).iterrows():
            ext_code = row['医保扩展码']
            print(f"   第{i+1}行: '{ext_code}' (类型: {type(ext_code)}, 长度: {len(str(ext_code))})")
        
        # 检查是否有非"00"的扩展码
        non_zero_codes = df[df['医保扩展码'] != '00']['医保扩展码'].unique()
        if len(non_zero_codes) > 0:
            print(f"\n📊 非'00'的扩展码: {non_zero_codes}")
        else:
            print(f"\n📊 所有扩展码都是'00'")
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

if __name__ == "__main__":
    check_extension_codes()
