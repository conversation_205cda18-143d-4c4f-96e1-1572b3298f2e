# Excel数据文件结构要求

本规则定义了检查项目生成器所需的Excel文件结构和格式要求。

## 必需工作表

### 1. 三级部位结构 (主数据表)
**用途**: 包含所有部位层级和检查模态配置

**必需列**:
- `一级编码` (float64): 一级部位编码
- `一级部位` (object): 一级部位名称
- `二级编码` (object): 二级部位编码  
- `二级合并` (object): 二级部位名称
- `三级编码` (object): 三级部位编码
- `三级部位` (object): 三级部位名称
- `三级部位编码` (float64): 三级部位数字编码
- `部位编码` (object): 六位完整部位编码
- `CT` (object): CT适用标记 (1表示适用)
- `MR` (object): MR适用标记 (1表示适用)

**CT相关列**:
- `CT-平扫` (float64): 1.0表示适用
- `CT-增强` (float64): 1.0表示适用
- `CT-平扫+增强` (float64): 1.0表示适用
- `CT-CTA` (float64): 1.0表示适用
- `CT-CTV` (float64): 1.0表示适用
- `CT_灌注` (float64): 1.0表示适用
- `CT-延时显像(扩展)CTU` (float64): 1.0表示适用

**MR相关列**:
- `MR-平扫` (float64): 1.0表示适用
- `MR-增强` (float64): 1.0表示适用
- `MR-平扫+增强` (float64): 1.0表示适用
- `MR-平扫+ASL` (float64): 1.0表示适用
- `MR-平扫+DCE` (float64): 1.0表示适用
- `MR-MRA` (float64): 1.0表示适用
- `MR-MRV` (float64): 1.0表示适用
- `MR-CE_MRA` (float64): 1.0表示适用
- `MR-CE_MRV` (float64): 1.0表示适用
- `MR-MRA+CE_MRA` (float64): 1.0表示适用
- `MR-电影成像` (float64): 1.0表示适用
- `MR-水成像MRH` (float64): 1.0表示适用
- `MR-水成像MRM` (float64): 1.0表示适用
- `MR-水成像MRCP` (float64): 1.0表示适用
- `MR-水成像MRU` (float64): 1.0表示适用
- `MR-水成像IEHM` (float64): 1.0表示适用

### 2. CT扫描方式
**用途**: CT扫描方式编码对照表

**必需列**:
- `CT扫描分类编码` (float64): CT扫描分类编码
- `CT扫描分类名称` (object): CT扫描分类名称
- `CT扫描编码` (object): 两位扫描编码
- `CT扫描名称` (object): 扫描方式名称

**数据要求**:
- 所有编码必须是两位格式
- 名称应与主数据表中的列名匹配
- 不允许重复编码

### 3. MR扫描方式
**用途**: MR扫描方式编码对照表

**必需列**:
- `MR成像分类编码` (int64): MR成像分类编码
- `MR成像分类` (object): MR成像分类名称
- `MR成像编码` (object): 两位扫描编码
- `MR成像名称` (object): 扫描方式名称

**数据要求**:
- 所有编码必须是两位格式
- 名称应与主数据表中的列名匹配
- 不允许重复编码

### 4. DR方向 (可选)

**用途**: 检查DR检查项目的方向信息
**状态**: 当前版本未使用，预留扩展

### 5. DR体位 (可选)
**用途**: 检查DR检查的体位信息  
**状态**: 当前版本未使用，预留扩展

## 数据质量要求

### 数据完整性
- 主数据表必须有完整的部位层级信息
- 部位编码必须是6位数字格式
- CT/MR标记列不能全为空
- 扫描方式表不能有缺失的编码

### 数据一致性
- 部位编码必须唯一
- 扫描方式编码必须唯一
- CT/MR标记值必须是1或空值
- 扫描方式列值必须是1.0或NaN

### 数据格式
- 编码列必须是数字或字符串
- 名称列必须是文本
- 标记列必须是数字 (1.0表示适用)
- 不允许包含特殊字符或格式错误

## 文件规范

### 文件命名
- 建议使用描述性文件名
- 包含版本信息 (如: "检查项目名称结构表 (7).xlsx")
- 避免使用特殊字符

### 工作表命名
- 工作表名称必须完全匹配
- 区分大小写
- 不允许多余空格

### 兼容性
- 支持.xlsx格式
- 使用pandas + openpyxl读取
- 确保Excel版本兼容性

## 数据示例

### 主数据表示例
```
一级编码  一级部位  二级编码  三级编码  三级部位  部位编码  CT  MR  CT-平扫  MR-平扫
1.0     头部     01       01       颅脑    010101  1   1   1.0     1.0
```

### CT扫描方式示例
```
CT扫描分类编码  CT扫描分类名称  CT扫描编码  CT扫描名称
10.0         CT平扫        10         CT平扫
```

### MR扫描方式示例
```
MR成像分类编码  MR成像分类  MR成像编码  MR成像名称
1            MR平扫     10         MR平扫
```

## 错误处理

### 常见问题
- NaN值处理: 使用`pd.notna()`检查
- 数据类型转换: 使用`str().strip()`清理
- 编码格式: 使用`zfill(2)`确保两位格式

### 验证检查
- 加载后立即验证工作表存在性
- 检查必需列是否存在
- 验证数据类型正确性
- 检查数据完整性
