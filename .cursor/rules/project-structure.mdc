---
alwaysApply: true
---

# 医疗检查项目可视化数据处理系统 - 项目结构规范

## 🏗️ 标准目录结构

本项目采用现代全栈架构，遵循以下标准目录结构：

```
medical-visual-system/
├── backend/                 # 后端 FastAPI 应用
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI 应用入口
│   │   ├── api/            # API 路由
│   │   │   ├── __init__.py
│   │   │   ├── router.py   # 主路由
│   │   │   └── endpoints/  # 各功能端点
│   │   ├── core/           # 核心配置
│   │   │   ├── __init__.py
│   │   │   ├── config.py   # 配置管理
│   │   │   └── database.py # 数据库配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── legacy/         # 原有代码
│   ├── migrations/         # 数据库迁移
│   ├── requirements.txt    # Python 依赖
│   ├── Dockerfile
│   └── env.example
├── frontend/                # 前端 Vue.js 应用
│   ├── src/
│   │   ├── main.ts         # 应用入口
│   │   ├── App.vue         # 根组件
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面视图
│   │   ├── stores/         # 状态管理
│   │   ├── utils/          # 工具函数
│   │   ├── types/          # TypeScript 类型
│   │   └── assets/         # 静态资源
│   ├── public/             # 公共文件
│   ├── package.json
│   ├── vite.config.ts
│   ├── tsconfig.json
│   └── Dockerfile
├── shared/                  # 共享资源
│   ├── data/               # 数据文件
│   └── docs/               # 文档
├── docker-compose.yml       # 容器编排
├── .gitignore
├── README.md
├── 项目开发计划.md
└── .cursor/rules/          # Cursor 规则
```

## 🔧 开发环境要求

### 后端环境
- Python 3.11+
- FastAPI + Uvicorn
- SQLAlchemy + SQLite/PostgreSQL
- 工作目录：`backend/`
- 启动命令：`uvicorn app.main:app --reload`

### 前端环境
- Node.js 18+
- Vue.js 3 + TypeScript
- Vite 构建工具
- 工作目录：`frontend/`
- 启动命令：`npm run dev`

## 📝 文件创建规则

1. **后端文件**：必须在 `backend/` 目录下创建
2. **前端文件**：必须在 `frontend/` 目录下创建
3. **共享文件**：放在 `shared/` 目录下
4. **文档文件**：根目录或 `shared/docs/` 目录
5. **配置文件**：各自模块的根目录

## 🚨 常见错误避免

❌ **错误示例**：
- 在根目录创建 `app/main.py`
- 在根目录创建 `test_app.py`
- 混合前后端文件

✅ **正确示例**：
- 后端文件：`backend/app/main.py`
- 前端文件：`frontend/src/main.ts`
- 测试文件：`backend/test_app.py`

## 🔄 模块导入规则

### 后端导入
```python
from app.core.config import settings
from app.api.router import api_router
from app.models.user import User
```

### 前端导入
```typescript
import { createApp } from 'vue'
import router from '@/router'
import '@/assets/styles/main.css'
```

遵循此结构规范，确保项目文件组织清晰、易于维护。
