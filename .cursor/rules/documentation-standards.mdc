# 文档编写规范

## 📝 项目文档标准

### 1. Markdown 文档格式规范

#### 标题结构
```markdown
# 主标题 (H1) - 文档名称
## 二级标题 (H2) - 主要章节
### 三级标题 (H3) - 小节
#### 四级标题 (H4) - 子节点
```

#### 代码块规范
```markdown
# Python 代码块
```python
def example_function():
    """示例函数"""
    return "Hello World"
```

# TypeScript 代码块
```typescript
interface User {
  id: number;
  name: string;
}
```

# 配置文件
```json
{
  "name": "medical-visual-system",
  "version": "1.0.0"
}
```

# 终端命令
```bash
# 安装依赖
npm install

# 启动服务
npm run dev
```
```

#### 链接和引用
```markdown
# 内部链接
[项目结构](README.md#项目结构)

# 外部链接
[Vue.js 文档](https://vuejs.org/)

# 图片引用
![架构图](docs/images/architecture.png)

# 表格
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 主键 |
| name | string | 名称 |
```

### 2. API 文档规范

#### 端点文档格式
```markdown
## POST /api/projects

创建新的医疗检查项目

### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| name | string | 是 | 项目名称 |
| code | string | 是 | 项目编码 |
| modality | string | 是 | 检查模态 |

### 请求示例

```json
{
  "name": "CT头颅(平扫)",
  "code": "CT001",
  "modality": "CT"
}
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "CT头颅(平扫)",
    "code": "CT001",
    "modality": "CT",
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

### 错误响应

| 状态码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 409 | 项目编码已存在 |
| 500 | 服务器内部错误 |
```

### 3. 技术文档规范

#### 系统架构文档
```markdown
# 系统架构文档

## 概述
简要描述系统的目标、功能和架构原则

## 架构设计
### 整体架构
- 前端：Vue.js 3 + TypeScript
- 后端：FastAPI + Python
- 数据库：SQLite/PostgreSQL
- 部署：Docker + Docker Compose

### 模块设计
#### 前端模块
- 用户界面层
- 业务逻辑层
- 数据访问层

#### 后端模块
- API 层
- 业务逻辑层
- 数据访问层

## 数据流设计
```mermaid
graph TD
    A[前端界面] --> B[API 接口]
    B --> C[业务逻辑]
    C --> D[数据库]
```

## 技术选型
| 技术 | 版本 | 说明 |
|------|------|------|
| Vue.js | 3.x | 前端框架 |
| FastAPI | 0.104+ | 后端框架 |
| SQLAlchemy | 2.0+ | ORM |
```

#### 部署文档
```markdown
# 部署文档

## 环境要求
- Python 3.11+
- Node.js 18+
- Docker 20.10+
- Docker Compose 2.0+

## 部署步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd medical-visual-system
```

### 2. 配置环境变量
```bash
# 复制环境配置
cp .env.example .env

# 编辑配置文件
nano .env
```

### 3. 构建和启动
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 4. 验证部署
```bash
# 检查服务状态
docker-compose ps

# 检查日志
docker-compose logs -f
```

## 生产环境配置
### 数据库配置
### 安全配置
### 监控配置
```

### 4. 用户手册规范

#### 功能说明文档
```markdown
# 用户操作手册

## 系统概述
医疗检查项目可视化数据处理系统是一个用于...

## 功能模块

### 1. 项目管理
#### 创建项目
1. 点击"新建项目"按钮
2. 填写项目信息
   - 项目名称：必填，2-100个字符
   - 项目编码：必填，3-20个字符
   - 检查模态：必选，从下拉列表选择
3. 点击"保存"按钮

#### 编辑项目
1. 在项目列表中找到需要编辑的项目
2. 点击"编辑"按钮
3. 修改项目信息
4. 点击"保存"按钮

### 2. 数据导入
#### 支持的文件格式
- Excel 文件 (.xlsx, .xls)
- CSV 文件 (.csv)

#### 导入步骤
1. 点击"导入数据"按钮
2. 选择文件
3. 预览数据
4. 确认导入

### 3. 数据可视化
#### 图表类型
- 柱状图：显示各模态项目数量
- 饼图：显示模态分布比例
- 表格：显示详细数据

## 常见问题
### Q: 如何处理导入错误？
A: 检查文件格式和数据完整性...

### Q: 如何导出数据？
A: 点击"导出"按钮，选择导出格式...
```

### 5. 代码注释规范

#### Python 注释
```python
def generate_medical_project(
    name: str,
    modality: str,
    body_part: str,
    scan_type: Optional[str] = None
) -> MedicalProject:
    """
    生成医疗检查项目
    
    Args:
        name: 项目名称，不能为空
        modality: 检查模态，必须是有效值
        body_part: 检查部位，不能为空
        scan_type: 扫描类型，可选参数
    
    Returns:
        MedicalProject: 生成的医疗项目对象
    
    Raises:
        ValueError: 当参数无效时抛出
        
    Example:
        >>> project = generate_medical_project(
        ...     name="CT头颅(平扫)",
        ...     modality="CT",
        ...     body_part="头颅",
        ...     scan_type="平扫"
        ... )
        >>> print(project.name)
        'CT头颅(平扫)'
    """
    # 验证输入参数
    if not name or not name.strip():
        raise ValueError("项目名称不能为空")
    
    if modality not in VALID_MODALITIES:
        raise ValueError(f"无效的检查模态: {modality}")
    
    # 生成项目编码
    code = generate_project_code(modality, body_part, scan_type)
    
    # 创建项目对象
    project = MedicalProject(
        name=name,
        code=code,
        modality=modality,
        body_part=body_part,
        scan_type=scan_type
    )
    
    return project
```

#### TypeScript 注释
```typescript
/**
 * 医疗项目数据接口
 */
interface MedicalProject {
  /** 项目ID */
  id: number;
  /** 项目名称 */
  name: string;
  /** 项目编码 */
  code: string;
  /** 检查模态 */
  modality: 'CT' | 'MR' | 'DR' | 'MG' | 'US';
  /** 创建时间 */
  createdAt: string;
}

/**
 * 创建医疗项目
 * @param projectData 项目数据
 * @returns Promise<MedicalProject> 创建的项目
 */
async function createMedicalProject(
  projectData: MedicalProjectCreate
): Promise<MedicalProject> {
  try {
    const response = await api.post('/projects', projectData);
    return response.data;
  } catch (error) {
    // 处理错误
    console.error('创建项目失败:', error);
    throw error;
  }
}
```

### 6. 变更日志规范

#### CHANGELOG.md 格式
```markdown
# 变更日志

本项目的所有重要变更都会记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范。

## [未发布]

### 新增
- 新功能描述

### 变更
- 变更功能描述

### 修复
- 修复问题描述

## [1.0.0] - 2024-01-01

### 新增
- 医疗项目管理功能
- 数据导入导出功能
- 数据可视化功能
- 用户权限管理

### 变更
- 优化了界面布局
- 改进了数据处理性能

### 修复
- 修复了数据导入时的编码问题
- 修复了权限检查的bug

### 移除
- 移除了废弃的API端点
```

### 7. 文档维护规范

#### 文档更新流程
1. **代码变更时同步更新文档**
   - 新功能必须有对应文档
   - API变更必须更新API文档
   - 配置变更必须更新部署文档

2. **文档审查**
   - 技术文档需要同行审查
   - 用户文档需要用户测试
   - 文档格式需要规范检查

3. **版本控制**
   - 文档与代码同步版本
   - 重要变更需要记录
   - 保持文档历史记录

#### 文档质量检查清单
- [ ] 内容准确性
- [ ] 格式规范性
- [ ] 链接有效性
- [ ] 代码示例可执行
- [ ] 截图和图片清晰
- [ ] 语言表达清晰
- [ ] 结构层次合理

### 8. 文档工具推荐

#### 文档生成工具
- **Sphinx**: Python 项目文档生成
- **VitePress**: Vue.js 项目文档站点
- **GitBook**: 在线文档平台
- **Notion**: 协作文档工具

#### 图表工具
- **Mermaid**: 流程图、时序图
- **PlantUML**: UML 图表
- **Draw.io**: 在线绘图工具
- **Lucidchart**: 专业图表工具

遵循此文档规范，确保项目文档的质量和可维护性。
