# 医疗检查项目数据字典规范

## 🏥 数据字典标准配置

### 1. 平急诊设置标准
```python
# 统一的平急诊编码标准
EMERGENCY_CODE_CONFIG = {
    "0": "平诊",  # 标准：0 代表平诊
    "1": "急诊"   # 标准：1 代表急诊
}

# 说明：之前存在不一致问题
# - 文档规范：1:平诊 2:急诊
# - 代码实现：0:平诊 1:急诊
# 统一采用：0:平诊 1:急诊
```

### 2. 检查模态标准
```python
# 标准检查模态编码
MODALITY_CONFIG = {
    "CT": "计算机断层扫描",
    "MR": "磁共振成像",
    "DR": "数字化X线摄影",
    "MG": "乳腺X线摄影",
    "US": "超声检查",
    "ES": "内镜检查",
    "NM": "核医学检查",
    "RF": "透视检查",
    "XA": "血管造影",
    "PT": "正电子发射断层扫描"
}
```

### 3. 医保映射码配置
```python
# 医保映射码与模态对应关系
INSURANCE_MAPPING_CONFIG = {
    # 精确映射
    "1": "DR",
    "2": "CT", 
    "2a": "CT",
    "3": "MR",
    "3a": "MR",
    "3b": "MR", 
    "3c": "MR",
    "4": "US",
    "5": "ES",
    "6": "NM",
    "7": "RF",
    "8": "XA",
    "9": "PT",
    "10": "MG"
}
```

### 4. 数据格式规范
```python
# 编码格式规范
CODING_STANDARDS = {
    "一级编码": {
        "格式": "1位字符",
        "类型": "字符串",
        "示例": "'1'",
        "说明": "必须加单引号防止Excel转换"
    },
    "二级编码": {
        "格式": "2位字符",
        "类型": "字符串", 
        "示例": "'01'",
        "说明": "必须加单引号防止Excel转换"
    },
    "三级编码": {
        "格式": "2位字符",
        "类型": "字符串",
        "示例": "'01'", 
        "说明": "必须加单引号防止Excel转换"
    },
    "项目编码": {
        "CT格式": "CT + 部位编码 + 扫描编码",
        "MR格式": "MR + 部位编码 + 扫描编码",
        "DR格式": "110000 + 部位编码(5位) + 医保扩展码(5位)",
        "示例": "CT01001, MR02001, 11000001001"
    }
}
```

### 5. 字段顺序规范
```python
# 各模态字段顺序标准
FIELD_ORDER_CONFIG = {
    "CT": [
        "模态", "医保映射码", "一级编码", "二级编码", "三级编码",
        "一级部位", "二级部位", "三级部位", "扫描方式", "扫描编码",
        "项目名称", "项目编码", "平/急诊编码", "人群编码", "疾病编码"
    ],
    "MR": [
        "模态", "医保映射码", "一级编码", "二级编码", "三级编码", 
        "一级部位", "二级部位", "三级部位", "扫描方式", "扫描编码",
        "项目名称", "项目编码", "平/急诊编码", "人群编码", "疾病编码"
    ],
    "DR": [
        "模态", "医保映射码", "一级编码", "二级编码", "三级编码",
        "一级部位", "二级部位", "三级部位", "体位", "体位编码",
        "方向", "方向编码", "摆位", "摆位码", "项目名称", "项目编码",
        "平/急诊编码", "人群编码", "疾病编码", "医保扩展码", "完整编码",
        "排序权重", "备注"
    ],
    "医保项目": [
        "模态", "医保映射码", "一级编码", "二级编码", "三级编码",
        "一级部位", "二级部位", "三级部位", "医保项目名称", "医保项目编码",
        "平/急诊编码", "人群编码", "疾病编码"
    ]
}
```

### 6. 数据验证规则
```python
# 数据验证标准
VALIDATION_RULES = {
    "编码格式": {
        "项目编码": r"^[A-Z0-9]{3,20}$",
        "医保编码": r"^[0-9]{10,15}$",
        "部位编码": r"^[0-9]{1,5}$"
    },
    "必填字段": [
        "项目名称", "项目编码", "模态", "一级部位", "二级部位", "三级部位"
    ],
    "枚举值": {
        "平/急诊编码": ["0", "1"],
        "模态": ["CT", "MR", "DR", "MG", "US", "ES", "NM", "RF", "XA", "PT"]
    },
    "长度限制": {
        "项目名称": {"min": 2, "max": 100},
        "项目编码": {"min": 3, "max": 20},
        "部位名称": {"min": 1, "max": 50}
    }
}
```

### 7. 数据质量控制
```python
# 数据质量检查项
DATA_QUALITY_CHECKS = {
    "编码唯一性": {
        "检查项": ["项目编码", "医保项目编码"],
        "规则": "同一模态内编码不得重复"
    },
    "编码格式": {
        "检查项": "所有编码字段",
        "规则": "必须符合预定义格式"
    },
    "关联性验证": {
        "检查项": "医保映射码与模态",
        "规则": "映射关系必须正确"
    },
    "完整性验证": {
        "检查项": "必填字段",
        "规则": "不得为空或null"
    }
}
```

### 8. 配置文件模板
```python
# config/data_dictionary.py
from typing import Dict, List, Any

class DataDictionaryConfig:
    """数据字典配置类"""
    
    def __init__(self):
        self.emergency_codes = EMERGENCY_CODE_CONFIG
        self.modalities = MODALITY_CONFIG
        self.insurance_mappings = INSURANCE_MAPPING_CONFIG
        self.coding_standards = CODING_STANDARDS
        self.field_orders = FIELD_ORDER_CONFIG
        self.validation_rules = VALIDATION_RULES
        self.quality_checks = DATA_QUALITY_CHECKS
    
    def get_emergency_name(self, code: str) -> str:
        """根据编码获取平急诊名称"""
        return self.emergency_codes.get(code, "未知")
    
    def get_modality_name(self, code: str) -> str:
        """根据编码获取模态名称"""
        return self.modalities.get(code, "未知")
    
    def get_modality_by_insurance(self, insurance_code: str) -> str:
        """根据医保映射码获取模态"""
        return self.insurance_mappings.get(insurance_code, "未知")
    
    def validate_field(self, field_name: str, value: str) -> bool:
        """验证字段值"""
        if field_name in self.validation_rules["枚举值"]:
            return value in self.validation_rules["枚举值"][field_name]
        return True
    
    def get_field_order(self, modality: str) -> List[str]:
        """获取指定模态的字段顺序"""
        return self.field_orders.get(modality, [])
```

### 9. 使用示例
```python
# 在项目生成器中使用
from config.data_dictionary import DataDictionaryConfig

config = DataDictionaryConfig()

# 获取平急诊名称
emergency_name = config.get_emergency_name("0")  # 返回 "平诊"

# 验证模态
is_valid = config.validate_field("模态", "CT")  # 返回 True

# 获取字段顺序
field_order = config.get_field_order("CT")  # 返回CT字段顺序列表
```

### 10. 重要说明

⚠️ **数据字典一致性要求**：
1. 所有模块必须使用统一的数据字典配置
2. 编码标准必须严格遵循，不得随意修改
3. 新增字段或编码需要更新配置文件
4. 数据验证规则必须在所有环节执行

✅ **最佳实践**：
- 使用配置类统一管理数据字典
- 在数据处理前进行验证
- 记录数据质量检查结果
- 定期review和更新数据字典

🔄 **变更管理**：
- 数据字典变更需要版本控制
- 重大变更需要影响评估
- 变更后需要全面测试
- 变更记录需要文档化

遵循此数据字典规范，确保医疗检查项目数据的标准化和一致性。
