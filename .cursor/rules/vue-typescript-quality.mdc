# Vue.js + TypeScript 代码质量规范

## 🖖 Vue.js 3 组合式API规范

### 1. 组件结构规范
```vue
<template>
  <div class="medical-project-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入项目名称"
          clearable
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
        <el-button @click="handleCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElForm } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useMedicalProjectStore } from '@/stores/medicalProject'
import type { MedicalProject, MedicalProjectCreate } from '@/types/medical'

// 接口定义
interface Props {
  project?: MedicalProject
  isEdit?: boolean
}

interface Emits {
  (e: 'success'): void
  (e: 'cancel'): void
}

// Props和Emits
const props = withDefaults(defineProps<Props>(), {
  isEdit: false
})

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<MedicalProjectCreate>({
  name: '',
  code: '',
  modality: 'CT'
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入项目编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9]{3,20}$/, message: '编码格式不正确', trigger: 'blur' }
  ]
}

// 方法
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    // 提交逻辑
    emit('success')
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.medical-project-form {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}
</style>
```

### 2. TypeScript 类型定义
```typescript
// types/medical.ts
export interface MedicalProject {
  id: number
  name: string
  code: string
  modality: 'CT' | 'MR' | 'DR' | 'MG' | 'US'
  bodyPart: string
  scanType?: string
  emergencyCode: '0' | '1' // 0: 平诊, 1: 急诊
  createdAt: string
  updatedAt: string
}

export interface MedicalProjectCreate {
  name: string
  code: string
  modality: MedicalProject['modality']
  bodyPart: string
  scanType?: string
  emergencyCode?: MedicalProject['emergencyCode']
}

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  total?: number
}
```

### 3. 代码质量检查清单

#### Vue 组件
- [ ] 使用 `<script setup>` 语法
- [ ] 正确定义 Props 和 Emits 类型
- [ ] 使用 TypeScript 严格模式
- [ ] 合理使用响应式API
- [ ] 组件职责单一

#### TypeScript
- [ ] 完整的类型定义
- [ ] 避免使用 `any` 类型
- [ ] 使用接口和类型别名
- [ ] 严格的空值检查

#### 样式
- [ ] 使用 scoped 样式
- [ ] CSS 变量的使用
- [ ] 响应式设计

遵循此规范，确保Vue.js + TypeScript代码的质量和可维护性。
