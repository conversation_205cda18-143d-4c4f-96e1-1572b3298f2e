# Cursor Rules 使用指南

这个目录包含了医疗检查项目可视化数据处理系统的完整开发规范和指南。

## 📋 规则文件列表

### 🏗️ 核心规范（自动应用）
- **[project-structure.mdc](project-structure.mdc)** - 项目结构和目录规范
- **[development-workflow.mdc](development-workflow.mdc)** - 开发工作流程规范
- **[medical-data-dictionary.mdc](medical-data-dictionary.mdc)** - 医疗数据字典标准
- **[problem-solving.mdc](problem-solving.mdc)** - 问题解决和故障排除指南

### 💻 代码质量规范（文件类型触发）
- **[python-code-quality.mdc](python-code-quality.mdc)** - Python代码质量规范（适用于 *.py 文件）
- **[vue-typescript-quality.mdc](vue-typescript-quality.mdc)** - Vue.js + TypeScript 代码质量规范（适用于 *.vue, *.ts, *.tsx 文件）

### 📝 文档规范（文档文件触发）
- **[documentation-standards.mdc](documentation-standards.mdc)** - 文档编写规范（适用于 *.md, *.rst, *.txt 文件）

### 🏥 医疗项目专用规范
- **[medical-naming-conventions.mdc](medical-naming-conventions.mdc)** - 医疗项目命名规范
- **[excel-data-structure.mdc](excel-data-structure.mdc)** - Excel数据结构规范

## 🚀 如何使用这些规则

### 自动应用规则
这些规则会在每次AI对话中自动应用：
- 项目结构规范
- 开发工作流程
- 医疗数据字典
- 问题解决指南

### 文件类型触发规则
当你处理特定类型的文件时，对应的规则会自动应用：
- 编辑 Python 文件时 → Python 代码质量规范
- 编辑 Vue/TypeScript 文件时 → Vue.js + TypeScript 规范
- 编辑 Markdown 文件时 → 文档编写规范

### 手动引用规则
你可以在对话中明确提到某个规则来获得更详细的指导：
```
请按照医疗项目命名规范来生成项目名称
```

## 🎯 规则的核心价值

### 1. 项目结构标准化
- 统一的目录结构
- 清晰的文件组织
- 避免常见的结构错误

### 2. 代码质量保证
- 统一的编码风格
- 最佳实践指导
- 错误预防机制

### 3. 医疗数据标准化
- 统一的数据字典
- 标准的编码规范
- 一致的命名约定

### 4. 问题解决效率
- 常见问题的解决方案
- 系统化的故障排除流程
- 预防性措施指导

### 5. 文档质量提升
- 统一的文档格式
- 完整的文档结构
- 清晰的表达标准

## 📊 规则执行逻辑

```mermaid
graph TD
    A[开始对话] --> B{检查文件类型}
    B -->|Python文件| C[应用Python规范]
    B -->|Vue/TS文件| D[应用前端规范]
    B -->|文档文件| E[应用文档规范]
    B -->|其他| F[应用核心规范]
    
    C --> G[执行代码质量检查]
    D --> G
    E --> H[执行文档质量检查]
    F --> I[执行项目结构检查]
    
    G --> J[提供改进建议]
    H --> J
    I --> J
    
    J --> K[生成符合规范的代码/文档]
```

## 🛠️ 自定义规则

如果需要添加新的规则，请遵循以下格式：

```markdown
---
alwaysApply: true  # 或 false
globs: *.py        # 或留空
description: "规则描述"  # 或留空
---

# 规则标题

规则内容...
```

### 规则类型说明
- `alwaysApply: true` - 每次对话都应用
- `globs: *.py` - 只在处理特定文件类型时应用
- `description: "..."` - 提供规则描述，便于手动引用

## 📈 规则效果评估

### 开发效率提升
- ✅ 减少结构性错误
- ✅ 统一代码风格
- ✅ 快速问题定位
- ✅ 标准化工作流程

### 代码质量改善
- ✅ 降低bug率
- ✅ 提高代码可读性
- ✅ 增强可维护性
- ✅ 规范化命名

### 团队协作优化
- ✅ 统一开发标准
- ✅ 一致的文档格式
- ✅ 标准化流程
- ✅ 知识共享

## 🔄 规则更新维护

### 更新原则
1. 基于实际问题和需求
2. 保持向后兼容
3. 定期review和优化
4. 记录变更历史

### 更新流程
1. 识别需求或问题
2. 设计规则改进方案
3. 更新相关规则文件
4. 测试规则效果
5. 更新文档说明

## 💡 最佳实践建议

1. **遵循规则优先级**：核心规范 > 文件类型规范 > 项目专用规范
2. **保持规则同步**：代码变更时同步更新规则
3. **定期审查规则**：确保规则的有效性和时效性
4. **记录规则使用**：追踪规则的应用效果
5. **持续改进**：基于实际使用情况优化规则

---

通过遵循这些规则，我们能够确保医疗检查项目可视化数据处理系统的高质量开发和维护。

如有任何疑问或建议，请在项目讨论中提出。 