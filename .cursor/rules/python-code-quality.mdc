# Python 代码质量规范

## 🐍 FastAPI 项目代码规范

### 1. 导入规范
```python
# 标准库导入
import os
import sys
from typing import List, Dict, Optional

# 第三方库导入
from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy.orm import Session
import pandas as pd

# 本地模块导入
from app.core.config import settings
from app.core.database import get_db
from app.models.user import User
```

### 2. 数据模型规范
```python
# SQLAlchemy 模型
class MedicalProject(Base):
    __tablename__ = "medical_projects"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, comment="项目名称")
    code = Column(String(50), unique=True, nullable=False, comment="项目编码")
    modality = Column(String(10), nullable=False, comment="检查模态")
    created_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<MedicalProject(name='{self.name}', code='{self.code}')>"

# Pydantic 模型
class MedicalProjectCreate(BaseModel):
    name: str = Field(..., description="项目名称", max_length=255)
    code: str = Field(..., description="项目编码", max_length=50)
    modality: str = Field(..., description="检查模态", max_length=10)
    
    class Config:
        from_attributes = True
```

### 3. API 端点规范
```python
@router.post("/projects", response_model=MedicalProjectResponse)
async def create_project(
    project: MedicalProjectCreate,
    db: Session = Depends(get_db)
):
    """
    创建医疗检查项目
    
    Args:
        project: 项目创建数据
        db: 数据库会话
        
    Returns:
        创建的项目信息
        
    Raises:
        HTTPException: 当项目编码重复时
    """
    # 检查编码是否已存在
    existing_project = db.query(MedicalProject).filter(
        MedicalProject.code == project.code
    ).first()
    
    if existing_project:
        raise HTTPException(
            status_code=400,
            detail=f"项目编码 '{project.code}' 已存在"
        )
    
    # 创建新项目
    db_project = MedicalProject(**project.dict())
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    
    return db_project
```

### 4. 错误处理规范
```python
# 自定义异常
class MedicalProjectNotFoundError(Exception):
    """医疗项目未找到异常"""
    pass

class DuplicateProjectCodeError(Exception):
    """重复项目编码异常"""
    pass

# 异常处理器
@app.exception_handler(MedicalProjectNotFoundError)
async def medical_project_not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"error": "医疗项目未找到", "detail": str(exc)}
    )
```

### 5. 配置管理规范
```python
class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    PROJECT_NAME: str = "医疗检查项目可视化数据处理系统"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 数据库配置
    DATABASE_URL: str = Field(..., description="数据库连接URL")
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # API配置
    API_PREFIX: str = "/api"
    DOCS_URL: str = "/docs"
    OPENAPI_URL: str = "/openapi.json"
    
    # 安全配置
    SECRET_KEY: str = Field(..., description="应用密钥")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 文件配置
    UPLOAD_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = [".xlsx", ".xls", ".csv"]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
```

### 6. 数据处理规范
```python
def process_medical_data(file_path: str) -> pd.DataFrame:
    """
    处理医疗数据文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        处理后的数据框
        
    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 数据格式错误
    """
    try:
        # 读取数据
        if file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path, dtype=str)
        elif file_path.endswith('.csv'):
            df = pd.read_csv(file_path, dtype=str)
        else:
            raise ValueError(f"不支持的文件格式: {file_path}")
        
        # 数据验证
        required_columns = ['项目名称', '项目编码', '检查模态']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"缺少必要列: {missing_columns}")
        
        # 数据清洗
        df = df.dropna(subset=required_columns)
        df = df.drop_duplicates(subset=['项目编码'])
        
        # 数据转换
        df['项目编码'] = df['项目编码'].str.strip()
        df['检查模态'] = df['检查模态'].str.upper()
        
        return df
        
    except Exception as e:
        logger.error(f"数据处理失败: {e}")
        raise
```

### 7. 日志记录规范
```python
import logging
from functools import wraps

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 日志装饰器
def log_operation(operation_name: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            logger.info(f"开始执行 {operation_name}")
            try:
                result = await func(*args, **kwargs)
                logger.info(f"完成执行 {operation_name}")
                return result
            except Exception as e:
                logger.error(f"执行失败 {operation_name}: {e}")
                raise
        return wrapper
    return decorator
```

### 8. 测试规范
```python
import pytest
from fastapi.testclient import TestClient

class TestMedicalProjectAPI:
    """医疗项目API测试"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    def test_create_project_success(self, client):
        """测试创建项目成功"""
        project_data = {
            "name": "CT头颅(平扫)",
            "code": "CT001",
            "modality": "CT"
        }
        
        response = client.post("/api/projects", json=project_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == project_data["name"]
        assert data["code"] == project_data["code"]
    
    def test_create_project_duplicate_code(self, client):
        """测试创建重复编码项目"""
        project_data = {
            "name": "CT头颅(平扫)",
            "code": "CT001",
            "modality": "CT"
        }
        
        # 第一次创建
        client.post("/api/projects", json=project_data)
        
        # 第二次创建（应该失败）
        response = client.post("/api/projects", json=project_data)
        
        assert response.status_code == 400
        assert "已存在" in response.json()["detail"]
```

### 9. 代码质量检查清单

- [ ] 使用类型提示
- [ ] 编写文档字符串
- [ ] 适当的错误处理
- [ ] 日志记录完整
- [ ] 测试覆盖率 > 80%
- [ ] 代码复杂度合理
- [ ] 遵循 PEP8 规范
- [ ] 无安全漏洞

### 10. 性能优化建议

- 使用异步函数处理IO操作
- 数据库查询优化，避免N+1问题
- 适当使用缓存
- 批量处理大量数据
- 使用连接池管理数据库连接

遵循此规范，确保Python代码质量和可维护性。
