# 问题解决和故障排除规范

## 🔧 系统故障排除指南

### 1. 常见启动问题解决

#### FastAPI 应用启动失败
```bash
# 问题：ModuleNotFoundError: No module named 'app'
# 解决步骤：

# 1. 检查当前目录
pwd
# 应该在项目根目录 /Users/<USER>/Desktop/12-new

# 2. 检查目录结构
ls -la backend/
# 应该有 backend/app/ 目录

# 3. 检查 __init__.py 文件
ls -la backend/app/
# 应该有 __init__.py 文件

# 4. 正确的启动方式
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 5. 检查 Python 路径
python -c "import sys; print(sys.path)"

# 6. 检查依赖
pip list | grep fastapi
pip list | grep uvicorn
```

#### Vue.js 应用启动失败
```bash
# 问题：Cannot resolve module 或 依赖安装问题
# 解决步骤：

# 1. 检查 Node.js 版本
node --version  # 应该 >= 18.0.0
npm --version

# 2. 清理缓存
npm cache clean --force

# 3. 删除 node_modules 重新安装
rm -rf frontend/node_modules
rm -rf frontend/package-lock.json
cd frontend
npm install

# 4. 检查端口占用
lsof -i :3000
# 如果被占用，kill 进程或换端口

# 5. 启动应用
npm run dev
```

### 2. 模块导入问题解决

#### Python 模块导入错误
```python
# 问题：ImportError 或 ModuleNotFoundError
# 解决方案：

# 1. 确保目录结构正确
backend/
├── app/
│   ├── __init__.py  # 必须存在
│   ├── main.py
│   ├── api/
│   │   ├── __init__.py  # 必须存在
│   │   └── router.py
│   └── core/
│       ├── __init__.py  # 必须存在
│       └── config.py

# 2. 检查导入路径
# 正确的导入
from app.core.config import settings
from app.api.router import api_router

# 错误的导入
from core.config import settings  # 错误
from api.router import api_router  # 错误

# 3. 检查环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 4. 使用相对导入（在包内）
from .config import settings
from ..models.user import User
```

#### TypeScript 模块导入错误
```typescript
// 问题：Cannot find module 或 Type error
// 解决方案：

// 1. 检查 tsconfig.json 路径配置
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}

// 2. 检查 vite.config.ts 路径配置
import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
})

// 3. 正确的导入方式
import { createApp } from 'vue'
import router from '@/router'
import '@/assets/styles/main.css'
```

### 3. 数据库连接问题解决

#### SQLite 数据库问题
```python
# 问题：数据库连接失败或文件不存在
# 解决步骤：

# 1. 检查数据库文件路径
import os
DATABASE_PATH = "backend/medical_projects.db"
print(f"数据库文件存在: {os.path.exists(DATABASE_PATH)}")

# 2. 检查数据库URL配置
from app.core.config import settings
print(f"数据库URL: {settings.DATABASE_URL}")

# 3. 手动创建数据库
from app.core.database import engine
from app.models import Base
Base.metadata.create_all(bind=engine)

# 4. 检查数据库权限
ls -la backend/*.db
# 确保有读写权限
```

### 4. 网络请求问题解决

#### API 请求失败
```typescript
// 问题：网络请求失败或超时
// 解决方案：

// 1. 检查后端服务状态
fetch('http://localhost:8000/docs')
  .then(response => console.log('Backend Status:', response.status))
  .catch(error => console.error('Backend Error:', error))

// 2. 检查跨域配置
// backend/app/main.py
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

// 3. 检查请求配置
// frontend/src/utils/request.ts
import axios from 'axios'

const request = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 4. 检查网络连接
ping localhost
curl -I http://localhost:8000/docs
```

### 5. 文件处理问题解决

#### Excel 文件处理错误
```python
# 问题：文件读取失败或编码错误
# 解决方案：

import pandas as pd
import logging

def safe_read_excel(file_path: str) -> pd.DataFrame:
    """安全读取Excel文件"""
    try:
        # 1. 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 2. 检查文件权限
        if not os.access(file_path, os.R_OK):
            raise PermissionError(f"文件无读取权限: {file_path}")
        
        # 3. 尝试读取文件
        df = pd.read_excel(file_path, dtype=str)
        
        # 4. 检查数据有效性
        if df.empty:
            raise ValueError("文件为空")
        
        return df
        
    except Exception as e:
        logging.error(f"读取Excel文件失败: {e}")
        raise

# 使用示例
try:
    df = safe_read_excel("data/medical_projects.xlsx")
    print(f"成功读取 {len(df)} 条记录")
except Exception as e:
    print(f"处理失败: {e}")
```

### 6. 性能问题解决

#### 数据处理性能优化
```python
# 问题：大数据量处理慢
# 解决方案：

# 1. 使用向量化操作
import pandas as pd
import numpy as np

# 慢的方式
def slow_process(df):
    result = []
    for index, row in df.iterrows():
        result.append(row['col1'] + row['col2'])
    return result

# 快的方式
def fast_process(df):
    return df['col1'] + df['col2']

# 2. 使用批处理
def batch_process(df, batch_size=1000):
    for i in range(0, len(df), batch_size):
        batch = df.iloc[i:i+batch_size]
        # 处理批次数据
        yield process_batch(batch)

# 3. 使用数据库优化
from sqlalchemy import text

def optimized_query(session, limit=1000):
    return session.execute(
        text("SELECT * FROM medical_projects LIMIT :limit"),
        {"limit": limit}
    ).fetchall()
```

### 7. 调试工具和技巧

#### 日志调试
```python
# 设置详细日志
import logging

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 在关键位置添加日志
def debug_function(data):
    logger.debug(f"函数开始，输入数据: {data}")
    
    try:
        result = process_data(data)
        logger.debug(f"处理完成，结果: {result}")
        return result
    except Exception as e:
        logger.error(f"处理失败: {e}")
        raise
```

#### 性能分析
```python
# 性能分析工具
import time
import cProfile
import pstats

def profile_function(func):
    """性能分析装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        # 使用 cProfile 分析
        pr = cProfile.Profile()
        pr.enable()
        
        result = func(*args, **kwargs)
        
        pr.disable()
        end_time = time.time()
        
        print(f"函数 {func.__name__} 执行时间: {end_time - start_time:.4f}秒")
        
        # 打印性能统计
        stats = pstats.Stats(pr)
        stats.sort_stats('tottime')
        stats.print_stats(10)
        
        return result
    return wrapper
```

### 8. 问题处理流程

#### 标准故障排除流程
1. **问题确认**
   - 详细记录错误信息
   - 复现错误步骤
   - 确认环境配置

2. **信息收集**
   - 检查日志文件
   - 确认系统资源
   - 验证网络连接

3. **问题分析**
   - 分析错误堆栈
   - 检查代码逻辑
   - 对比正常情况

4. **解决方案**
   - 制定修复计划
   - 实施解决方案
   - 验证修复效果

5. **文档记录**
   - 记录问题详情
   - 记录解决过程
   - 更新故障排除手册

### 9. 预防措施

#### 代码质量保证
- 使用类型检查
- 编写单元测试
- 代码审查
- 持续集成

#### 监控和告警
- 系统监控
- 错误告警
- 性能监控
- 用户反馈

#### 备份和恢复
- 定期数据备份
- 代码版本控制
- 配置管理
- 灾难恢复计划

遵循此问题解决规范，快速定位和解决系统问题，确保开发效率。
